﻿using Binit.Framework.Helpers.Hateoas;
using Domain.Entities.Model;
using System;

namespace Domain.Logic.DTOs.SampleCageReportDTOs
{
    public class SampleCageReportItem : HateoasItem
    {
        #region Properties
        public Guid Id { get; set; }
        public DateTime Date { get; set; }
        public string HenBatchName { get; set; }
        public decimal AvgEggWeight { get; set; }
        public decimal AvgFemaleBirdWeight { get; set; }
        public decimal AvgMaleBirdWeight { get; set; }
        public decimal VariationCoefficientFemale { get; set; }
        public decimal VariationCoefficientMale { get; set; }
        public decimal UniformityFemale { get; set; }
        public decimal UniformityMale { get; set; }

        public override string UpdateURL => $"/sample-cage-report/{Id}";

        public override string DeleteURL => $"/sample-cage-report/{Id}";
        #endregion

        #region Constructors
        public SampleCageReportItem() { }

        public SampleCageReportItem(SampleCageReport sampleCageReport)
        {
            Id = sampleCageReport.Id;
            Date = sampleCageReport.Date;
            HenBatchName = sampleCageReport.HenBatch.ToString();
            AvgEggWeight = sampleCageReport.AvgEggWeight;
            AvgFemaleBirdWeight = sampleCageReport.AvgFemaleBirdWeight;
            AvgMaleBirdWeight = sampleCageReport.AvgMaleBirdWeight;
            VariationCoefficientFemale = sampleCageReport.AvgVariationCoefficientFemale;
            VariationCoefficientMale = sampleCageReport.AvgVariationCoefficientMale;
            UniformityFemale = sampleCageReport.AvgUniformityFemale;
            UniformityMale = sampleCageReport.AvgUniformityMale;
        }
        #endregion

    }
}
