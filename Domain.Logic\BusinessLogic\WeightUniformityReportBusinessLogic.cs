﻿using Domain.Logic.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Binit.Framework;
using Microsoft.Extensions.Localization;
using Binit.Framework.ExceptionHandling.Types;
using Domain.Entities.Model;

using DevExpress.Data.ODataLinq.Helpers;
using Binit.Framework.Helpers.Excel;
using OfficeOpenXml.Style;
using System.Drawing;
using System.IO;
using OfficeOpenXml;
using Domain.Logic.BusinessLogic.DTOs;
using DevExpress.Utils;

namespace Domain.Logic.BusinessLogic
{
    public class WeightUniformityReportBusinessLogic : IWeightUniformityReportBusinessLogic
    {
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly ISampleCageReportService sampleCageReportService;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly IHenBatchService henBatchService;
        private readonly IStringLocalizer<SharedResources> localizer;

        public WeightUniformityReportBusinessLogic(
            IHenBatchPerformanceService henBatchPerformanceService,
            ISampleCageReportService sampleCageReportService,
            IGeneticsParameterService geneticsParameterService,
            IHenBatchService henBatchService,
            IStringLocalizer<SharedResources> localizer)
        {
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.sampleCageReportService = sampleCageReportService;
            this.geneticsParameterService = geneticsParameterService;
            this.henBatchService = henBatchService;
            this.localizer = localizer;
        }

        public ExportResult GenerateReportInExcel(WeightUniformityReportFilterDTO filters)
        {
            WeightUniformityReportDTO report = GetReport(filters);
            return ExportExcel(report);
        }

        private WeightUniformityReportDTO GetReport(WeightUniformityReportFilterDTO filters)
        {
            var parentHenBatch = henBatchService.GetAll()
              .AsNoTracking()
              .Include(h => h.Farm)
              .FirstOrDefault(h => h.Id == filters.ParentHenBatch);

            if (parentHenBatch == null)
            {
                throw new NotFoundException(localizer["Nenhum lote encontrado"]);
            }

            WeightUniformityReportDTO report = new WeightUniformityReportDTO()
            {
                Farm = parentHenBatch.Farm?.Name,
                Batch = parentHenBatch.Code,
                HenReportCreationMinDate = parentHenBatch.ReportingStartDate,
                DetailedWeightComparison = GetDetailedWeightReport(parentHenBatch, filters.HenStage)
            };

            return report;
        }

        public List<WeightComparisonReportDTO> GetDetailedWeightReport(HenBatch parentHenBatch, HenStage henStage)
        {
            var result = new List<WeightComparisonReportDTO>();

            // Get all child batches
            var childHenBatches = henBatchService.GetAll()
                .AsNoTracking()
                .Where(h => h.ParentId == parentHenBatch.Id)
                .Select(h => new
                {
                    h.Id,
                    h.GeneticId
                })
                .ToList();

            // Get all genetic parameters for child batches
            var parentHenBatchesGeneticParameters = geneticsParameterService.GetAll()
                .Where(gp => childHenBatches.Select(chb => chb.GeneticId).Contains(gp.GeneticsId))
                .Where(gp => gp.HenStage == henStage)
                .AsNoTracking()
                .ToList();

            // Get all sample cage reports for child batches
            var sampleCageReports = sampleCageReportService.GetAll()
                .Include(scr => scr.SampleCageMeasurement)
                .Include(scr => scr.HenBatch)
                .Include(scr => scr.HenBatch.Line)
                .Include(scr => scr.HenBatch.Line.Warehouse)
                .Include(scr => scr.HenBatch.Category)
                .Include(scr => scr.HenBatchPerformance)
                .Where(scr => childHenBatches.Select(chb => chb.Id).Contains(scr.HenBatchId))
                .Where(scr => scr.HenBatch.HenStage == henStage)
                .Where(scr => scr.HenBatchPerformance.HenAmountMale > 0 || scr.HenBatchPerformance.HenAmountFemale > 0)
                .ToList();

            // Process sample cage reports
            foreach (var report in sampleCageReports)
            {
                var henBatch = report.HenBatch;
                var performance = report.HenBatchPerformance;

                var standardWeight = parentHenBatchesGeneticParameters
                    .Where(gp => gp.TimePeriodValue == performance.WeekNumber && gp.GeneticsId == henBatch.GeneticId)
                    .Select(gp => new
                    {
                        Male = gp.WeightMale * 1000,
                        Female = gp.WeightFemale * 1000
                    })
                    .FirstOrDefault();

                var maleWeight = report.AvgMaleBirdWeight * 1000;
                var femaleWeight = report.AvgFemaleBirdWeight * 1000;

                result.Add(new WeightComparisonReportDTO
                {
                    Id = henBatch.Id,
                    PerformanceGender = report.HenBatchPerformance.HenAmountMale > 0 ? "M" : "F",
                    Farm = parentHenBatch.Farm?.Name,
                    ParentBatch = parentHenBatch.Code,
                    ChildBatch = henBatch.Code,
                    Week = performance.WeekNumber,
                    Date = report.Date,
                    UniformityMale = Math.Round(report.SampleCageMeasurement?.Where(c => c.UniformityMale > 0)?.Average(c => c.UniformityMale) ?? 0, 2),
                    UniformityFemale = Math.Round(report.SampleCageMeasurement?.Where(c => c.UniformityFemale > 0)?.Average(c => c.UniformityFemale) ?? 0, 2),
                    StandardWeightMale = standardWeight.Male,
                    StandardWeightFemale = standardWeight.Female,
                    ActualWeightMale = maleWeight,
                    ActualWeightFemale = femaleWeight,
                    WeightDifferenceMale = maleWeight - standardWeight.Male,
                    WeightDifferenceFemale = femaleWeight - standardWeight.Female,
                    WeightPercentageMale = maleWeight > 0 && standardWeight.Male > 0 ? Math.Round((maleWeight - standardWeight.Male) / standardWeight.Male * 100, 2) : 0,
                    WeightPercentageFemale = femaleWeight > 0 && standardWeight.Female > 0 ? Math.Round((femaleWeight - standardWeight.Female) / standardWeight.Female * 100, 2) : 0,
                    LineName = henBatch.Line.Name, // Box
                    WarehouseName = henBatch.Line.Warehouse.Name, //Aviário
                    CategoryName = henBatch.Category.Name, // Genética
                    DeadAccumulatedFemale = henBatch.DeadAccumulatedFemale,
                    DeadAccumulatedMale = henBatch.DeadAccumulatedMale,
                    InitialHenAmountMale = henBatch.InitialHenAmountMale,
                    InitialHenAmountFemale = henBatch.InitialHenAmountFemale,
                });
            }

            return result;
        }

        public WeightUniformityReportDTO GetReportData(WeightUniformityReportFilterDTO filters)
        {
            var report = GetReport(filters);
            return report;
        }

        private ExportResult ExportExcel(WeightUniformityReportDTO report)
        {
            MemoryStream stream = new MemoryStream();
            MemoryStream output = new MemoryStream();

            using (ExcelPackage package = new ExcelPackage(stream))
            {
                ExcelWorksheet workSheetFemale = package.Workbook.Worksheets.Add("FÊMEAS");
                ExcelWorksheet workSheetMale = package.Workbook.Worksheets.Add("MACHOS");
                int initialHeaderRow = 4;

                #region Filtro performances por genero
                var malePerformances = report.DetailedWeightComparison
                    .Where(r => r.PerformanceGender == "M")
                    .ToList();

                var femalePerformances = report.DetailedWeightComparison
                    .Where(r => r.PerformanceGender == "F")
                    .ToList();
                #endregion

                #region Worksheet Male
                workSheetMale.Cells[2, 2, 2, 26].Style.Fill.PatternType = ExcelFillStyle.Solid;
                workSheetMale.Cells[2, 2, 2, 26].Style.Fill.BackgroundColor.SetColor(Color.Orange);
                workSheetMale.Cells[2, 2, 2, 26].Style.Font.Bold = true;

                workSheetMale.Cells[2, 2].Value = "Geral do lote:";
                workSheetMale.Cells[2, 2, 2, 3].Merge = true;
                workSheetMale.Cells[2, 4].Value = report.Batch;

                workSheetMale.Cells[2, 7].Value = "Data do alojamento:";
                workSheetMale.Cells[2, 7, 2, 9].Merge = true;
                workSheetMale.Cells[2, 10].Value = report.HenReportCreationMinDate;
                workSheetMale.Cells[2, 10].Style.Numberformat.Format = "dd/MM/yyyy";
                workSheetMale.Cells[2, 10, 2, 11].Merge = true;

                workSheetMale.Cells[2, 13].Value = "Aves alojadas:";
                workSheetMale.Cells[2, 13].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheetMale.Cells[2, 13, 2, 14].Merge = true;
                workSheetMale.Cells[2, 15].Value = report.DetailedWeightComparison
                    .GroupBy(x => x.Id)
                    .Select(g => g.First())
                    .Sum(x => x.InitialHenAmountMale);

                workSheetMale.Cells[2, 16].Value = "Saldo atual:";
                workSheetMale.Cells[2, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheetMale.Cells[2, 16, 2, 17].Merge = true;
                workSheetMale.Cells[2, 18].Value = report.DetailedWeightComparison
                    .GroupBy(x => x.Id)
                    .Select(g => g.First())
                    .Sum(x => x.InitialHenAmountMale - x.DeadAccumulatedMale);

                workSheetMale.Cells[4, 2].Value = "Data";
                workSheetMale.Cells[4, 2, 6, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheetMale.Cells[4, 2, 6, 3].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                workSheetMale.Cells[4, 2, 6, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
                workSheetMale.Cells[4, 2, 6, 3].Style.Fill.BackgroundColor.SetColor(Color.Orange);
                workSheetMale.Cells[4, 2, 6, 3].Style.Font.Bold = true;
                workSheetMale.Cells[4, 2, 6, 3].Merge = true;
                workSheetMale.Cells[4, 2, 6, 3].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheetMale.Cells[4, 2, 6, 3].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                workSheetMale.Cells[4, 2, 6, 3].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheetMale.Cells[4, 2, 6, 3].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                workSheetMale.Cells[4, 4].Value = "Idade";
                workSheetMale.Cells[4, 4, 6, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheetMale.Cells[4, 4, 6, 4].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                workSheetMale.Cells[4, 4, 6, 4].Style.Fill.PatternType = ExcelFillStyle.Solid;
                workSheetMale.Cells[4, 4, 6, 4].Style.Fill.BackgroundColor.SetColor(Color.Red);
                workSheetMale.Cells[4, 4, 6, 4].Style.Font.Color.SetColor(Color.White);
                workSheetMale.Cells[4, 4, 6, 4].Style.Font.Bold = true;
                workSheetMale.Cells[4, 4, 6, 4].Style.TextRotation = 90; // Inverte o texto para vertical
                workSheetMale.Cells[4, 4, 6, 4].Merge = true;
                workSheetMale.Cells[4, 4, 6, 4].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheetMale.Cells[4, 4, 6, 4].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                workSheetMale.Cells[4, 4, 6, 4].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheetMale.Cells[4, 4, 6, 4].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                Dictionary<string, int> warehouseMaleLineColumnMap = new Dictionary<string, int>();
                int initialOrderingMaleHeaderColumn = 5;
                var maleWeekMap = malePerformances
                    .Where(r => r.PerformanceGender == "M" && r.Date.HasValue)
                    .Select(r => new { r.Date, r.Week })
                    .Distinct()
                    .OrderBy(w => w.Date)
                    .ThenBy(w => w.Week)
                    .Select((entry, index) => new { entry.Date, entry.Week, Row = index + 7 })
                    .ToDictionary(x => Tuple.Create(x.Date, x.Week), x => x.Row);



                #region Male Date/Week Side Header
                foreach (var weekEntry in maleWeekMap)
                {
                    // Data Machos
                    workSheetMale.Cells[weekEntry.Value, 2].Value = weekEntry.Key.Item1.HasValue
                        ? weekEntry.Key.Item1
                        : (object)"-";
                    workSheetMale.Cells[weekEntry.Value, 2].Style.Numberformat.Format = "dd/MM/yyyy";
                    workSheetMale.Cells[weekEntry.Value, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetMale.Cells[weekEntry.Value, 2].Style.Fill.BackgroundColor.SetColor(Color.Yellow);
                    workSheetMale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Merge = true;
                    workSheetMale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Font.Bold = true;
                    workSheetMale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    // Semana Machos
                    workSheetMale.Cells[weekEntry.Value, 4].Value = weekEntry.Key.Item2;
                    workSheetMale.Cells[weekEntry.Value, 4].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetMale.Cells[weekEntry.Value, 4].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(120, 204, 253));
                    workSheetMale.Cells[weekEntry.Value, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[weekEntry.Value, 4].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[weekEntry.Value, 4].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[weekEntry.Value, 4].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[weekEntry.Value, 4].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[weekEntry.Value, 4].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }
                #endregion

                var malePerformance = malePerformances
                    .Where(p => p.Date.HasValue)
                    .GroupBy(p => new { p.WarehouseName, p.LineName, p.Date })
                    .Select(g => new WeightComparisonReportDTO
                    {
                        WarehouseName = g.Key.WarehouseName,
                        LineName = g.Key.LineName,
                        Week = g.First().Week,
                        ActualWeightMale = g.Count() > 1
                            ? Math.Round(g.Average(p => p.ActualWeightMale), 2)
                            : g.First().ActualWeightMale,
                        WeightPercentageMale = g.Count() > 1
                            ? Math.Round(((g.Average(p => p.ActualWeightMale) - g.First().StandardWeightMale) / g.First().StandardWeightMale * 100), 2)
                            : g.First().WeightPercentageMale,
                        UniformityMale = g.Count() > 1
                            ? Math.Round(g.Average(p => p.UniformityMale), 2)
                            : g.First().UniformityMale,
                        Date = g.Key.Date,
                        StandardWeightMale = g.First().StandardWeightMale,
                        // Manter os demais campos originais
                        Id = g.First().Id,
                        PerformanceGender = "M",
                        Farm = g.First().Farm,
                        ParentBatch = g.First().ParentBatch,
                        ChildBatch = g.First().ChildBatch,
                        CategoryName = g.First().CategoryName,
                        DeadAccumulatedFemale = g.First().DeadAccumulatedFemale,
                        DeadAccumulatedMale = g.First().DeadAccumulatedMale,
                        InitialHenAmountMale = g.First().InitialHenAmountMale,
                        InitialHenAmountFemale = g.First().InitialHenAmountFemale
                    })
                    .ToList();

                var orderedMalePerformances = malePerformance
                    .OrderBy(p => p.WarehouseName)
                    .ThenBy(p => p.LineName, new NaturalStringComparer())
                    .ToList();

                foreach (var performance in orderedMalePerformances)
                {
                    string key = $"{performance.WarehouseName} - {performance.LineName}";

                    // Se a combinação ainda não foi mapeada, adicionar ao Dictionary
                    if (!warehouseMaleLineColumnMap.ContainsKey(key))
                    {
                        warehouseMaleLineColumnMap[key] = initialOrderingMaleHeaderColumn;
                        initialOrderingMaleHeaderColumn += 3; // Avança 3 colunas para o próximo Aviário + Box
                    }
                }

                #region Put 0 in cells
                foreach (var weekEntry in maleWeekMap)
                {
                    foreach (var warehouseEntry in warehouseMaleLineColumnMap)
                    {
                        int column = warehouseEntry.Value;
                        int row = weekEntry.Value;

                        // Preencher as 3 colunas (Peso, %, Unif.) com 0
                        workSheetMale.Cells[row, column].Value = 0;
                        workSheetMale.Cells[row, column].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheetMale.Cells[row, column].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        workSheetMale.Cells[row, column].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheetMale.Cells[row, column].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheetMale.Cells[row, column].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheetMale.Cells[row, column].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                        workSheetMale.Cells[row, column + 1].Value = 0;
                        workSheetMale.Cells[row, column + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        workSheetMale.Cells[row, column + 1].Style.Fill.BackgroundColor.SetColor(Color.Yellow);
                        workSheetMale.Cells[row, column + 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheetMale.Cells[row, column + 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        workSheetMale.Cells[row, column + 1].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheetMale.Cells[row, column + 1].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheetMale.Cells[row, column + 1].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheetMale.Cells[row, column + 1].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                        workSheetMale.Cells[row, column + 2].Value = 0;
                        workSheetMale.Cells[row, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheetMale.Cells[row, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        workSheetMale.Cells[row, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheetMale.Cells[row, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheetMale.Cells[row, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheetMale.Cells[row, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    }
                }
                #endregion

                foreach (var performance in malePerformance)
                {
                    string key = $"{performance.WarehouseName} - {performance.LineName}";

                    // Verificar se a chave existe no mapeamento de colunas
                    int column = warehouseMaleLineColumnMap[key];
                    if (!warehouseMaleLineColumnMap.TryGetValue(key, out column))
                    {
                        continue;
                    }

                    // Verificar se a combinação de data e semana existe no mapeamento de linhas
                    int mappedRow = maleWeekMap[Tuple.Create(performance.Date, performance.Week)];
                    var weekKey = Tuple.Create(performance.Date, performance.Week);
                    if (!maleWeekMap.TryGetValue(weekKey, out mappedRow))
                    {
                        continue;
                    }

                    workSheetMale.Cells[initialHeaderRow, column].Value = performance.WarehouseName + " - " + performance.LineName;
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Merge = true;
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Font.Color.SetColor(Color.White);
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Font.Bold = true;
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    //Genética
                    workSheetMale.Cells[initialHeaderRow + 1, column].Value = performance.CategoryName;
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Merge = true;
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Font.Color.SetColor(Color.White);
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Font.Bold = true;
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    //Peso, % e Unif. Estático
                    workSheetMale.Cells[initialHeaderRow + 2, column].Value = "Peso";
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.Font.Color.SetColor(Color.White);
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.Font.Bold = true;
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 2, column].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Value = "%";
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.Font.Color.SetColor(Color.White);
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.Font.Bold = true;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 1].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Value = "Unif.";
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.Font.Color.SetColor(Color.White);
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.Font.Bold = true;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[initialHeaderRow + 2, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    // Plotar os dados na célula correta
                    workSheetMale.Cells[mappedRow, column].Value = performance.ActualWeightMale;
                    workSheetMale.Cells[mappedRow, column].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[mappedRow, column].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[mappedRow, column].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[mappedRow, column].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[mappedRow, column].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[mappedRow, column].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    workSheetMale.Cells[mappedRow, column + 1].Value = performance.WeightPercentageMale;
                    workSheetMale.Cells[mappedRow, column + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetMale.Cells[mappedRow, column + 1].Style.Fill.BackgroundColor.SetColor(Color.Yellow);
                    workSheetMale.Cells[mappedRow, column + 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[mappedRow, column + 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[mappedRow, column + 1].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[mappedRow, column + 1].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[mappedRow, column + 1].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[mappedRow, column + 1].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    workSheetMale.Cells[mappedRow, column + 2].Value = performance.UniformityMale;
                    workSheetMale.Cells[mappedRow, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetMale.Cells[mappedRow, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetMale.Cells[mappedRow, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[mappedRow, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[mappedRow, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetMale.Cells[mappedRow, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }

                #endregion //WorksheetMale --------------------------------------------------------

                #region Worksheet Female
                workSheetFemale.Cells[2, 2, 2, 26].Style.Fill.PatternType = ExcelFillStyle.Solid;
                workSheetFemale.Cells[2, 2, 2, 26].Style.Fill.BackgroundColor.SetColor(Color.Orange);
                workSheetFemale.Cells[2, 2, 2, 26].Style.Font.Bold = true;

                workSheetFemale.Cells[2, 2].Value = "Geral do lote:";
                workSheetFemale.Cells[2, 2, 2, 3].Merge = true;
                workSheetFemale.Cells[2, 4].Value = report.Batch;

                workSheetFemale.Cells[2, 7].Value = "Data do alojamento:";
                workSheetFemale.Cells[2, 7, 2, 9].Merge = true;
                workSheetFemale.Cells[2, 10].Value = report.HenReportCreationMinDate;
                workSheetFemale.Cells[2, 10].Style.Numberformat.Format = "dd/MM/yyyy";
                workSheetFemale.Cells[2, 10, 2, 11].Merge = true;

                workSheetFemale.Cells[2, 13].Value = "Aves alojadas:";
                workSheetFemale.Cells[2, 13].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheetFemale.Cells[2, 13, 2, 14].Merge = true;
                workSheetFemale.Cells[2, 15].Value = report.DetailedWeightComparison
                    .GroupBy(x => x.Id)
                    .Select(g => g.First())
                    .Sum(x => x.InitialHenAmountFemale);

                workSheetFemale.Cells[2, 16].Value = "Saldo atual:";
                workSheetFemale.Cells[2, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheetFemale.Cells[2, 16, 2, 17].Merge = true;
                workSheetFemale.Cells[2, 18].Value = report.DetailedWeightComparison
                    .GroupBy(x => x.Id)
                    .Select(g => g.First())
                    .Sum(x => x.InitialHenAmountFemale - x.DeadAccumulatedFemale);

                workSheetFemale.Cells[4, 2].Value = "Data";
                workSheetFemale.Cells[4, 2, 6, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheetFemale.Cells[4, 2, 6, 3].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                workSheetFemale.Cells[4, 2, 6, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
                workSheetFemale.Cells[4, 2, 6, 3].Style.Fill.BackgroundColor.SetColor(Color.Orange);
                workSheetFemale.Cells[4, 2, 6, 3].Style.Font.Bold = true;
                workSheetFemale.Cells[4, 2, 6, 3].Merge = true;
                workSheetFemale.Cells[4, 2, 6, 3].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheetFemale.Cells[4, 2, 6, 3].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                workSheetFemale.Cells[4, 2, 6, 3].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheetFemale.Cells[4, 2, 6, 3].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                workSheetFemale.Cells[4, 4].Value = "Idade";
                workSheetFemale.Cells[4, 4, 6, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheetFemale.Cells[4, 4, 6, 4].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                workSheetFemale.Cells[4, 4, 6, 4].Style.Fill.PatternType = ExcelFillStyle.Solid;
                workSheetFemale.Cells[4, 4, 6, 4].Style.Fill.BackgroundColor.SetColor(Color.Red);
                workSheetFemale.Cells[4, 4, 6, 4].Style.Font.Color.SetColor(Color.White);
                workSheetFemale.Cells[4, 4, 6, 4].Style.Font.Bold = true;
                workSheetFemale.Cells[4, 4, 6, 4].Style.TextRotation = 90; // Inverte o texto para vertical
                workSheetFemale.Cells[4, 4, 6, 4].Merge = true;
                workSheetFemale.Cells[4, 4, 6, 4].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheetFemale.Cells[4, 4, 6, 4].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                workSheetFemale.Cells[4, 4, 6, 4].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheetFemale.Cells[4, 4, 6, 4].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                Dictionary<string, int> warehouseFemaleLineColumnMap = new Dictionary<string, int>();
                int initialOrderingFemaleHeaderColumn = 5;
                var femaleWeekMap = femalePerformances
                    .Where(r => r.PerformanceGender == "F" && r.Date.HasValue)
                    .Select(r => new { r.Date, r.Week })
                    .Distinct()
                    .OrderBy(w => w.Date)
                    .ThenBy(w => w.Week)
                    .Select((entry, index) => new { entry.Date, entry.Week, Row = index + 7 })
                    .ToDictionary(x => Tuple.Create(x.Date, x.Week), x => x.Row);



                #region Female Date/Week Side Header
                foreach (var weekEntry in femaleWeekMap)
                {
                    // Data Fêmeas
                    workSheetFemale.Cells[weekEntry.Value, 2].Value = weekEntry.Key.Item1.HasValue
                        ? weekEntry.Key.Item1
                        : (object)"-";
                    workSheetFemale.Cells[weekEntry.Value, 2].Style.Numberformat.Format = "dd/MM/yyyy";
                    workSheetFemale.Cells[weekEntry.Value, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetFemale.Cells[weekEntry.Value, 2].Style.Fill.BackgroundColor.SetColor(Color.Yellow);
                    workSheetFemale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Merge = true;
                    workSheetFemale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Font.Bold = true;
                    workSheetFemale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[weekEntry.Value, 2, weekEntry.Value, 3].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    // Semana Fêmeas
                    workSheetFemale.Cells[weekEntry.Value, 4].Value = weekEntry.Key.Item2;
                    workSheetFemale.Cells[weekEntry.Value, 4].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetFemale.Cells[weekEntry.Value, 4].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(120, 204, 253));
                    workSheetFemale.Cells[weekEntry.Value, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[weekEntry.Value, 4].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[weekEntry.Value, 4].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[weekEntry.Value, 4].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[weekEntry.Value, 4].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[weekEntry.Value, 4].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }
                #endregion

                var femalePerformance = femalePerformances
                    .Where(p => p.Date.HasValue)
                    .GroupBy(p => new { p.WarehouseName, p.LineName, p.Date })
                    .Select(g => new WeightComparisonReportDTO
                    {
                        WarehouseName = g.Key.WarehouseName,
                        LineName = g.Key.LineName,
                        Week = g.First().Week,
                        ActualWeightFemale = g.Count() > 1
                            ? Math.Round(g.Average(p => p.ActualWeightFemale), 2)
                            : g.First().ActualWeightFemale,
                        WeightPercentageFemale = g.Count() > 1
                            ? Math.Round(((g.Average(p => p.ActualWeightFemale) - g.First().StandardWeightFemale) / g.First().StandardWeightFemale * 100), 2)
                            : g.First().WeightPercentageFemale,
                        UniformityFemale = g.Count() > 1
                            ? Math.Round(g.Average(p => p.UniformityFemale), 2)
                            : g.First().UniformityFemale,
                        Date = g.Key.Date,
                        StandardWeightFemale = g.First().StandardWeightFemale,
                        // Manter os demais campos originais
                        Id = g.First().Id,
                        PerformanceGender = "F",
                        Farm = g.First().Farm,
                        ParentBatch = g.First().ParentBatch,
                        ChildBatch = g.First().ChildBatch,
                        CategoryName = g.First().CategoryName,
                        DeadAccumulatedFemale = g.First().DeadAccumulatedFemale,
                        DeadAccumulatedMale = g.First().DeadAccumulatedMale,
                        InitialHenAmountMale = g.First().InitialHenAmountMale,
                        InitialHenAmountFemale = g.First().InitialHenAmountFemale
                    })
                    .ToList();

                var orderedFemalePerformances = femalePerformance
                    .OrderBy(p => p.WarehouseName)
                    .ThenBy(p => p.LineName, new NaturalStringComparer())
                    .ToList();

                foreach (var performance in orderedFemalePerformances)
                {
                    string key = $"{performance.WarehouseName} - {performance.LineName}";

                    // Se a combinação ainda não foi mapeada, adicionar ao Dictionary
                    if (!warehouseFemaleLineColumnMap.ContainsKey(key))
                    {
                        warehouseFemaleLineColumnMap[key] = initialOrderingFemaleHeaderColumn;
                        initialOrderingFemaleHeaderColumn += 3; // Avança 3 colunas para o próximo Aviário + Box
                    }
                }

                #region Put 0 in cells
                foreach (var weekEntry in femaleWeekMap)
                {
                    foreach (var warehouseEntry in warehouseFemaleLineColumnMap)
                    {
                        int column = warehouseEntry.Value;
                        int row = weekEntry.Value;

                        // Preencher as 3 colunas (Peso, %, Unif.) com 0
                        workSheetFemale.Cells[row, column].Value = 0;
                        workSheetFemale.Cells[row, column].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheetFemale.Cells[row, column].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        workSheetFemale.Cells[row, column].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheetFemale.Cells[row, column].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheetFemale.Cells[row, column].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheetFemale.Cells[row, column].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                        workSheetFemale.Cells[row, column + 1].Value = 0;
                        workSheetFemale.Cells[row, column + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        workSheetFemale.Cells[row, column + 1].Style.Fill.BackgroundColor.SetColor(Color.Yellow);
                        workSheetFemale.Cells[row, column + 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheetFemale.Cells[row, column + 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        workSheetFemale.Cells[row, column + 1].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheetFemale.Cells[row, column + 1].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheetFemale.Cells[row, column + 1].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheetFemale.Cells[row, column + 1].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                        workSheetFemale.Cells[row, column + 2].Value = 0;
                        workSheetFemale.Cells[row, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheetFemale.Cells[row, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        workSheetFemale.Cells[row, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheetFemale.Cells[row, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheetFemale.Cells[row, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheetFemale.Cells[row, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    }
                }
                #endregion

                foreach (var performance in femalePerformance)
                {
                    string key = $"{performance.WarehouseName} - {performance.LineName}";

                    // Verificar se a chave existe no mapeamento de colunas
                    int column = warehouseFemaleLineColumnMap[key];
                    if (!warehouseFemaleLineColumnMap.TryGetValue(key, out column))
                    {
                        continue;
                    }

                    // Verificar se a combinação de data e semana existe no mapeamento de linhas
                    int mappedRow = femaleWeekMap[Tuple.Create(performance.Date, performance.Week)];
                    var weekKey = Tuple.Create(performance.Date, performance.Week);
                    if (!femaleWeekMap.TryGetValue(weekKey, out mappedRow))
                    {
                        continue;
                    }

                    workSheetFemale.Cells[initialHeaderRow, column].Value = performance.WarehouseName + " - " + performance.LineName;
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Merge = true;
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Font.Color.SetColor(Color.White);
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Font.Bold = true;
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow, column, initialHeaderRow, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    //Genética
                    workSheetFemale.Cells[initialHeaderRow + 1, column].Value = performance.CategoryName;
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Merge = true;
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Font.Color.SetColor(Color.White);
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Font.Bold = true;
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 1, column, initialHeaderRow + 1, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    //Peso, % e Unif. Estático
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Value = "Peso";
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.Font.Color.SetColor(Color.White);
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.Font.Bold = true;
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 2, column].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Value = "%";
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.Font.Color.SetColor(Color.White);
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.Font.Bold = true;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 1].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Value = "Unif.";
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.Fill.BackgroundColor.SetColor(Color.Red);
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.Font.Color.SetColor(Color.White);
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.Font.Bold = true;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[initialHeaderRow + 2, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    // Plotar os dados na célula correta
                    workSheetFemale.Cells[mappedRow, column].Value = performance.ActualWeightFemale;
                    workSheetFemale.Cells[mappedRow, column].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[mappedRow, column].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[mappedRow, column].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[mappedRow, column].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[mappedRow, column].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[mappedRow, column].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    workSheetFemale.Cells[mappedRow, column + 1].Value = performance.WeightPercentageFemale;
                    workSheetFemale.Cells[mappedRow, column + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    workSheetFemale.Cells[mappedRow, column + 1].Style.Fill.BackgroundColor.SetColor(Color.Yellow);
                    workSheetFemale.Cells[mappedRow, column + 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[mappedRow, column + 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[mappedRow, column + 1].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[mappedRow, column + 1].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[mappedRow, column + 1].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[mappedRow, column + 1].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    workSheetFemale.Cells[mappedRow, column + 2].Value = performance.UniformityFemale;
                    workSheetFemale.Cells[mappedRow, column + 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    workSheetFemale.Cells[mappedRow, column + 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    workSheetFemale.Cells[mappedRow, column + 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[mappedRow, column + 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[mappedRow, column + 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    workSheetFemale.Cells[mappedRow, column + 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }

                #endregion //WorksheetFemale --------------------------------------------------------

                package.SaveAs(output);
            }
            output.Position = 0;

            return new ExportResult(output, "Report.xlsx");
        }
    }
}
