using Binit.Framework.Helpers.Excel;
using Domain.Logic.BusinessLogic.DTOs;
using static Domain.Logic.BusinessLogic.ManagerialLayingReportBusinessLogic;

namespace Domain.Logic.Interfaces
{
    public interface IManagerialLayingReportBusinessLogic
    {
        ExportResult GenerateReportInExcel(ManagerialLayingReportFilterDTO filters);

        ManagerialLayingReportDataDTO GetReportData(ManagerialLayingReportFilterDTO filters);
    }
}
