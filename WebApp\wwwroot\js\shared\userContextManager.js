/**
 * User Context Manager - Handles automatic pre-selection and disabling of filter dropdowns
 * for users with single company/farm access across dashboard and inventory screens.
 */

class UserContextManager {
  constructor() {
    this.userContext = null;
    this.isInitialized = false;
    this.initializationPromise = null;
  }

  /**
   * Initialize the user context by fetching user company/farm information
   * @returns {Promise<Object>} User context object
   */
  async initialize() {
    if (this.isInitialized) {
      return this.userContext;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._fetchUserContext();
    this.userContext = await this.initializationPromise;
    this.isInitialized = true;

    return this.userContext;
  }

  /**
   * Fetch user context from the backend API
   * @private
   * @returns {Promise<Object>} User context data
   */
  async _fetchUserContext() {
    try {
      const response = await fetch(`${location.origin}/Home/GetUserContext`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      console.log("User context loaded:", data);
      return data;
    } catch (error) {
      console.error("Failed to fetch user context:", error);
      // Return default context that allows normal behavior
      return {
        shouldAutoSelect: false,
        companyIds: [],
        farmIds: [],
        singleCompanyId: null,
        singleFarmId: null,
        singleRegionalId: null,
      };
    }
  }

  /**
   * Check if user should have auto-selected filters
   * @returns {boolean} True if user has single company and farm access
   */
  shouldAutoSelectFilters() {
    return this.userContext?.shouldAutoSelect || false;
  }

  /**
   * Get the single company ID for auto-selection
   * @returns {string|null} Company ID or null
   */
  getSingleCompanyId() {
    return this.userContext?.singleCompanyId || null;
  }

  /**
   * Get the single farm ID for auto-selection
   * @returns {string|null} Farm ID or null
   */
  getSingleFarmId() {
    return this.userContext?.singleFarmId || null;
  }

  /**
   * Get the single regional ID for auto-selection
   * @returns {string|null} Regional ID or null
   */
  getSingleRegionalId() {
    return this.userContext?.singleRegionalId || null;
  }

  /**
   * Apply auto-selection and disabling to filter dropdowns
   * @param {Object} config Configuration object with dropdown mappings
   * @param {string} config.regionalDropdown - Selector for regional dropdown
   * @param {string} config.unitDropdown - Selector for unit/company dropdown
   * @param {string} config.productorDropdown - Selector for productor/farm dropdown
   * @param {string} config.supervisorDropdown - Selector for supervisor dropdown
   * @param {string} config.extensionistDropdown - Selector for extensionist dropdown
   * @param {Function} config.onAutoSelection - Callback function called after auto-selection
   */
  async applyAutoSelection(config) {
    await this.initialize();

    if (!this.shouldAutoSelectFilters()) {
      console.log(
        "User has multiple companies/farms - normal dropdown behavior"
      );
      return;
    }

    console.log("Applying auto-selection for single company/farm user");

    const {
      regionalDropdown = "#regionalFilter",
      unitDropdown = "#unitFilter",
      productorDropdown = "#productorFilter",
      supervisorDropdown = "#supervisorFilter",
      extensionistDropdown = "#extensionistFilter",
      onAutoSelection = null,
    } = config;

    try {
      // Auto-select regional dropdown first (if applicable)
      await this._autoSelectRegionalDropdown(regionalDropdown);

      // Auto-select and disable company-related dropdowns
      await this._autoSelectCompanyDropdowns(unitDropdown, productorDropdown);

      // Auto-select and disable user-related dropdowns
      await this._autoSelectUserDropdowns(
        supervisorDropdown,
        extensionistDropdown,
        unitDropdown
      );

      // Call the callback function if provided
      if (onAutoSelection && typeof onAutoSelection === "function") {
        onAutoSelection();
      }

      console.log("Auto-selection completed successfully");
    } catch (error) {
      console.error("Error during auto-selection:", error);
    }
  }

  /**
   * Auto-select regional dropdown if user has single company access
   * @private
   * @param {string} regionalDropdown - Regional dropdown selector
   */
  async _autoSelectRegionalDropdown(regionalDropdown) {
    const regionalId = this.getSingleRegionalId();

    if (!regionalId) {
      return;
    }

    // Auto-select regional dropdown
    const $regionalDropdown = $(regionalDropdown);
    if ($regionalDropdown.length) {
      // Wait for dropdown to be populated
      await this._waitForDropdownOptions($regionalDropdown);

      // Find and select the regional option
      const regionalOption = $regionalDropdown.find(
        `option[value="${regionalId}"]`
      );
      if (regionalOption.length) {
        $regionalDropdown.val(regionalId).trigger("change");
        $regionalDropdown.prop("disabled", true);
        this._addDisabledStyling($regionalDropdown);
        console.log(`Auto-selected regional: ${regionalId}`);

        // Wait for the regional change to trigger unit dropdown population
        await this._delay(500);
      }
    }
  }

  /**
   * Auto-select company-related dropdowns (unit/company and productor/farm)
   * @private
   * @param {string} unitDropdown - Unit dropdown selector
   * @param {string} productorDropdown - Productor dropdown selector
   */
  async _autoSelectCompanyDropdowns(unitDropdown, productorDropdown) {
    const companyId = this.getSingleCompanyId();
    const farmId = this.getSingleFarmId();

    if (!companyId || !farmId) {
      return;
    }

    // Auto-select unit/company dropdown
    const $unitDropdown = $(unitDropdown);
    if ($unitDropdown.length) {
      // Wait for dropdown to be populated (longer timeout after regional selection)
      await this._waitForDropdownOptions($unitDropdown, 3000);

      // Find and select the company option
      const companyOption = $unitDropdown.find(`option[value="${companyId}"]`);
      if (companyOption.length) {
        $unitDropdown.val(companyId).trigger("change");
        $unitDropdown.prop("disabled", true);
        this._addDisabledStyling($unitDropdown);
        console.log(`Auto-selected company: ${companyId}`);

        // Wait for unit change to trigger productor dropdown population
        await this._delay(1000);
      }
    }

    // Auto-select productor/farm dropdown
    const $productorDropdown = $(productorDropdown);
    if ($productorDropdown.length) {
      // Wait for dropdown to be populated (may depend on unit selection)
      await this._waitForDropdownOptions($productorDropdown, 3000);

      // Find and select the farm option
      const farmOption = $productorDropdown.find(`option[value="${farmId}"]`);
      if (farmOption.length) {
        $productorDropdown.val(farmId).trigger("change");
        $productorDropdown.prop("disabled", true);
        this._addDisabledStyling($productorDropdown);
        console.log(`Auto-selected farm: ${farmId}`);

        // Wait for farm change to complete before proceeding to user dropdowns
        await this._delay(500);
      }
    }
  }

  /**
   * Auto-select user-related dropdowns (supervisor and extensionist)
   * @private
   * @param {string} supervisorDropdown - Supervisor dropdown selector
   * @param {string} extensionistDropdown - Extensionist dropdown selector
   * @param {string} unitDropdown - Unit dropdown selector (for context)
   */
  async _autoSelectUserDropdowns(
    supervisorDropdown,
    extensionistDropdown,
    unitDropdown
  ) {
    const companyId = this.getSingleCompanyId();

    if (!companyId) {
      return;
    }

    // Wait for company and farm selections to complete before handling user dropdowns
    await this._delay(1500);

    // Auto-select supervisor if only one option available
    const $supervisorDropdown = $(supervisorDropdown);
    if ($supervisorDropdown.length) {
      await this._waitForDropdownOptions($supervisorDropdown, 3000);
      await this._autoSelectSingleOption($supervisorDropdown, "supervisor");

      // Wait for supervisor selection to complete before extensionist
      await this._delay(500);
    }

    // Auto-select extensionist if only one option available
    const $extensionistDropdown = $(extensionistDropdown);
    if ($extensionistDropdown.length) {
      await this._waitForDropdownOptions($extensionistDropdown, 3000);
      await this._autoSelectSingleOption($extensionistDropdown, "extensionist");
    }
  }

  /**
   * Auto-select dropdown if it has only one non-empty option
   * @private
   * @param {jQuery} $dropdown - jQuery dropdown element
   * @param {string} type - Type of dropdown for logging
   */
  async _autoSelectSingleOption($dropdown, type) {
    const options = $dropdown.find('option[value!=""]');
    if (options.length === 1) {
      const value = options.first().val();
      $dropdown.val(value).trigger("change");
      $dropdown.prop("disabled", true);
      this._addDisabledStyling($dropdown);
      console.log(`Auto-selected ${type}: ${value}`);
    }
  }

  /**
   * Wait for dropdown to have options populated
   * @private
   * @param {jQuery} $dropdown - jQuery dropdown element
   * @param {number} timeout - Maximum wait time in milliseconds
   * @returns {Promise<void>}
   */
  async _waitForDropdownOptions($dropdown, timeout = 5000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const options = $dropdown.find('option[value!=""]');
      if (options.length > 0) {
        return;
      }
      await this._delay(100);
    }

    console.warn(
      `Timeout waiting for dropdown options: ${$dropdown.attr("id")}`
    );
  }

  /**
   * Add visual styling to indicate disabled state
   * @private
   * @param {jQuery} $dropdown - jQuery dropdown element
   */
  _addDisabledStyling($dropdown) {
    $dropdown.css({
      "background-color": "#f8f9fa",
      color: "#6c757d",
      cursor: "not-allowed",
    });

    // Add tooltip to explain why it's disabled
    $dropdown.attr("title", "Auto-selected based on your access permissions");
  }

  /**
   * Utility function to create a delay
   * @private
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise<void>}
   */
  _delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Reset all auto-selections and re-enable dropdowns
   * @param {Object} config Configuration object with dropdown selectors
   */
  resetAutoSelection(config) {
    const {
      regionalDropdown = "#regionalFilter",
      unitDropdown = "#unitFilter",
      productorDropdown = "#productorFilter",
      supervisorDropdown = "#supervisorFilter",
      extensionistDropdown = "#extensionistFilter",
    } = config;

    const dropdowns = [
      regionalDropdown,
      unitDropdown,
      productorDropdown,
      supervisorDropdown,
      extensionistDropdown,
    ];

    dropdowns.forEach((selector) => {
      const $dropdown = $(selector);
      if ($dropdown.length) {
        $dropdown.prop("disabled", false);
        $dropdown.css({
          "background-color": "",
          color: "",
          cursor: "",
        });
        $dropdown.removeAttr("title");
      }
    });

    console.log("Auto-selection reset completed");
  }
}

// Create global instance
window.userContextManager = new UserContextManager();
