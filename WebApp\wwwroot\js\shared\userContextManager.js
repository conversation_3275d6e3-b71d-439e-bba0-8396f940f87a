/**
 * User Context Manager - Handles automatic pre-selection and disabling of filter dropdowns
 * for users with single company/farm access across dashboard and inventory screens.
 *
 * This implementation follows the successful pattern from henReport/createBreeding.js
 * by using existing server-rendered dropdown options instead of additional API calls.
 */

class UserContextManager {
  constructor() {
    // No initialization needed - we work with existing DOM elements
  }

  /**
   * Apply auto-selection to filter dropdowns using the simple pattern from createBreeding.js
   * This checks if dropdowns have only one real option and auto-selects them
   * @param {Object} config Configuration object with dropdown mappings
   */
  async applyAutoSelection(config) {
    const {
      regionalDropdown = "#regionalFilter",
      unitDropdown = "#unitFilter",
      productorDropdown = "#productorFilter",
      supervisorDropdown = "#supervisorFilter",
      extensionistDropdown = "#extensionistFilter",
      onAutoSelection = null,
    } = config;

    console.log("Applying simple auto-selection for single option dropdowns");

    let hasAutoSelected = false;

    // Auto-select dropdowns that have only one real option (following createBreeding.js pattern)
    const dropdowns = [
      { selector: regionalDropdown, name: "regional" },
      { selector: unitDropdown, name: "unit" },
      { selector: productorDropdown, name: "productor" },
      { selector: supervisorDropdown, name: "supervisor" },
      { selector: extensionistDropdown, name: "extensionist" },
    ];

    for (const dropdown of dropdowns) {
      const $dropdown = $(dropdown.selector);
      if ($dropdown.length) {
        // Check if dropdown has only 2 options (empty + 1 real option)
        const options = $dropdown.find("option");
        const nonEmptyOptions = $dropdown.find('option[value!=""]');

        if (options.length === 2 && nonEmptyOptions.length === 1) {
          // Auto-select the single option and disable dropdown
          const singleValue = nonEmptyOptions.first().val();
          $dropdown.val(singleValue).trigger("change");
          $dropdown.prop("disabled", true);
          this._addDisabledStyling($dropdown);
          console.log(`Auto-selected ${dropdown.name}: ${singleValue}`);
          hasAutoSelected = true;

          // Small delay to allow change event to process
          await this._delay(100);
        }
      }
    }

    // Call the callback function if provided and we auto-selected something
    if (
      hasAutoSelected &&
      onAutoSelection &&
      typeof onAutoSelection === "function"
    ) {
      // Delay callback to ensure all cascading changes are complete
      setTimeout(onAutoSelection, 500);
    }

    if (hasAutoSelected) {
      console.log("Simple auto-selection completed successfully");
    } else {
      console.log("No auto-selection needed - user has multiple options");
    }
  }

  /**
   * Add visual styling to indicate disabled state
   * @private
   * @param {jQuery} $dropdown - jQuery dropdown element
   */
  _addDisabledStyling($dropdown) {
    $dropdown.css({
      "background-color": "#f8f9fa",
      color: "#6c757d",
      cursor: "not-allowed",
    });

    // Add tooltip to explain why it's disabled
    $dropdown.attr("title", "Auto-selected based on your access permissions");
  }

  /**
   * Utility function to create a delay
   * @private
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise<void>}
   */
  _delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Reset all auto-selections and re-enable dropdowns
   * @param {Object} config Configuration object with dropdown selectors
   */
  resetAutoSelection(config = {}) {
    const {
      regionalDropdown = "#regionalFilter",
      unitDropdown = "#unitFilter",
      productorDropdown = "#productorFilter",
      supervisorDropdown = "#supervisorFilter",
      extensionistDropdown = "#extensionistFilter",
    } = config;

    const dropdowns = [
      regionalDropdown,
      unitDropdown,
      productorDropdown,
      supervisorDropdown,
      extensionistDropdown,
    ];

    dropdowns.forEach((selector) => {
      const $dropdown = $(selector);
      if ($dropdown.length) {
        $dropdown.prop("disabled", false);
        $dropdown.css({
          "background-color": "",
          color: "",
          cursor: "",
        });
        $dropdown.removeAttr("title");
      }
    });

    console.log("Simple auto-selection reset completed");
  }
}

// Create global instance
window.userContextManager = new UserContextManager();
