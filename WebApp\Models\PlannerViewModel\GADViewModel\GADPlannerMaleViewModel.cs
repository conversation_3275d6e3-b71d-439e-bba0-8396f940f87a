﻿using Binit.Framework;
using Domain.Entities.Model;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System.Collections.Generic;

using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Models.PlannerViewModel.GADPlannerMaleViewModel;

namespace WebApp.Models.PlannerViewModel.GADViewModel
{
    public class GADPlannerMaleViewModel
    {
        public string FarmName { get; set; }
        public string HenbatchCode { get; set; }
        public string ProductionDate { get; set; }
        public string LineNumber { get; set; }
        public string Genetic { get; set; }
        public string AvgBirdWeight { get; set; }
        public string AvgGAD { get; set; }

        public GADPlannerMaleViewModel() { }

        public GADPlannerMaleViewModel(HenBatch henbatch)
        {
            FarmName = $"{henbatch.Farm.Code} | {henbatch.Farm.Name}";
            HenbatchCode = henbatch.Code;
            ProductionDate = henbatch.DateStart.Value.ToString("dd/MM/yyyy");
            LineNumber = henbatch.Line?.Code;
            Genetic = henbatch.Genetic.Name;
        }

        public static List<object[]> GetColumns(IStringLocalizer<SharedResources> localizer)
        {
            return new List<object[]>
            {
                new object[]{ "", "", "", new { label = localizer[Lang.ColumnName1].Value, colspan = 3 }, new { label = localizer[Lang.ColumnName2].Value, colspan = 3 }, "", "", new { label = localizer[Lang.ColumnName3].Value, colspan = 2 } },
                new object[]{ "Id", localizer[Lang.ColumnName4].Value, localizer[Lang.ColumnName5].Value, localizer[Lang.ColumnName11].Value, localizer[Lang.ColumnName10].Value, localizer[Lang.ColumnName7].Value, localizer[Lang.ColumnName11].Value, localizer[Lang.ColumnName6].Value, localizer[Lang.ColumnName10].Value, localizer[Lang.ColumnName9].Value, localizer[Lang.ColumnName8].Value, localizer[Lang.ColumnName10].Value, localizer[Lang.ColumnName11].Value },
            };
        }

        public string Serialize() => JsonConvert.SerializeObject(this);
    }
}
