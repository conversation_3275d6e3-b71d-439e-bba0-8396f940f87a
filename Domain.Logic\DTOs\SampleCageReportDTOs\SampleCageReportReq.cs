using Binit.Framework.Attributes;
using Binit.Framework.Constants.SeedEntities;
using Domain.Entities.Model;
using Domain.Logic.Interfaces;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.DTOs.SampleCageReportDTOs.SampleCageReportReq;

namespace Domain.Logic.DTOs.SampleCageReportDTOs
{
    public class SampleCageReportReq
    {
        #region Properties
        [Required(ErrorMessage = Lang.DateRequired)]
        [FutureDatesNotAllowed(ErrorMessage = Lang.GreaterThanToday)]
        public Guid HenBatchId { get; set; }
        public DateTime Date { get; set; }

        [Required(ErrorMessage = Lang.WarehouseRequired)]
        public Guid WarehouseId { get; set; }

        public List<SampleCageMeasurementDTO> SampleCageMeasurements { get; set; }

        [Range(0, 10000, ErrorMessage = Lang.HenWarehouseEggWeightRangeError)]
        public decimal? HenWarehouseEggWeight { get; set; }

        [Range(0, 10000, ErrorMessage = Lang.HenWarehouseAvgFemaleBirdWeightRangeError)]
        public decimal? HenWarehouseAvgFemaleBirdWeight { get; set; }

        [Range(0, 10000, ErrorMessage = Lang.HenWarehouseAvgMaleBirdWeightRangeError)]
        public decimal? HenWarehouseAvgMaleBirdWeight { get; set; }

        public Guid? HenWarehouseFemaleBirdWeightMeasureId { get; set; }

        public Guid? HenWarehouseMaleBirdWeightMeasureId { get; set; }

        [Range(0, 100, ErrorMessage = Lang.HenWarehouseVariationCoefficientFemaleRangeError)]
        public decimal? HenWarehouseVariationCoefficientFemale { get; set; }

        [Range(0, 100, ErrorMessage = Lang.HenWarehouseVariationCoefficientMaleRangeError)]
        public decimal? HenWarehouseVariationCoefficientMale { get; set; }

        [Range(0, 100, ErrorMessage = Lang.HenWarehouseUniformityFemaleRangeError)]
        public decimal? HenWarehouseUniformityFemale { get; set; }

        [Range(0, 100, ErrorMessage = Lang.HenWarehouseUniformityMaleRangeError)]
        public decimal? HenWarehouseUniformityMale { get; set; }

        #endregion

        #region Constructors

        public SampleCageReportReq()
        {
            SampleCageMeasurements = new List<SampleCageMeasurementDTO>();
        }

        public List<SampleCageReport> ToEntity(ICapacityUnitBusinessLogic capacityUnitBusinessLogic)
        {
            var sampleCageReports = new List<SampleCageReport>();

            decimal henWarehouseUniformityFemale = SampleCageMeasurements
                .Where(s => s.UniformityFemale > 0)
                .Select(s => s.UniformityFemale.Value)
                .DefaultIfEmpty(0)
                .Average();

            decimal henWarehouseUniformityMale = SampleCageMeasurements
                .Where(s => s.UniformityMale > 0)
                .Select(s => s.UniformityMale.Value)
                .DefaultIfEmpty(0)
                .Average();

            decimal henWarehouseVariationCoefficientFemale = SampleCageMeasurements
                .Where(s => s.VariationCoefficientFemale > 0)
                .Select(s => s.VariationCoefficientFemale.Value)
                .DefaultIfEmpty(0)
                .Average();

            decimal henWarehouseVariationCoefficientMale = SampleCageMeasurements
                .Where(s => s.VariationCoefficientMale > 0)
                .Select(s => s.VariationCoefficientMale.Value)
                .DefaultIfEmpty(0)
                .Average();

            decimal henWarehouseAvgFemaleBirdWeight = SampleCageMeasurements
                .Where(s => s.AvgFemaleBirdWeight > 0)
                .Select(s => s.AvgFemaleBirdWeight)
                .DefaultIfEmpty(0)
                .Average() / 1000M;

            decimal henWarehouseAvgMaleBirdWeight = SampleCageMeasurements
                .Where(s => s.AvgMaleBirdWeight > 0)
                .Select(s => s.AvgMaleBirdWeight)
                .DefaultIfEmpty(0)
                .Average() / 1000M;

            foreach (var group in SampleCageMeasurements.GroupBy(m => m.HenBatchId))
            {
                var report = new SampleCageReport
                {
                    Id = Guid.NewGuid(),
                    Date = this.Date,
                    HenBatchId = group.Key,
                    HenWarehouseUniformityFemale = this.HenWarehouseUniformityFemale ?? henWarehouseUniformityFemale,
                    HenWarehouseUniformityMale = this.HenWarehouseUniformityMale ?? henWarehouseUniformityMale,
                    HenWarehouseVariationCoefficientFemale = this.HenWarehouseVariationCoefficientFemale ?? henWarehouseVariationCoefficientFemale,
                    HenWarehouseVariationCoefficientMale = this.HenWarehouseVariationCoefficientMale ?? henWarehouseVariationCoefficientMale,
                    HenWarehouseAvgFemaleBirdWeight = this.HenWarehouseAvgFemaleBirdWeight,
                    HenWarehouseAvgMaleBirdWeight = this.HenWarehouseAvgMaleBirdWeight,
                    AvgEggWeight = this.HenWarehouseEggWeight ?? 0,

                    AvgFemaleBirdWeight = group
                        .Where(m => m.AvgFemaleBirdWeight > 0)
                        .Select(m => m.AvgFemaleBirdWeight)
                        .DefaultIfEmpty(0)
                        .Average() / 1000M,

                    AvgMaleBirdWeight = group
                        .Where(m => m.AvgMaleBirdWeight > 0)
                        .Select(m => m.AvgMaleBirdWeight)
                        .DefaultIfEmpty(0)
                        .Average() / 1000M,

                    AvgVariationCoefficientFemale = group
                        .Where(m => m.VariationCoefficientFemale > 0)
                        .Select(m => m.VariationCoefficientFemale.Value)
                        .DefaultIfEmpty(0)
                        .Average(),

                    AvgVariationCoefficientMale = group
                        .Where(m => m.VariationCoefficientMale > 0)
                        .Select(m => m.VariationCoefficientMale.Value)
                        .DefaultIfEmpty(0)
                        .Average(),

                    AvgUniformityFemale = group
                        .Where(m => m.UniformityFemale > 0)
                        .Select(m => m.UniformityFemale.Value)
                        .DefaultIfEmpty(0)
                        .Average(),

                    AvgUniformityMale = group
                        .Where(m => m.UniformityMale > 0)
                        .Select(m => m.UniformityMale.Value)
                        .DefaultIfEmpty(0)
                        .Average()
                };

                report.SampleCageMeasurement = group
                    .Select(m => new SampleCageMeasurement
                    {
                        Id = Guid.NewGuid(),
                        SampleCageReportId = report.Id,
                        SampleCageId = m.SampleCageId,
                        AvgFemaleBirdWeight = m.AvgFemaleBirdWeight > 0 ? m.AvgFemaleBirdWeight / 1000M : 0,
                        AvgMaleBirdWeight = m.AvgMaleBirdWeight > 0 ? m.AvgMaleBirdWeight / 1000M : 0,
                        FemaleBirdWeightCapacityUnitId = m.FemaleBirdWeightMeasureId ?? CapacityUnits.Grams,
                        MaleBirdWeightCapacityUnitId = m.MaleBirdWeightMeasureId ?? CapacityUnits.Grams,
                        VariationCoefficientFemale = m.VariationCoefficientFemale,
                        VariationCoefficientMale = m.VariationCoefficientMale,
                        UniformityFemale = m.UniformityFemale,
                        UniformityMale = m.UniformityMale
                    })
                    .ToList();

                sampleCageReports.Add(report);
            }

            return sampleCageReports;
        }

        #endregion
    }
}