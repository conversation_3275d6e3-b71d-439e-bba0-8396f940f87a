@using Binit.Framework;
@using Binit.Framework.Interfaces.DAL
@using Domain.Logic.BusinessLogic.DTOs;
@using Domain.Entities.Model.Enum;
@using System.Globalization;
@using Microsoft.Extensions.Localization;
@using Binit.Framework.Constants.Authentication
@using Domain.Entities.Model;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Breeding.Dashboard;
@using LangStatus = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.WeightUniformityReport.Report
@inject IStringLocalizer<SharedResources> localizer
@inject IOperationContext operationContext

@{
    FilterDataDTO defaultFilterParameters = ViewData["DefaultFilterParameters"] as FilterDataDTO;

    string lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;

    // Variables for performance chart filter
    var warehouses = ViewData["Warehouses"] as List<SelectListItem>;
    var lines = ViewData["Lines"] as List<SelectListItem>;
    var genetics = ViewData["Genetics"] as List<SelectListItem>;
    var companies = ViewData["Companies"] as List<SelectListItem>;
    var supervisors = ViewData["Supervisors"] as List<SelectListItem>;
    var extensionists = ViewData["Extensionists"] as List<SelectListItem>;
    var productors = ViewData["Productors"] as List<SelectListItem>;
    var henBatchesOrderByNew = ViewData["HenBatchesOrderByNew"] as List<SelectListItem>;
    bool moreThanOneHenWarehouse = warehouses != null && warehouses.Count() > 1;
    bool moreThanOneLine = lines.Count() > 1;

    string minDayDefault = ViewData["MinDayDefault"] as string;
    string today = ViewData["Today"] as string;

    var status = ViewData["HenBatchStatus"] as List<SelectListItem>;

    bool areThereParentHenBatches = henBatchesOrderByNew.Count() > 0;

    bool hasAuthorizationSearaCharts = this.operationContext.UserIsInAnyRole(Roles.SearaDashboardChartsBreeding);
}

@section ViewStyles {
    <link href="~/css/dashboards.css" rel="stylesheet" />
    <!-- Message Carousel CSS -->
    <link href="~/css/carouselBS4.css" rel="stylesheet" />
    <!-- Calendar CSS -->
    <link href="~/css/fullcalendar.css" rel="stylesheet" />
    <!-- Gridstack -->
    <link href="~/css/gridstack/gridstack.css" rel="stylesheet" />
}

<!-- Start Filters  -->
<div class="container-fluid">
    <div class="row">
        <!-- Date/Week filters section -->
        <div class="col-md-2 p-1 border rounded shadow-sm bg-light">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold" for="DateStart">@localizer[Lang.DateStartLabel]</label>
                        <input type="text" language="@lang" class="form-control date-picker month-year-only" id="filter-from-date"  bname="-"
                               value="@(minDayDefault)">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold" for="startWeek">@localizer[Lang.StartWeekLabel]</label>
                        <input type="number" id="startWeek" class="form-control" min="1" max="53" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold" for="DateEnd">@localizer[Lang.DateEndLabel]</label>
                        <input type="text" language="@lang" class="form-control date-picker month-year-only" id="filter-to-date" bname="-"
                               value="@(today)">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold" for="endWeek">@localizer[Lang.EndWeekLabel]</label>
                        <input type="number" id="endWeek" class="form-control" min="1" max="53" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Regional/Unit/Extensionist/Supervisor filters section -->
        <div class="col-md-4 p-1 py-1 border rounded shadow-sm bg-light">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold" for="regionalFilter">@localizer[Lang.RegionalSelectLabel]</label>
                        <select class="form-control select2" id="regionalFilter">
                            <option value="">@localizer[Lang.RegionalSelectPlaceholder]</option>
                            @if (ViewData["Regionals"] != null)
                            {
                                foreach (var item in (List<SelectListItem>)ViewData["Regionals"])
                                {
                            <option value="@item.Value">@item.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold" for="unitFilter">@localizer[Lang.UnitSelectLabel] </label>
                        <select class="form-control select2" id="unitFilter">
                            <option value="">@localizer[Lang.UnitSelectPlaceholder]</option>
                            @if (ViewData["Units"] != null)
                            {
                                foreach (var item in (List<SelectListItem>)ViewData["Units"])
                                {
                            <option value="@item.Value">@item.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold" for="supervisorFilter">@localizer[Lang.SupervisorUserSelectLabel]</label>
                        <select class="form-control select2" id="supervisorFilter">
                            <option value="">@localizer[Lang.SupervisorUserSelectPlaceholder]</option>
                            @foreach (var item in supervisors)
                            {
                            <option value="@item.Value">@item.Text</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold" for="extensionistFilter">@localizer[Lang.ExtensionistUserSelectLabel]</label>
                        <select class="form-control select2" id="extensionistFilter">
                            <option value="">@localizer[Lang.ExtensionistUserSelectPlaceholder]</option>
                            @foreach (var item in extensionists)
                            {
                            <option value="@item.Value">@item.Text</option>
                            }
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Producer/Batch/HenBatchStatus/Warehouse/Line/Genetic/Gender/ReportType filters section -->
        <div class="col-md-6 p-1 border rounded shadow-sm bg-light">
            <div class="row">
                <div class="col-md-5">
                    <div class="form-group">
                        <label class="font-weight-bold" for="productorFilter">@localizer[Lang.ProductorUserSelectLabel]</label>
                        <select class="form-control select2" id="productorFilter">
                            <option value="">@localizer[Lang.ProductorUserSelectPlaceholder]</option>
                            @foreach (var item in productors)
                    {
                            <option value="@item.Value">@item.Text</option>
                    }
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="font-weight-bold" for="henbatch-status">@localizer[LangStatus.HenBatchStatus]</label>
                        <select class="form-control select2" id="henbatch-status">
                            @foreach (var item in status)
                    {
                        if (item.Value == "active")
                    {
                            <option value="@item.Value" selected>@item.Text</option>
                    }
                        else
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    }
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="font-weight-bold" for="parentHenBatchMainFilter">@localizer[Lang.HenBatchSelectLabel]</label>
                        <select class="form-control select2" id="parentHenBatchMainFilter">
                            <option value="" selected>@localizer[Lang.HenBatchAllOption]</option>
                            @if (henBatchesOrderByNew.Any())
                    {
                            <option value="@henBatchesOrderByNew.First().Value">@henBatchesOrderByNew.First().Text</option>
                            foreach (var item in henBatchesOrderByNew.Skip(1))
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    }
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="font-weight-bold" for="warehouse">@localizer[Lang.WarehouseLabel]</label>
                        <select class="form-control select2" id="warehouse">
                            <option value="">@localizer[Lang.WarehouseAllOption]</option>
                            @if (warehouses != null)
                    {
                            foreach (var item in warehouses)
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    }
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="font-weight-bold" for="line">@localizer[Lang.LineLabel]</label>
                        <select class="form-control select2" id="line">
                            <option value="" selected>@localizer[Lang.LineAllOption]</option>
                            @foreach (var item in lines)
                    {
                            <option value="@item.Value">@item.Text</option>
                    }
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="font-weight-bold" for="geneticFilter">@localizer[Lang.GeneticSelectLabel]</label>
                        <select class="form-control select2" id="geneticFilter">
                            <option value="">@localizer[Lang.GeneticSelectPlaceholder]</option>
                            @if (ViewData["GeneticsList"] != null)
                            {
                                foreach (var item in (List<SelectListItem>)ViewData["GeneticsList"])
                                {
                                    <option value="@item.Value">@item.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-2 mt-4">
                    <div class="form-check form-check-inline">
                        <label class="font-weight-bold form-check-label small-radio-label">
                            <input class="form-check-input" type="radio" name="gender" value="1" >
                            @(localizer[Lang.GenderMaleOption])
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <label class="font-weight-bold form-check-label small-radio-label">
                            <input class="form-check-input" type="radio" name="gender" value="2" checked>
                            @(localizer[Lang.GenderFemaleOption])
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <label class="font-weight-bold form-check-label small-radio-label">
                            <input class="form-check-input" type="radio" name="gender" value="3">
                            @(localizer[Lang.GenderBothOption])
                        </label>
                    </div>
                </div>
                <div class="col-md-2 mt-4">
                    <div class="form-check form-check-inline">
                        <label class="font-weight-bold form-check-label small-radio-label">
                            <input class="form-check-input" type="radio" name="reportType" value="1">
                            @(localizer[Lang.ReportTypeConsolidatedOption])
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <label class="font-weight-bold form-check-label small-radio-label">
                            <input class="form-check-input" type="radio" name="reportType" value="2" checked>
                            @(localizer[Lang.ReportTypeOpenOption])
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- End Filters  -->


    <div class="row">
        <div class="col-md-12 p-2 border rounded shadow-sm bg-light">
            <!-- Start Buttons -->
            <div class="row w-full p-2 d-flex flex-column overflow-auto align-items-center justify-content-center" style="gap:2px;">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-secondary btn-report" data-report-type="viability">
                        @(localizer[Lang.BtnManagerialViability])
                    </button>
                    <button type="button" class="btn btn-secondary btn-report" data-report-type="weight">
                        @(localizer[Lang.BtnManagerialWeight])
                    </button>
                    <button type="button" class="btn btn-secondary btn-report" data-report-type="uniformity">
                        @(localizer[Lang.BtnManagerialUniformity])
                    </button>
                    <button type="button" class="btn btn-secondary btn-report" data-report-type="mf_ratio">
                        @(localizer[Lang.BtnManagerialMF])
                    </button>
                    <button type="button" class="btn btn-secondary btn-report" data-report-type="gad">
                        @(localizer[Lang.BtnManagerialGAD])
                    </button>
                    @* WARN: Não estava especificado para o Breeding *@
                    <button type="button" class="btn btn-secondary btn-report" data-report-type="genetic">
                        @localizer[Lang.BtnManagerialGenetic]
                    </button>
                    @* WARN: Estava especificado como um novo gráfico, o que não está de acordo com o que foi cotado, talvez aqui necessite de novas implementações, no caso até novos campos *@
                    <button type="button" class="btn btn-secondary btn-report" data-report-type="mortality">
                        @(localizer[Lang.BtnManagerialMortality])
                    </button>
                </div>
                <div class="spinner-border" style="display: none;"></div>
            </div>
        <!-- End Buttons -->
            <div style="display: flex; width: 100%; gap: 10px;">
                <div class="col-lg-3">
                    <!-- Cards -->
                    <div id="sampleCard" style="display: block;">
                    </div>
                </div>

@* <div class="row justify-content-around" id="informativeCard" style="padding-bottom: 10px;display:flex;flex-wrap:wrap">
</div>

<div class="row justify-content-around" id="widthCard" style="padding-bottom: 10px;display:flex;flex-wrap:wrap">
</div> *@

@* <div id="dashboardCardsDiv">
    <div class="d-flex justify-content-center m-t-30 m-b-30">
        <div class="spinner-border"></div>
    </div>
    <hr>
</div> *@
<!-- End Cards -->
<!-- Calendar -->
@* <partial name="_TaskCalendarForDashboards" /> *@
<!-- End Calendar -->

<!-- Start New Charts -->
<!-- End New Charts -->

<!-- Charts -->
@* <div class="row w-100">
    <div class="filters m-l-40" id="containerFilters">
        <div class="form-group filter-input col-2" id="WHDiv"
            style="margin-left:0.5rem;width: 30rem; @(areThereParentHenBatches ? "" : "display:none")">
            <label for="warehouse">
                @(localizer[Lang.WarehouseLabel])
            </label>
            <select class="form-control select2" id="warehouse">
                <option value="" select>
                    @(localizer[Lang.WarehouseAllOption])
                </option>
                @if (warehouses != null)
                {
                    @foreach (var item in warehouses)
                    {
                        <option warehouse value="@item.Value">@item.Text</option>
                    }
                }
            </select>
        </div>

        <div class="form-group filter-input col-2" id="LineDiv"
            style="margin-left:0.5rem;width: 30rem; @(moreThanOneLine && (!moreThanOneHenWarehouse) ? "" : "display:none")">
            <label for="line">
                @(localizer[Lang.LineLabel])
            </label>
            <select class="form-control select2" id="line">
                <option value="" selected>
                    @(localizer[Lang.LineAllOption])
                </option>
                @foreach (var item in lines)
                {
                    <option line value="@item.Value">@item.Text</option>
                }
            </select>
        </div>

        <div class="form-group filter-input col-2" id="CageDiv"
            style="margin-left:0.5rem;width: 30rem; @(moreThanOneCage ? "" : "display:none")">
            <label for="cage">
                @(localizer[Lang.CageLabel])
            </label>
            <select class="form-control select2" id="cage">
                <option value="" selected>
                    @(localizer[Lang.CageAllOption])
                </option>
                @foreach (var item in cages)
                {
                    <option cage value="@item.Value">@item.Text</option>
                }
            </select>
        </div>

        <div class="form-group filter-input">
            <button id="mainButtonFilter" class="btn waves-effect waves-light btn-dark btn-sm datatable-action-button">
                @(localizer[Lang.BtnFilter])
            </button>
        </div>
    </div>
</div> *@


                <div class="col-lg-9">
@if (hasAuthorizationSearaCharts)
{
    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="Uniformity"></div>
        </div>
    </div>
    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="ViabilityFemale"></div>
        </div>
    </div>
    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="BirdWeight"></div>
        </div>
    </div>

    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="BirdWeightGain"></div>
        </div>
    </div>

    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="FeedIntakeGAD"></div>
        </div>
    </div>
    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="MaleFemale"></div>
        </div>
    </div>

     <div class="col-12">
        <div class="row w-100">
                                <div class="chart col-12" style="background-color: #fff;">
                                    <canvas class="ignite-chart performance-standards-chart"
                                        data-performance-label="Rendimento (%)"
                                        data-weeks-from-birth-label="Semanas de idade"
                                        data-body-weight-label="Peso corporal (g)" data-mortality-label="Mortalidade (%)"
                                        id="Genetic">
                                    </canvas>
                                </div>
        </div>
     </div>

    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="Mortality"></div>
        </div>
    </div>
}
            </div>
        </div>
    </div>
</div>
@* @if (hasAuthorizationSearaCharts)
{
    <!--Bird weight chart-->
    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="BirdWeight"></div>
        </div>
    </div>
    <br />

    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="ViabilityFemale"></div>
        </div>
    </div>
    <br />



    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="FeedIntakeGAD"></div>
        </div>
    </div>
    <br />

    <div class="col-12">
        <div class="row w-100">
            <div class="chart col-12" id="WeightGainFemales"></div>
        </div>
    </div>
    <br />
}

<div class="row w-100">
    <div class="filters m-l-40" id="containerFilters">
        <div class="filters m-t-10" id="mainDateFilters">
            <div class="form-group filter-input" style="margin-left:0.5rem">
                <label for="DateStart">@(localizer[Lang.DateStartLabel])</label>
                <br>
                <input type="text" language="@lang" class="form-control date-picker date-only"
                    id="main-date-filter-from-date" bname="-" value="@(minDayDefault)">
            </div>

            <div class="form-group filter-input" style="margin-left:0.5rem">
                <label for="DateStart">@(localizer[Lang.DateEndLabel])</label>
                <br>
                <input type="text" language="@lang" class="form-control date-picker date-only"
                    id="main-date-filter-to-date" bname="-" value="@(today)">
            </div>
        </div>
    </div>
</div>

<br />

<div class="col-12">
    <div class="row w-100">
        <div class="chart col-12" id="MaleFemaleDistributionChart"></div>
    </div>
</div>

<br />

<div class="col-12">
    <div class="row w-100">
        <div class="chart col-12" id="MortalityChart"></div>
    </div>
</div>

<br />

<div class="col-12">
    <div class="row w-100">
        <div class="chart col-12" id="Performance"></div>
    </div>
</div>

<br />

<div class="col-12">
    <div class="row w-100">
        <div class="chart col-12" id="HenBatchPerformance"></div>
    </div>
</div>

<br />

@*<div class="row justify-content-center">
    <div class="chart col-2" id="HenAmountByGroup"></div>
</div>

<br />

<div class="row">
    <div class="chart col-12" id="Capacity"></div>
</div>

<br />

<div class="row justify-content-center">
    <div class="col-2">
        <div class="row w-100">
            <div class="chart col-12" id="HenBatchWeekNumberHistogram"></div>
        </div>
        <div class="row w-100 justify-content-around" id="histogramFilters">
            <div class="col-10 row">
                <div class="col-12 col-sm-3">
                    <input class="form-control" type="number" id="henBatchHistogramWeekNumberBinLimit1"
                        name="henBatchHistogramWeekNumberBinLimit1"
                        value=@defaultFilterParameters.HenBatchHistogramWeekNumberBinLimit1>
                </div>
                <div class="col-12 col-sm-3">
                    <input class="form-control" type="number" id="henBatchHistogramWeekNumberBinLimit2"
                        name="henBatchHistogramWeekNumberBinLimit2"
                        value=@defaultFilterParameters.HenBatchHistogramWeekNumberBinLimit2>
                </div>
                <div class="col-12 col-sm-3">
                    <input class="form-control" type="number" id="henBatchHistogramWeekNumberBinLimit3"
                        name="henBatchHistogramWeekNumberBinLimit3"
                        value=@defaultFilterParameters.HenBatchHistogramWeekNumberBinLimit3>
                </div>
                <div class="col-12 col-sm-3">
                    <input class="form-control" type="number" id="henBatchHistogramWeekNumberBinLimit4"
                        name="henBatchHistogramWeekNumberBinLimit4"
                        value=@defaultFilterParameters.HenBatchHistogramWeekNumberBinLimit4>
                </div>
            </div>
            <div class="col-2 justify-content-end">
                <button type="button" class="btn border-dark btn-themecolor"
                    onclick="GetDataForHenWeekNumberHistogram()"><i class="icon-Bar-Chart"></i></button>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="chart col-12 justify-content-center" id="Genetics"></div>
    </div>
</div>
<br /> *@
<!-- End Charts -->
</div>
@section scripts {
    <!-- Carousel Functionality -->
    <script src="@Url.Content("~/js/bootstrapCarousel.js")"></script>
    <!-- End Carousel -->
    <!-- Calendar JavaScript -->
    <script>
        var tasks = @Json.Serialize(ViewData["Tasks"]);
        var taskTypes = @Json.Serialize(ViewData["TaskTypes"]);
        var taskManagerResources = @Json.Serialize(ViewData["TaskManagerResources"]);
        var relevances = @Json.Serialize(ViewData["Relevances"]);
        var userIsAdmin = "@(operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeBreedingAdministrator))";
        var area = "@(ViewData["Area"])";
        var containers = @Json.Serialize(ViewData["ContainersForSelect"]);
        var DashboardsResources = @Json.Serialize(ViewData["DashboardsResources"]);
        var userLanguage = "@(ViewData["UserLanguage"])";
        var hasAuthorizationSearaCharts = "@hasAuthorizationSearaCharts";
    </script>
    <script src="@Url.Content("~/js/moment.js")"></script>
    <script src="@Url.Content("~/lib/jqueryui/jquery-ui.min.js")"></script>
    <script src='@Url.Content("~/js/fullcalendar.min.js")'></script>
    <script src='@Url.Content("~/js/views/locales/locale-all.js")'></script>
    <script src="@Url.Content("~/js/dashboardTaskCalendar.js")" type="text/javascript"></script>
    <!-- End Calendar JavaScript -->
    <script src="@Url.Content("~/js/highcharts.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/zipcelx.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/highcharts.exporting.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/highcharts.exportData.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboardStyling.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/highchartHelpers.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboardGenericFunctions.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/highcharts.nodata.module.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/highcharts.sunburst.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardCapacityChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardGeneticPieChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardHenDistributionSunburstChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardPerformanceChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardBirdWeightChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardViabilityChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardMortalityChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardBreedingMaleFemaleDistributionChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardGeneticChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardBreedingGADChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/dashboardBreedingGenericChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/dashboards/lineCapacityChart.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/views/managerialBreeding/dashboard.js")" type="text/javascript"></script>
}

<ignite-load plugins="select2,date-time-picker,dropzone,switchery,chartjs,performanceStandardsChart"></ignite-load>
