﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.2.4.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.2, Version=20.2.4.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="SendEggsReport" Margins="50, 50, 50, 50" PageWidth="850" PageHeight="1100" Version="20.2">
  <Extensions>
    <Item1 Ref="1" Key="VSReportExtInfo" Value=".vsrepx" />
  </Extensions>
  <Bands>
    <Item1 Ref="2" ControlType="TopMarginBand" Name="topMarginBand1" HeightF="50" BackColor="Transparent" BorderColor="Transparent" Borders="None">
      <StylePriority Ref="3" UseBackColor="false" UseBorderColor="false" UseBorders="false" />
    </Item1>
    <Item2 Ref="4" ControlType="DetailBand" Name="detailBand1" HeightF="17.708334">
      <SubBands>
        <Item1 Ref="5" ControlType="SubBand" Name="SubBand1" HeightF="689.5833">
          <Controls>
            <Item1 Ref="6" ControlType="XRLabel" Name="label19" Multiline="true" Text="[TableTitle]" SizeF="100,23" LocationFloat="31.25,393.666656" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,96">
              <StylePriority Ref="7" UseFont="false" />
            </Item1>
            <Item2 Ref="8" ControlType="XRTable" Name="EggsTable" KeepTogether="true" TextAlignment="MiddleCenter" SizeF="687.500244,60" LocationFloat="30.2080784,416.666656" Font="Arial, 9.75pt, charSet=0" ForeColor="Black" Padding="2,2,0,0,96" BorderColor="Silver" Borders="All" Tag_type="System.String" Tag="[EggsTable]">
              <Rows>
                <Item1 Ref="9" ControlType="XRTableRow" Name="HeaderRow" Weight="0.7024383551463842">
                  <Cells>
                    <Item1 Ref="10" ControlType="XRTableCell" Name="tableCell5" Weight="0.90303030787276" Multiline="true" Text="[MaterialHeader]" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightGray">
                      <StylePriority Ref="11" UseFont="false" UseBackColor="false" />
                    </Item1>
                    <Item2 Ref="12" ControlType="XRTableCell" Name="tableCell6" Weight="1.4909090053590179" Multiline="true" Text="[HenBatchHeader]" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightGray">
                      <StylePriority Ref="13" UseFont="false" UseBackColor="false" />
                    </Item2>
                    <Item3 Ref="14" ControlType="XRTableCell" Name="tableCell7" Weight="0.78787880402031107" Multiline="true" Text="[EggQuantityHeader]" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightGray">
                      <StylePriority Ref="15" UseFont="false" UseBackColor="false" />
                    </Item3>
                    <Item4 Ref="16" ControlType="XRTableCell" Name="tableCell8" Weight="0.81818188274791093" Multiline="true" Text="[BrokenEggQuantityHeader]" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightGray">
                      <StylePriority Ref="17" UseFont="false" UseBackColor="false" />
                    </Item4>
                  </Cells>
                </Item1>
                <Item2 Ref="18" ControlType="XRTableRow" Name="EggsTableRow" Weight="0.7024383551463842">
                  <Cells>
                    <Item1 Ref="19" ControlType="XRTableCell" Name="tableCell1" Weight="0.90303030787276" Multiline="true" Text="tableCell1" />
                    <Item2 Ref="20" ControlType="XRTableCell" Name="tableCell2" Weight="1.4909090053590179" Multiline="true" Text="tableCell2" Tag_type="System.String" Tag="[EggsTable]" />
                    <Item3 Ref="21" ControlType="XRTableCell" Name="tableCell3" Weight="0.78787880402031107" Multiline="true" Text="tableCell3" />
                    <Item4 Ref="22" ControlType="XRTableCell" Name="tableCell4" Weight="0.81818188274791093" Multiline="true" Text="tableCell4" />
                  </Cells>
                </Item2>
              </Rows>
              <StylePriority Ref="23" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item2>
            <Item3 Ref="24" ControlType="XRShape" Name="shape6" SizeF="687.5,84.45834" LocationFloat="32.291748,294.374817">
              <Shape Ref="25" Fillet="30" ShapeName="Rectangle" />
            </Item3>
            <Item4 Ref="26" ControlType="XRShape" Name="shape5" SizeF="326.041656,27.0833435" LocationFloat="32.29154,219.3751">
              <Shape Ref="27" Fillet="30" ShapeName="Rectangle" />
            </Item4>
            <Item5 Ref="28" ControlType="XRShape" Name="shape4" SizeF="326.04126,27.0832825" LocationFloat="396.875519,219.375229">
              <Shape Ref="29" Fillet="30" ShapeName="Rectangle" />
            </Item5>
            <Item6 Ref="30" ControlType="XRLabel" Name="label18" Multiline="true" Text="[DescriptionValue]" TextAlignment="MiddleLeft" SizeF="687.5,84.45828" LocationFloat="31.25,294.374817" Font="Arial, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="31" UseFont="false" UseTextAlignment="false" />
            </Item6>
            <Item7 Ref="32" ControlType="XRLabel" Name="label17" Multiline="true" Text="[Description]" TextAlignment="MiddleLeft" SizeF="687.5,27.083252" LocationFloat="31.25,267.291534" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="33" UseFont="false" UseTextAlignment="false" />
            </Item7>
            <Item8 Ref="34" ControlType="XRLabel" Name="label16" Multiline="true" Text="[WaybillValue]" TextAlignment="MiddleLeft" SizeF="326.041382,27.0832825" LocationFloat="396.875519,219.375168" Font="Arial, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="35" UseFont="false" UseTextAlignment="false" />
            </Item8>
            <Item9 Ref="36" ControlType="XRLabel" Name="label15" Multiline="true" Text="[Waybill]" TextAlignment="MiddleLeft" SizeF="326.04126,27.0832672" LocationFloat="395.8339,192.291763" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="37" UseFont="false" UseTextAlignment="false" />
            </Item9>
            <Item10 Ref="38" ControlType="XRLabel" Name="label14" Multiline="true" Text="[IdentifierValue]" TextAlignment="MiddleLeft" SizeF="326.041656,27.0833435" LocationFloat="32.29154,219.374908" Font="Arial, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="39" UseFont="false" UseTextAlignment="false" />
            </Item10>
            <Item11 Ref="40" ControlType="XRLabel" Name="label13" Multiline="true" Text="[Identifier]" TextAlignment="MiddleLeft" SizeF="326.041656,27.0832825" LocationFloat="32.29154,192.2917" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="41" UseFont="false" UseTextAlignment="false" />
            </Item11>
            <Item12 Ref="42" ControlType="XRLabel" Name="label11" Multiline="true" Text="[User]" TextAlignment="MiddleLeft" SizeF="198.958618,27.083252" LocationFloat="523.9583,118.333344" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="43" UseFont="false" UseTextAlignment="false" />
            </Item12>
            <Item13 Ref="44" ControlType="XRLabel" Name="label9" Multiline="true" Text="[Origin]" TextAlignment="MiddleLeft" SizeF="198.958649,27.083252" LocationFloat="284.375183,118.333344" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="45" UseFont="false" UseTextAlignment="false" />
            </Item13>
            <Item14 Ref="46" ControlType="XRLabel" Name="label7" Multiline="true" Text="[FarmName]" TextAlignment="MiddleLeft" SizeF="197.916733,27.08329" LocationFloat="31.25,118.333305" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="47" UseFont="false" UseTextAlignment="false" />
            </Item14>
            <Item15 Ref="48" ControlType="XRLine" Name="line2" LineWidth="1.5" SizeF="750,23" LocationFloat="0,0" BorderWidth="2">
              <StylePriority Ref="49" UseBorderWidth="false" />
            </Item15>
            <Item16 Ref="50" ControlType="XRLine" Name="line1" LineWidth="1.5" SizeF="750,21.4583054" LocationFloat="0,85.91665" BorderWidth="2">
              <StylePriority Ref="51" UseBorderWidth="false" />
            </Item16>
            <Item17 Ref="52" ControlType="XRLabel" Name="label6" Multiline="true" Text="[DateValue]" TextAlignment="MiddleCenter" SizeF="194.791687,25.0000076" LocationFloat="523.9584,59.45832" Font="Arial, 11.25pt, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="53" UseFont="false" UseTextAlignment="false" />
            </Item17>
            <Item18 Ref="54" ControlType="XRLabel" Name="label5" Multiline="true" Text="[Date]" TextAlignment="MiddleCenter" SizeF="195.833313,25.0000076" LocationFloat="523.958435,24.458313" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="55" UseFont="false" UseTextAlignment="false" />
            </Item18>
            <Item19 Ref="56" ControlType="XRLabel" Name="label4" Multiline="true" Text="[FarmValue]" TextAlignment="MiddleCenter" SizeF="195.833282,26.4583282" LocationFloat="283.3335,57.9999924" Font="Arial, 11.25pt, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="57" UseFont="false" UseTextAlignment="false" />
            </Item19>
            <Item20 Ref="58" ControlType="XRLabel" Name="label3" Multiline="true" Text="[Farm]" TextAlignment="MiddleCenter" SizeF="195.833328,26.4583263" LocationFloat="284.3752,22.9999866" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
            </Item20>
            <Item21 Ref="60" ControlType="XRLabel" Name="label2" Multiline="true" Text="[ClassificationWarehouseValue]" TextAlignment="MiddleCenter" SizeF="196.875061,26.0416718" LocationFloat="31.25,57.9999924" Font="Arial, 11.25pt, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
            </Item21>
            <Item22 Ref="62" ControlType="XRLabel" Name="label1" Multiline="true" Text="[ClassificationWarehouse]" TextAlignment="MiddleCenter" SizeF="196.875061,26.4583263" LocationFloat="30.20827,22.9999866" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="63" UseFont="false" UseTextAlignment="false" />
            </Item22>
            <Item23 Ref="64" ControlType="XRShape" Name="shape3" SizeF="198.958618,28.1249084" LocationFloat="523.9583,145.416641">
              <Shape Ref="65" Fillet="30" ShapeName="Rectangle" />
            </Item23>
            <Item24 Ref="66" ControlType="XRLabel" Name="label10" Multiline="true" Text="[OriginValue]" TextAlignment="MiddleLeft" SizeF="198.958649,27.0832672" LocationFloat="284.375183,145.416519" Font="Arial, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="67" UseFont="false" UseTextAlignment="false" />
            </Item24>
            <Item25 Ref="68" ControlType="XRLabel" Name="label12" Multiline="true" Text="[UserValue]" TextAlignment="MiddleLeft" SizeF="198.958618,27.083252" LocationFloat="523.958069,146.458313" Font="Arial, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="69" UseFont="false" UseTextAlignment="false" />
            </Item25>
            <Item26 Ref="70" ControlType="XRShape" Name="shape1" SizeF="198.958679,27.083252" LocationFloat="284.375153,145.41658">
              <Shape Ref="71" Fillet="30" ShapeName="Rectangle" />
            </Item26>
            <Item27 Ref="72" ControlType="XRShape" Name="shape2" SizeF="196.875061,27.0832367" LocationFloat="30.20827,144.374847">
              <Shape Ref="73" Fillet="30" ShapeName="Rectangle" />
            </Item27>
            <Item28 Ref="74" ControlType="XRLabel" Name="label8" Multiline="true" Text="[FarmNameValue]" TextAlignment="MiddleLeft" SizeF="197.916733,27.0832672" LocationFloat="31.25,145.41658" Font="Arial, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <StylePriority Ref="75" UseFont="false" UseTextAlignment="false" />
            </Item28>
          </Controls>
        </Item1>
      </SubBands>
    </Item2>
    <Item3 Ref="76" ControlType="BottomMarginBand" Name="bottomMarginBand1" HeightF="50" />
    <Item4 Ref="77" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="122.916664">
      <Controls>
        <Item1 Ref="78" ControlType="XRLabel" Name="SendEggsReportTitle" Multiline="true" Text="[ReportTitle]" TextAlignment="MiddleCenter" SizeF="242.708344,59.375" LocationFloat="416.6668,28.125" Font="Verdana, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="79" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="80" ControlType="XRPictureBox" Name="pictureBox1" ImageSource="img,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" Sizing="ZoomImage" SizeF="216.666656,98.95833" LocationFloat="0,10.0000067" BorderColor="Transparent">
          <StylePriority Ref="81" UseBorderColor="false" />
        </Item2>
      </Controls>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>