using Binit.Framework;
using Binit.Framework.Interfaces.DAL;
using Domain.Entities.Model;
using Domain.Logic.Interfaces;
using Domain.Logic.BusinessLogic.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.Linq;
using WebApp.WebTools.Charts;
using Binit.Framework.Helpers;
using System.Text.RegularExpressions;

using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.WeightUniformityReportController;
using Binit.Framework.Helpers.Excel;

namespace WebApp.Controllers
{
    public class NaturalStringComparer : IComparer<string>
    {
        public int Compare(string x, string y)
        {
            if (x == null && y == null) return 0;
            if (x == null) return -1;
            if (y == null) return 1;

            var regex = new Regex(@"(\d+|\D+)");
            var xMatches = regex.Matches(x);
            var yMatches = regex.Matches(y);

            for (int i = 0; i < Math.Min(xMatches.Count, yMatches.Count); i++)
            {
                var xMatch = xMatches[i].Value;
                var yMatch = yMatches[i].Value;

                if (int.TryParse(xMatch, out int xNum) && int.TryParse(yMatch, out int yNum))
                {
                    if (xNum != yNum) return xNum.CompareTo(yNum);
                }
                else
                {
                    var comparison = string.Compare(xMatch, yMatch, StringComparison.OrdinalIgnoreCase);
                    if (comparison != 0) return comparison;
                }
            }

            return xMatches.Count.CompareTo(yMatches.Count);
        }
    }

    [Authorize]
    public class WeightUniformityReportController : Controller
    {
        private readonly IWeightUniformityReportBusinessLogic weightUniformityReportBusinessLogic;
        private readonly IHenBatchService henbatchService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IFarmService farmService;

        public WeightUniformityReportController(
            IWeightUniformityReportBusinessLogic weightUniformityReportBusinessLogic,
            IHenBatchService henbatchService,
            IStringLocalizer<SharedResources> localizer,
            IFarmService farmService
        )
        {
            this.weightUniformityReportBusinessLogic = weightUniformityReportBusinessLogic;
            this.henbatchService = henbatchService;
            this.localizer = localizer;
            this.farmService = farmService;
        }



        public void SetViewDatasForGraphic(HenStage? henStage)
        {
            IQueryable<HenBatch> henBatches = this.henbatchService
                .GetAll()
                .Where(hb => hb.HenStage == henStage && !hb.ParentId.HasValue);

            IQueryable<Guid?> farmsId = henBatches.Select(hb => hb.FarmId).Distinct();
            IQueryable<Farm> farms = farmService.GetAll().Where(f => farmsId.Any(id => id == f.Id));
            bool farmIsUnique = farms.Count() == 1;

            List<SelectListItem> farmList = farms.Select(f => new SelectListItem($"{f.Code} | {f.Name}", f.Id.ToString(), farmIsUnique)).ToList();
            ViewData["Farms"] = farmList;

            if (farmIsUnique)
            {
                var parentHenBatches = GetParentHenBatches(henStage.Value, farms.First().Id, null);
                ViewData["ParentHenBatches"] = parentHenBatches;
            }

            ViewData["HenBatchStatus"] = GetStatusOptions();
        }

        private List<SelectListItem> GetStatusOptions()
        {
            List<SelectListItem> items = new List<SelectListItem>()
            {
                new SelectListItem(localizer[Lang.HenBatchStatusAll], ""),
                new SelectListItem(localizer[Lang.HenBatchStatusActive], "active"),
                new SelectListItem(localizer[Lang.HenBatchStatusClosed], "closed")
            };

            return items;
        }

        public List<SelectListItem> GetParentHenBatches(HenStage henStage, Guid selectedFarm, bool? active)
        {
            IQueryable<HenBatch> parentHenBatches = this.henbatchService.GetAll()
                .Where(hb =>
                    hb.HenStage == henStage
                    && hb.FarmId == selectedFarm
                    && (!active.HasValue || (active.Value ? !hb.DateEnd.HasValue : hb.DateEnd.HasValue))
                    && !hb.ParentId.HasValue);


            var items = parentHenBatches.Select(hb =>
                new SelectListItem()
                {
                    Text = hb.DetailedName,
                    Value = hb.Id.ToString()
                })
                .OrderBy(sli => sli.Text).ToList();

            if (items.Count is 1)
                items.First().Selected = true;

            return items;
        }

        #region Report
        public IActionResult Report(HenStage? henStage = null)
        {
            SetViewDatasForGraphic(henStage ?? HenStage.Breeding);
            return View();
        }

        [HttpPost]
        public FileResult ExcelExport([FromBody] WeightUniformityReportFilterDTO filters)
        {
            if (filters == null || filters.ParentHenBatch == null)
            {
                throw new ArgumentException("Filtros inválidos ou ausentes.");
            }

            ExportResult exportResult = this.weightUniformityReportBusinessLogic.GenerateReportInExcel(filters);
            return File(exportResult.Stream, exportResult.ExportMimeType, exportResult.Filename);
        }

        [HttpGet]
        public IActionResult ReportData([FromQuery] WeightUniformityReportFilterDTO filters)
        {
            if (filters == null || filters.ParentHenBatch == null)
            {
                return BadRequest("Filtros inválidos ou ausentes.");
            }

            var reportData = this.weightUniformityReportBusinessLogic.GetReportData(filters);
            var formattedData = FormatDataForHandsontable(reportData);
            return Json(formattedData);
        }

        private object FormatDataForHandsontable(WeightUniformityReportDTO reportData)
        {
            var maleData = reportData.DetailedWeightComparison.Where(x => x.PerformanceGender == "M").ToList();
            var femaleData = reportData.DetailedWeightComparison.Where(x => x.PerformanceGender == "F").ToList();

            return new
            {
                Male = new
                {
                    Headers = GetHeaders(maleData),
                    Data = GetTableData(maleData, "Male"),
                    Summary = new
                    {
                        Batch = reportData.Batch,
                        Farm = reportData.Farm,
                        PlacementDate = reportData.HenReportCreationMinDate,
                        InitialAmount = reportData.DetailedWeightComparison
                            .GroupBy(x => x.Id)
                            .Select(g => g.First())
                            .Sum(x => x.InitialHenAmountMale),
                        CurrentAmount = reportData.DetailedWeightComparison
                            .GroupBy(x => x.Id)
                            .Select(g => g.First())
                            .Sum(x => x.InitialHenAmountMale - x.DeadAccumulatedMale)
                    }
                },
                Female = new
                {
                    Headers = GetHeaders(femaleData),
                    Data = GetTableData(femaleData, "Female"),
                    Summary = new
                    {
                        Batch = reportData.Batch,
                        Farm = reportData.Farm,
                        PlacementDate = reportData.HenReportCreationMinDate,
                        InitialAmount = reportData.DetailedWeightComparison
                            .GroupBy(x => x.Id)
                            .Select(g => g.First())
                            .Sum(x => x.InitialHenAmountFemale),
                        CurrentAmount = reportData.DetailedWeightComparison
                            .GroupBy(x => x.Id)
                            .Select(g => g.First())
                            .Sum(x => x.InitialHenAmountFemale - x.DeadAccumulatedFemale)
                    }
                }
            };
        }

        private object GetHeaders(List<WeightComparisonReportDTO> data)
        {
            var warehouseLineGroups = data
                .GroupBy(x => new { x.WarehouseName, x.LineName, x.CategoryName })
                .OrderBy(g => g.Key.WarehouseName)
                .ThenBy(g => g.Key.LineName, new NaturalStringComparer())
                .ToList();

            // Primeira linha (Data, Idade e Aviário + Box)
            var firstRow = new List<object> { "", "" };
            foreach (var group in warehouseLineGroups)
                firstRow.Add(new { label = $"{group.Key.WarehouseName} - {group.Key.LineName}", colspan = 3, rowspan = 1 });

            // Segunda linha (Categoria/Genética)
            var secondRow = new List<object> { "", "" }; // Células vazias para Data e Idade
            foreach (var group in warehouseLineGroups)
                secondRow.Add(new { label = group.Key.CategoryName, colspan = 3, rowspan = 1 });

            // Terceira linha (Peso, %, Unif.)
            var thirdRow = new List<object>
            {
                new { label = "Data", colspan = 1, rowspan = 3, width = 300 },
                new { label = "Idade", colspan = 1, rowspan = 3 }
            }; // Células vazias para Data e Idade
            foreach (var group in warehouseLineGroups)
            {

                thirdRow.Add(new { label = "Peso", width = 140 });
                thirdRow.Add(new { label = "%", width = 140 });
                thirdRow.Add(new { label = "Unif.", width = 140 });
            }

            return new[] { firstRow, secondRow, thirdRow };
        }

        private List<object> GetTableData(List<WeightComparisonReportDTO> data, string gender)
        {
            var tableData = new List<object>();

            // Cria o dicionário de mapeamento de colunas para Aviário + Box
            Dictionary<string, int> warehouseLineColumnMap = new Dictionary<string, int>();
            int initialColumn = 2; // Começa em 2 pois as colunas 0 e 1 são Data e Idade

            // Obtém todos os Aviários + Box únicos e ordenados
            var uniqueWarehouseLines = data
                .Select(x => new { x.WarehouseName, x.LineName })
                .Distinct()
                .OrderBy(x => x.WarehouseName)
                .ThenBy(x => x.LineName, new NaturalStringComparer())
                .ToList();

            // Cria o mapeamento de colunas
            foreach (var warehouseLine in uniqueWarehouseLines)
            {
                string key = $"{warehouseLine.WarehouseName} - {warehouseLine.LineName}";
                warehouseLineColumnMap[key] = initialColumn;
                initialColumn += 3; // Avança 3 colunas (Peso, %, Unif.)
            }

            // Agrupa por data e semana
            var dateWeekGroups = data
                .Where(x => x.Date.HasValue)
                .GroupBy(x => new { x.Date, x.Week })
                .OrderBy(g => g.Key.Date)
                .ThenBy(g => g.Key.Week);

            foreach (var group in dateWeekGroups)
            {
                // Cria uma linha com valores padrão (0) para todas as colunas
                var row = new object[2 + (warehouseLineColumnMap.Count * 3)]; // Data, Idade + (Aviário+Box * 3 colunas)
                row[0] = group.Key.Date.Value.ToString("dd/MM/yyyy"); // Data
                row[1] = group.Key.Week; // Idade

                // Preenche com zeros todas as colunas de dados
                for (int i = 2; i < row.Length; i++)
                {
                    row[i] = 0;
                }

                // Agrupa os dados por Aviário + Box para esta data/semana
                var warehouseLineGroups = group
                    .GroupBy(x => new { x.WarehouseName, x.LineName })
                    .OrderBy(g => g.Key.WarehouseName)
                    .ThenBy(g => g.Key.LineName, new NaturalStringComparer());

                foreach (var warehouseLine in warehouseLineGroups)
                {
                    string key = $"{warehouseLine.Key.WarehouseName} - {warehouseLine.Key.LineName}";

                    if (warehouseLineColumnMap.TryGetValue(key, out int columnIndex))
                    {
                        // ** MODIFICAÇÃO AQUI: Usar propriedades com base no gênero **
                        if (gender == "Male")
                        {
                            var firstItem = warehouseLine.First();
                            // Calcula médias para grupos com múltiplos itens (Male)
                            var weight = warehouseLine.Count() > 1
                                ? Math.Round(warehouseLine.Average(x => x.ActualWeightMale), 2)
                                : firstItem.ActualWeightMale;

                            var percentage = warehouseLine.Count() > 1
                                ? Math.Round(((warehouseLine.Average(x => x.ActualWeightMale) - firstItem.StandardWeightMale) / firstItem.StandardWeightMale * 100), 2)
                                : firstItem.WeightPercentageMale; // Assumindo que WeightPercentageMale já está calculado se Count() <= 1

                            var uniformity = warehouseLine.Count() > 1
                                ? Math.Round(warehouseLine.Average(x => x.UniformityMale), 2)
                                : firstItem.UniformityMale;

                            // Adiciona os valores nas colunas corretas (Male)
                            row[columnIndex] = weight;
                            row[columnIndex + 1] = percentage;
                            row[columnIndex + 2] = uniformity;
                        }
                        else if (gender == "Female")
                        {
                            var firstItem = warehouseLine.First();
                            // Calcula médias para grupos com múltiplos itens (Female)
                            var weight = warehouseLine.Count() > 1
                                ? Math.Round(warehouseLine.Average(x => x.ActualWeightFemale), 2)
                                : firstItem.ActualWeightFemale;

                            var percentage = warehouseLine.Count() > 1
                                ? Math.Round(((warehouseLine.Average(x => x.ActualWeightFemale) - firstItem.StandardWeightFemale) / firstItem.StandardWeightFemale * 100), 2)
                                : firstItem.WeightPercentageFemale; // Assumindo que WeightPercentageFemale já está calculado se Count() <= 1

                            var uniformity = warehouseLine.Count() > 1
                                ? Math.Round(warehouseLine.Average(x => x.UniformityFemale), 2)
                                : firstItem.UniformityFemale;

                            // Adiciona os valores nas colunas corretas (Female)
                            row[columnIndex] = weight;
                            row[columnIndex + 1] = percentage;
                            row[columnIndex + 2] = uniformity;
                        }
                        // Adicionar um else ou tratamento para gênero inválido se necessário
                    }
                }

                tableData.Add(row);
            }

            return tableData;
        }
        #endregion
    }
}