﻿using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Interfaces.DAL;
using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs.SampleCageReportDTOs;
using Domain.Logic.DTOs.SampleCageReportDTOs;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WebAPI.Attributes;
using WebAPI.DTOs.HenReportDTOs;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebAPI.SampleCageReportController;


namespace WebAPI.Controllers
{
    [AuthorizeAnyRoles(Roles.BackofficeSuperAdministrator, Roles.BackofficeLayingAdministrator, Roles.BackofficeLayingUser,
        Roles.BackofficeLayingDailyReportsAdministrator, Roles.BackofficeLayingDailyReportsUser, Roles.BackofficeLayingHenReportAdministrator,
        Roles.BackofficeLayingHenReportUser, Roles.BackofficeBreedingAdministrator, Roles.BackofficeBreedingUser,
        Roles.BackofficeBreedingDailyReportsAdministrator, Roles.BackofficeBreedingDailyReportsUser, Roles.BackofficeBreedingHenReportAdministrator,
        Roles.BackofficeBreedingHenReportUser)]
    [Route("api/sample-cage-report")]
    [ApiController]
    public class SampleCageReportController : ControllerBase
    {

        #region Properties
        private readonly ISampleCageReportBusinessLogic sampleCageReportBusinessLogic;
        private readonly IEggWeightReportBusinessLogic eggWeightReportBusinessLogic;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IServiceTenantDependent<SampleCage> sampleCageService;


        #endregion

        #region Constructor

        public SampleCageReportController(
            ISampleCageReportBusinessLogic sampleCageReportBusinessLogic,
            IEggWeightReportBusinessLogic eggWeightReportBusinessLogic,
            IStringLocalizer<SharedResources> localizer,
            IServiceTenantDependent<SampleCage> sampleCageService)
        {
            this.sampleCageReportBusinessLogic = sampleCageReportBusinessLogic;
            this.eggWeightReportBusinessLogic = eggWeightReportBusinessLogic;
            this.localizer = localizer;
            this.sampleCageService = sampleCageService;
        }

        #endregion

        #region Endpoints

        /// <summary>
        /// Creates new sample cage reports by warehouse, returns the ids of the created henreports than contained declared deaths or depopulations.
        /// </summary>
        /// <response code="200">Sample cage report successfully created</response>
        /// <response code="404">An id hasn't been found</response>
        /// <response code="500">There was an unexpected error trying to create the report</response>
        [ProducesResponseType(typeof(HenReportResponse), (int)HttpStatusCode.Created)]
        [HttpPost("from-warehouse")]
        public async Task<ActionResult<HenReportResponse>> SampleCageReportFromWarehouse([FromBody] SampleCageReportReq dto)
        {
            try
            {
                // List<SampleCageReport> reports = sampleCageReportBusinessLogic.ValidateDTO(dto);
                // await sampleCageReportBusinessLogic.CreateReports(reports);

                var newCreateSampleCageReportFromTableDTO = new CreateSampleCageReportFromTableDTO
                {
                    ReportDate = dto.Date.ToString("yyyy-MM-dd"),
                    Reports = dto.SampleCageMeasurements.Select(s => new SampleCageReportFromTableDTO
                    {
                        HenBatchId = s.HenBatchId,
                        SampleCageId = s.SampleCageId,
                        WarehouseId = dto.WarehouseId,
                        WarehouseName = "",
                        LineName = s.Name,
                        WeightFemale = s.AvgFemaleBirdWeight,
                        WeightMale = s.AvgMaleBirdWeight,
                        CvFemale = s.VariationCoefficientFemale,
                        CvMale = s.VariationCoefficientMale,
                        UniformityFemale = s.UniformityFemale,
                        UniformityMale = s.UniformityMale,
                        EggWeight = s.AvgEggWeight,
                    }).ToList()

                };

                var entities = await sampleCageReportBusinessLogic.CreateReportsFromTableAsync(newCreateSampleCageReportFromTableDTO);

                // Process egg weight reports for laying hens
                foreach (var report in newCreateSampleCageReportFromTableDTO.Reports)
                {
                    if (report.EggWeight.HasValue && report.EggWeight.Value > 0)
                    {
                        try
                        {
                            if (report.SampleCageId != null && report.SampleCageId != Guid.Empty)
                            {
                                // Check if the SampleCage exists
                                var sampleCageExists = sampleCageService.GetAll()
                                    .Any(sc => sc.Id == report.SampleCageId);

                                if (!sampleCageExists)
                                {
                                    continue;
                                }
                            }

                            // Create egg weight report
                            await eggWeightReportBusinessLogic.CreateEggWeightReportFromSampleCageReport(
                                report.HenBatchId,
                                DateTime.Parse(newCreateSampleCageReportFromTableDTO.ReportDate),
                                report.EggWeight.Value,
                                report.SampleCageId);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error creating egg weight report: {ex.Message}");
                        }
                    }
                }


                return Ok(new { Message = localizer[Lang.ReportCreated].Value });
            }
            catch (ValidationException ex)
            {
                ValidationProblemDetails validationErrors = new ValidationProblemDetails(
                    ex.Errors.Select(ve => new { ve.Key, ve.Value })
                    .ToDictionary(ve => ve.Key, ve => ve.Value.Split(","))
                );
                return ValidationProblem(validationErrors);
            }
        }

        /// <summary>
        /// Get All sample cage reports for offline functionality.
        /// </summary>
        /// <response code="200">Sample Cage reports successfully retrieved</response>
        /// <response code="500">There was an unexpected error trying to get the reports</response>
        [HttpGet("full")]
        [HateoasActionFilter]
        public async Task<ActionResult<List<SampleCageReportFullItem>>> GetFull()
        {
            return Ok(await sampleCageReportBusinessLogic.GetFullListForOffline());
        }
        #endregion
    }
}
