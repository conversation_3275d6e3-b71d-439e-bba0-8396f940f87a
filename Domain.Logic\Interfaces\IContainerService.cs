﻿using Binit.Framework.Interfaces.DAL;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs.StockDTOs;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Domain.Logic.Interfaces
{
    public interface IContainerService<TContainer> : IServiceTenantDependent<TContainer> where TContainer: Container
    {

        /// <summary>
        /// Returns a cluster by Id with its relationships.
        /// </summary>
        TContainer GetWithMaterial(Guid id, bool asNoTracking = false, bool? active = true);

        /// <summary>
        /// Returns an IQueryable of containers with its material containers and accepted material types.
        /// </summary>
        IQueryable<TContainer> GetAllWithMaterial(bool ignoringClaims = false);

        /// <summary>
        /// Return the farm's day of week.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        DayOfWeek GetDayOfWeek(Guid id);

        /// <summary>
        /// Returns a List<Container> of containers filtered severside.
        /// </summary>
        Task<List<TContainer>> GetAllByTermAsync(string searchTerm);

        /// <summary>
        /// Returns a List<SelectListItem> of containers.
        /// </summary>
        List<SelectListItem> GetAllAsListForArea(AreaEnum? area);

        /// <summary>
        /// Returns a List<SelectListItem> of material types.
        /// </summary>
        List<SelectListItem> GetAllMaterialTypesAsListForArea(AreaEnum? area);

        /// <summary>
        /// Returns a List<SelectListItem> of containers.
        /// </summary>
        List<SelectListItem> GetAllWithMaterialAsList();

        /// <summary>
        /// Returns an IQueryable of containers with its happening containers and happening types.
        /// </summary>
        IQueryable<TContainer> GetAllWithHappenings();

        /// <summary>
        /// Returns a List<Container> of containers.
        /// </summary>
        IQueryable<Container> GetAllFull();

        /// <summary>
        /// Returns a List<Container> of containers filtered.
        /// </summary>
        IQueryable<Container> GetAllFullFiltered(string area = null, Dictionary<string, string> filters=null);

        /// <summary>
        /// Returns a IQueryable<stockIndexDTO> of containers filtered for index.
        /// </summary>
        IQueryable<StockIndexDTO> GetAllFullFilteredIndex(Dictionary<string, string> filters = null);

        /// <summary>
        /// return boolean if has inconsistencies or not.
        /// </summary>
        bool CheckInconsistency(Container container);

        ///<summary>
        /// Returns  an IQueryable of containers filtered by area 
        /// <summary>
        IQueryable<Container> GetAllByArea(AreaEnum area);

        ///<summary>
        /// If addMaterials is false, returns containers filtered by area, else containers and its underlying materials filtered by area 
        /// <summary>
        IQueryable<Container> GetAllByAreaWithMaterials(AreaEnum area, bool addMaterials = true, bool asNoTracking = false);

        ///<summary>
        /// Returns  an list of containerTypes to select
        /// <summary>
        IEnumerable<(string containerType,string containerTypeName)> GetContainersTypes(AreaEnum? area = null);

        /// <summary>
        /// Returns an IQueryable of containers with relations used in Stock index with inconsistencies.
        /// </summary>
        IQueryable<Container> GetAllFullForStockIndexWithInconsistencies();

        /// <summary>
        /// Returns an IQueryable of containers with relations used in Stock index.
        /// </summary>
        IQueryable<Container> GetAllFullForStockIndex();

        /// <summary>
        /// Returns a Container with all their relations.
        /// </summary>
        Container GetFull(Guid id);

        /// <summary>
        /// Returns a Container with all their materials and capacity units.
        /// </summary>
        Container GetWithMaterialContainers(Guid id);

        /// <summary>
        /// Returns a Container with origin containers
        /// </summary>
        Container GetWithOriginContainers(Guid id);

        /// <summary>
        /// Returns a Container with origin containers and material batches 
        /// </summary>
        Container GetWithOriginContainersAndBatches(Guid id);

        /// <summary>
        /// Returns a Container with accepted material types
        /// </summary>
        Container GetWithAcceptedMaterialTypes(Guid id);

        /// <summary>
        /// Returns a Container with accepted material types and material container
        /// </summary>
        Container GetWithAcceptedMaterialTypesAndMaterialsContainer(Guid id);

        /// <summary>
        /// Returns a Container with the area containers.
        /// </summary>
        Container GetWithAreaContainers(Guid id);

        /// <summary>
        /// Returns the available materials in stock for use in filter.
        /// </summary>
        List<SelectListItem> GetMaterialsOptions(AreaEnum? area);

        /// <summary>
        /// Returns the available materials (by material type) in stock for use in filter.
        /// </summary>
        List<SelectListItem> GetMaterialsByTypeOptions(AreaEnum? area, Guid? materialTypeId);

        /// <summary>
        /// Creates container name chaining parents containers if there is more than one option for each.
        /// </summary>
        string CreateDetailedName(Container entity, IStringLocalizer customLocalizer = null);

        /// <summary>
        /// Update files throw the Container Service comparing the container files currently on the db and the in memory container files.
        /// </summary>
        Task UpdateFiles(Guid containerId, List<TenantDependentEntityFile> newFiles);

        /// <summary>
        /// Returns containers with free space for indicated material types.
        /// </summary>
        IQueryable<TContainer> GetAllWithAvailableSpace(bool asNoTracking = false, bool useDefaultSorting = true, params string[] materialTypePaths);

        /// <summary>
        /// Returns an IQueryable of active containers.
        /// </summary>
        IQueryable<Container> GetAllActive();

        /// <summary>
        /// Returns an IQueryable of active containers whith all its relationships .
        /// </summary>
        IQueryable<Container> GetAllFullActive();

        /// <summary>
        /// Returns all accepted material types for a particual container.
        /// </summary>
        IQueryable<MaterialType> GetAcceptedMaterialTypes(Guid id, bool verifyByHenBatch = false);

        /// <summary>
        /// Returns materials that a container has available. They can be filtered by material type.
        /// </summary>
        IQueryable<MaterialContainer> GetAvailableMaterials(Guid id, string materialPath);

        /// <summary>
        /// Returns all material batches in a container. Can be filtered by material.
        /// </summary>
        IQueryable<MaterialBatchContainer> GetAvailableMaterialBatches(Guid id, Guid? materialId = null);

        ///<summary>
        /// Returns  an IQueryable of containers filtered by areas 
        /// <summary>
        IQueryable<Container> GetAllByAreas(List<AreaEnum> areas);

        /// <summary>
        /// Returns containers that contain the alias for the indicated namer
        /// </summary>
        IQueryable<TContainer> GetByAlias(Guid namerId, string alias);

        /// <summary>
        ///  Function used to get a container with the necesary relationships to handle a shipping note
        /// </summary>
        IQueryable<TContainer> GetForHandleShippingNote(bool asNoTracking = false, bool? active = true, params Guid[] ids);

        /// <summary>
        /// Asynchronously retrieves a container by code.
        /// </summary>
        Task<TContainer> GetByCodeAsync(string code);

        /// <summary>
        /// Return a material id list.
        /// </summary>
        IQueryable<Guid> GetAllMaterialsForArea(AreaEnum? area);

        /// <summary>
        /// Returns a container by Id with its material containers and accepted material.
        /// </summary>
        TContainer GetWithMaterialAndAcceptedMaterial(Guid id, bool asNoTracking = false);

        /// <summary>
        /// Returns the container stock data to adjust.
        /// </summary>
        StockAdjustmentDTO GetForStockAdjustment(Guid id);

        /// <summary>
        /// Returns all containers with their acepted material types, filter by farm
        /// </summary>
        public IQueryable<Container> GetAllFilterByFarm(Guid farmId);

        /// <summary>
        /// Returns all containers with their relationships, filter by farm and add its origins
        /// </summary>
        IQueryable<Container> GetAllForFarmStructure(Guid farmId);

        EntityTypeEnum? ParseEntityTypeEnum(string containerType);

        /// <summary>
        /// Update container passed from a job 
        /// </summary>
        Task UpdateFromJobAsync(TContainer entity, IStringLocalizer customLocalizer);
    }
}
