using Binit.Framework;
using Binit.Framework.Interfaces.DAL;
using Domain.Entities.Model;
using Domain.Logic.Interfaces;
using Domain.Logic.BusinessLogic.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.Linq;

using Binit.Framework.Helpers.Excel;


namespace WebApp.Controllers
{
    [Authorize]
    public class ManagerialLayingReportController : Controller
    {
        private readonly IManagerialLayingReportBusinessLogic managerialLayingReportBusinessLogic;
        private readonly IHenBatchService henbatchService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IFarmService farmService;

        public ManagerialLayingReportController(
            IManagerialLayingReportBusinessLogic managerialLayingReportBusinessLogic,
            IHenBatchService henbatchService,
            IStringLocalizer<SharedResources> localizer,
            IFarmService farmService
        )
        {
            this.managerialLayingReportBusinessLogic = managerialLayingReportBusinessLogic;
            this.henbatchService = henbatchService;
            this.localizer = localizer;
            this.farmService = farmService;
        }

        private List<SelectListItem> GetStatusOptions()
        {
            List<SelectListItem> items = new List<SelectListItem>()
            {
                new SelectListItem("Todos", ""),
                new SelectListItem("Ativos", "active"),
                new SelectListItem("Fechados", "closed")
            };

            return items;
        }

        public List<SelectListItem> GetParentHenBatches(Guid selectedFarm, bool? active)
        {
            IQueryable<HenBatch> parentHenBatches = this.henbatchService.GetAll()
                .Where(hb =>
                    hb.HenStage == HenStage.Laying
                    && hb.FarmId == selectedFarm
                    && (!active.HasValue || (active.Value ? !hb.DateEnd.HasValue : hb.DateEnd.HasValue))
                    && !hb.ParentId.HasValue);


            var items = parentHenBatches.Select(hb =>
                new SelectListItem()
                {
                    Text = hb.DetailedName,
                    Value = hb.Id.ToString()
                })
                .OrderBy(sli => sli.Text).ToList();

            if (items.Count is 1)
                items.First().Selected = true;

            return items;
        }

        #region Report
        public IActionResult Report()
        {
            IQueryable<HenBatch> henBatches = this.henbatchService
                .GetAll()
                .Where(hb => hb.HenStage == HenStage.Laying && !hb.ParentId.HasValue);

            IQueryable<Guid?> farmsId = henBatches.Select(hb => hb.FarmId).Distinct();
            IQueryable<Farm> farms = farmService.GetAll().Where(f => farmsId.Any(id => id == f.Id));
            bool farmIsUnique = farms.Count() == 1;

            List<SelectListItem> farmList = farms.Select(f => new SelectListItem($"{f.Code} | {f.Name}", f.Id.ToString(), farmIsUnique)).ToList();
            ViewData["Farms"] = farmList;

            if (farmIsUnique)
            {
                var parentHenBatches = GetParentHenBatches(farms.First().Id, null);
                ViewData["ParentHenBatches"] = parentHenBatches;
            }

            ViewData["HenBatchStatus"] = GetStatusOptions();
            return View();
        }

        [HttpPost]
        public FileResult ExcelExport([FromBody] ManagerialLayingReportFilterDTO filters)
        {
            if (filters == null || filters.ParentHenBatch == null)
            {
                throw new ArgumentException("Filtros inválidos ou ausentes.");
            }

            ExportResult exportResult = this.managerialLayingReportBusinessLogic.GenerateReportInExcel(filters);
            return File(exportResult.Stream, exportResult.ExportMimeType, exportResult.Filename);
        }

        [HttpPost]
        public IActionResult GetReportData([FromBody] ManagerialLayingReportFilterDTO filters)
        {
            if (filters == null || filters.ParentHenBatch == null)
            {
                throw new ArgumentException("Filtros inválidos ou ausentes.");
            }

            return Ok(managerialLayingReportBusinessLogic.GetReportData(filters));
        }
        #endregion
    }
}