using Binit.Framework.Helpers;
using Domain.Entities.Constants.ReportPlanner;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.FoodInventoryDTOs;
using Domain.Logic.Interfaces;
using Domain.Logic.Interfaces.PlannerBusinessLogic;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Domain.Logic.BusinessLogic
{
    public class InventoryBusinessLogic : IInventoryBusinessLogic
    {
        private readonly IHenBatchService henBatchService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IShippingNoteService shippingNoteService;
        private readonly IContainerService<Container> containerService;
        private readonly IGADPlannerBusinessLogic gadPlannerBusinessLogic;

        public InventoryBusinessLogic(
            IHenBatchService henBatchService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IShippingNoteService shippingNoteService,
            IContainerService<Container> containerService,
            IGADPlannerBusinessLogic gadPlannerBusinessLogic)
        {
            this.henBatchService = henBatchService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.shippingNoteService = shippingNoteService;
            this.containerService = containerService;
            this.gadPlannerBusinessLogic = gadPlannerBusinessLogic;
        }

        #region Get food inventory functions

        // Food capacity unit ID constant - all foods are measured in kilograms
        private static readonly Guid FoodCapacityUnitId = Guid.Parse("E6F01CB9-E6B4-4163-8972-000CCA13108F");

        public IQueryable<Container> HandleFilters(IQueryable<Container> query, FilterStockCardsDTO filters)
        {
            if (filters == null)
                return query;

            // Filter by Food capacity unit ID - only include materials with this capacity unit
            query = query.Where(c => c.MaterialContainers.Any(mc =>
                mc.Material.CapacityUnitId == FoodCapacityUnitId && mc.Quantity > 0));

            if (filters.HenStage.HasValue)
            {
                AreaEnum? areaEnum = null;
                if (filters.HenStage == HenStage.Breeding)
                {
                    areaEnum = AreaEnum.Breeding;
                }
                else if (filters.HenStage == HenStage.Laying)
                {
                    areaEnum = AreaEnum.Laying;
                }
                if (areaEnum != null)
                {
                    query = query.Where(c => c.AreaContainers.Any(ac => ac.AreaEnum == areaEnum));
                }
            }

            if (!string.IsNullOrEmpty(filters.Productor))
            {
                query = query.Where(c => c.FarmId.ToString() == filters.Productor);
            }

            if (!string.IsNullOrEmpty(filters.Extensionist))
            {
                query = query.Where(c => c.Farm.TechnicianId.ToString() == filters.Extensionist);
            }

            if (!string.IsNullOrEmpty(filters.Supervisor))
            {
                query = query.Where(c => c.Farm.SupervisorId.ToString() == filters.Supervisor);
            }

            if (!string.IsNullOrEmpty(filters.Unit))
            {
                query = query.Where(c => c.Farm.CompanyId.ToString() == filters.Unit);
            }

            if (!string.IsNullOrEmpty(filters.Regional))
            {
                query = query.Where(c => c.Farm.Company.RegionalId.ToString() == filters.Regional);
            }

            if (!string.IsNullOrEmpty(filters.Material))
            {
                query = query.Where(c => c.MaterialContainers.Any(mc => mc.Material.InternalId == filters.Material));
            }

            if (!string.IsNullOrEmpty(filters.MaterialType))
            {
                if (Guid.TryParse(filters.MaterialType, out Guid materialTypeId))
                {
                    query = query.Where(c => c.MaterialContainers.Any(mc =>
                        mc.Material.MaterialTypeId == materialTypeId));
                }
                else
                {
                    query = query.Where(c => c.MaterialContainers.Any(mc =>
                        mc.Material.MaterialType.Name.Contains(filters.MaterialType) ||
                        mc.Material.MaterialType.Path.Contains(filters.MaterialType)));
                }
            }

            query = query.Include(c => c.MaterialContainers)
                        .ThenInclude(mc => mc.Material)
                            .ThenInclude(m => m.MaterialType)
                        .Include(c => c.MaterialContainers)
                            .ThenInclude(mc => mc.Material)
                                .ThenInclude(m => m.CapacityUnit);

            return query;
        }

        public List<Guid> GetHenBatchIds(FilterStockCardsDTO filters, HenStage? henStage = null)
        {
            var henBatchesQuery = henBatchService.GetAll()
                .Where(hb => hb.ContainerType == "henbatch" &&
                            hb.ParentId == null &&
                            !hb.Deleted &&
                            hb.Active &&
                            hb.OpeningDate.HasValue);

            // Filter by hen stage if provided
            if (henStage.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.HenStage == henStage.Value);
            }

            if (filters != null &&
                (!string.IsNullOrEmpty(filters.Regional) ||
                 !string.IsNullOrEmpty(filters.Unit) ||
                 !string.IsNullOrEmpty(filters.Supervisor) ||
                 !string.IsNullOrEmpty(filters.Extensionist) ||
                 !string.IsNullOrEmpty(filters.Productor)))
            {
                henBatchesQuery = henBatchesQuery
                    .Include(hb => hb.Farm)
                        .ThenInclude(f => f.Company)
                            .ThenInclude(c => c.Regional);

                if (!string.IsNullOrEmpty(filters.Regional))
                {
                    henBatchesQuery = henBatchesQuery
                        .Where(hb => hb.Farm.Company.RegionalId.ToString() == filters.Regional);
                }

                if (!string.IsNullOrEmpty(filters.Unit))
                {
                    henBatchesQuery = henBatchesQuery
                        .Where(hb => hb.Farm.CompanyId.ToString() == filters.Unit);
                }

                if (!string.IsNullOrEmpty(filters.Supervisor))
                {
                    henBatchesQuery = henBatchesQuery
                        .Where(hb => hb.Farm.SupervisorId.ToString() == filters.Supervisor);
                }

                if (!string.IsNullOrEmpty(filters.Extensionist))
                {
                    henBatchesQuery = henBatchesQuery
                        .Where(hb => hb.Farm.TechnicianId.ToString() == filters.Extensionist);
                }

                if (!string.IsNullOrEmpty(filters.Productor))
                {
                    henBatchesQuery = henBatchesQuery
                        .Where(hb => hb.FarmId.ToString() == filters.Productor);
                }
            }

            // No need to filter by DayOfWeek as it's a non-nullable enum

            return henBatchesQuery.Select(hb => hb.Id).ToList();
        }

        public async Task<List<FeedStockIndicatorDTO>> GetFeedStockIndicators(FilterStockCardsDTO filters)
        {
            try
            {
                // Get containers based on filters
                var query = containerService.GetAll()
                    .Include(c => c.MaterialContainers)
                        .ThenInclude(mc => mc.Material)
                            .ThenInclude(m => m.CapacityUnit)
                    .Include(c => c.MaterialContainers)
                        .ThenInclude(mc => mc.Material)
                            .ThenInclude(m => m.MaterialType)
                    .Include(c => c.Farm)
                        .ThenInclude(f => f.Company)
                            .ThenInclude(c => c.Regional)
                    .AsQueryable();

                // Apply filters
                query = HandleFilters(query, filters);

                // Check if we have any containers after filtering
                if (!query.Any())
                {
                    // If materialType filter was applied and no results, try without it
                    if (!string.IsNullOrEmpty(filters.MaterialType))
                    {
                        filters.MaterialType = null;
                        query = containerService.GetAll()
                            .Include(c => c.MaterialContainers)
                                .ThenInclude(mc => mc.Material)
                                    .ThenInclude(m => m.CapacityUnit)
                            .Include(c => c.MaterialContainers)
                                .ThenInclude(mc => mc.Material)
                                    .ThenInclude(m => m.MaterialType)
                            .Include(c => c.Farm)
                                .ThenInclude(f => f.Company)
                                    .ThenInclude(c => c.Regional);

                        query = HandleFilters(query, filters);

                        if (!query.Any())
                        {
                            return new List<FeedStockIndicatorDTO>();
                        }
                    }
                    else
                    {
                        return new List<FeedStockIndicatorDTO>();
                    }
                }

                // Get filtered containers
                var containers = await query.ToListAsync();

                if (!containers.Any())
                {
                    return new List<FeedStockIndicatorDTO>();
                }

                // Get hen batch IDs for consumption calculation
                var henBatchIds = GetHenBatchIds(filters, filters.HenStage);
                if (!henBatchIds.Any())
                {

                    // Create indicators with just stock data, but only for food materials
                    var stockOnlyIndicators = containers
                        .SelectMany(c => c.MaterialContainers.Select(mc => new
                        {
                            MaterialContainer = mc,
                            ProducerName = c.Farm?.Name ?? "Unknown"
                        }))
                        .Where(x => x.MaterialContainer.Quantity > 0 &&
                               x.MaterialContainer.Material.CapacityUnitId == FoodCapacityUnitId &&
                               (string.IsNullOrEmpty(filters.Material) || x.MaterialContainer.Material.InternalId == filters.Material))
                        .GroupBy(x => new { x.MaterialContainer.MaterialId, x.MaterialContainer.Material.Name, x.MaterialContainer.Material.CapacityUnit.Symbol, x.ProducerName })
                        .Select(g => new FeedStockIndicatorDTO
                        {
                            MaterialId = g.Key.MaterialId,
                            MaterialName = g.Key.Name,
                            TotalStock = g.Sum(x => x.MaterialContainer.Quantity),
                            Unity = g.Key.Symbol ?? "kg",
                            DailyConsumption = 0,
                            DaysRemaining = 999, // No consumption data, so show as "many days"
                            AvailableUntilDate = DateTime.Now.AddYears(1), // Far future date
                            ProducerName = g.Key.ProducerName
                        })
                        .Where(dto => dto.TotalStock > 0) // Filter out zero stock
                        .ToList();

                    // Apply sorting to stock only indicators
                    if (!string.IsNullOrEmpty(filters.SortBy))
                    {
                        stockOnlyIndicators = filters.SortBy.ToLower() switch
                        {
                            "days" => stockOnlyIndicators.OrderBy(m => m.DaysRemaining).ToList(),
                            "material" => stockOnlyIndicators.OrderBy(m => m.MaterialName).ToList(),
                            "stock" => stockOnlyIndicators.OrderByDescending(m => m.TotalStock).ToList(),
                            _ => stockOnlyIndicators
                        };
                    }

                    return stockOnlyIndicators;
                }

                // Get materials being consumed from GAD Planner
                var gadMaterials = new HashSet<Guid>();

                // Get all food materials from containers first (with capacity unit ID for food)
                var foodMaterialIds = containers
                    .SelectMany(c => c.MaterialContainers)
                    .Where(mc => mc.Quantity > 0 && mc.Material.CapacityUnitId == FoodCapacityUnitId)
                    .Select(mc => mc.MaterialId)
                    .Distinct()
                    .ToList();

                // For each hen batch, get the GAD data to find materials being consumed
                if (henBatchIds.Any())
                {
                    try
                    {
                        // Get a sample hen batch to check GAD data
                        var sampleHenBatchId = henBatchIds.First();

                        // Get female GAD data
                        var femaleGadData = gadPlannerBusinessLogic.GetGADFemale(sampleHenBatchId, new Range(1, 50));

                        // Get male GAD data
                        var maleGadData = gadPlannerBusinessLogic.GetGADMale(sampleHenBatchId, new Range(1, 50));

                        // If we can get GAD data, use all food materials
                        foreach (var materialId in foodMaterialIds)
                        {
                            gadMaterials.Add(materialId);
                        }
                    }
                    catch
                    {
                        // If we can't get GAD data, still use all food materials
                        foreach (var materialId in foodMaterialIds)
                        {
                            gadMaterials.Add(materialId);
                        }
                    }
                }
                else
                {
                    // If no hen batches, still use all food materials
                    foreach (var materialId in foodMaterialIds)
                    {
                        gadMaterials.Add(materialId);
                    }
                }

                // Get consumption data from the last closed week
                var consumptions = GetLastClosedWeekConsumptions(henBatchIds, filters);

                // If no consumption data, still show stock but with zero consumption
                if (!consumptions.Any())
                {
                    // Create indicators with just stock data, but only for materials in GAD Planner
                    var stockOnlyIndicators = containers
                        .SelectMany(c => c.MaterialContainers.Select(mc => new
                        {
                            MaterialContainer = mc,
                            ProducerName = c.Farm?.Name ?? "Unknown"
                        }))
                        .Where(x => x.MaterialContainer.Quantity > 0 &&
                               gadMaterials.Contains(x.MaterialContainer.MaterialId) &&
                               (string.IsNullOrEmpty(filters.Material) || x.MaterialContainer.Material.InternalId == filters.Material))
                        .GroupBy(x => new { x.MaterialContainer.MaterialId, x.MaterialContainer.Material.Name, x.MaterialContainer.Material.CapacityUnit.Symbol, x.ProducerName })
                        .Select(g => new FeedStockIndicatorDTO
                        {
                            MaterialId = g.Key.MaterialId,
                            MaterialName = g.Key.Name,
                            TotalStock = g.Sum(x => x.MaterialContainer.Quantity),
                            Unity = g.Key.Symbol ?? "kg",
                            DailyConsumption = 0,
                            DaysRemaining = 999, // No consumption data, so show as "many days"
                            AvailableUntilDate = DateTime.Now.AddYears(1), // Far future date
                            ProducerName = g.Key.ProducerName
                        })
                        .Where(dto => dto.TotalStock > 0)
                        .ToList();

                    if (!string.IsNullOrEmpty(filters.SortBy))
                    {
                        stockOnlyIndicators = filters.SortBy.ToLower() switch
                        {
                            "days" => stockOnlyIndicators.OrderBy(m => m.DaysRemaining).ToList(),
                            "material" => stockOnlyIndicators.OrderBy(m => m.MaterialName).ToList(),
                            "stock" => stockOnlyIndicators.OrderByDescending(m => m.TotalStock).ToList(),
                            _ => stockOnlyIndicators
                        };
                    }

                    return stockOnlyIndicators;
                }

                var materialStocks = GetMaterialStockConsumption(query, consumptions, filters.SortBy);

                var feedStockIndicators = new List<FeedStockIndicatorDTO>();

                foreach (var stock in materialStocks)
                {
                    if (gadMaterials.Contains(stock.MaterialId) &&
                        (string.IsNullOrEmpty(filters.Material) ||
                         containerService.GetAll()
                            .SelectMany(c => c.MaterialContainers)
                            .Any(mc => mc.MaterialId == stock.MaterialId &&
                                  mc.Material.InternalId == filters.Material)))
                    {
                        var daysRemaining = stock.ConsumptionDailyAVG > 0
                            ? Math.Round(stock.TotalStock / stock.ConsumptionDailyAVG, 2)
                            : 999;

                        var indicator = new FeedStockIndicatorDTO
                        {
                            MaterialId = stock.MaterialId,
                            MaterialName = stock.MaterialName,
                            TotalStock = stock.TotalStock,
                            Unity = stock.Unity,
                            DailyConsumption = stock.ConsumptionDailyAVG,
                            DaysRemaining = daysRemaining,
                            AvailableUntilDate = DateTime.Now.AddDays((double)daysRemaining),
                            ProducerName = stock.ProducerName // Add producer name
                        };

                        feedStockIndicators.Add(indicator);
                    }
                }

                feedStockIndicators = feedStockIndicators.Where(m => m.TotalStock > 0).ToList();

                if (!string.IsNullOrEmpty(filters.SortBy))
                {
                    feedStockIndicators = filters.SortBy.ToLower() switch
                    {
                        "days" => feedStockIndicators.OrderBy(m => m.DaysRemaining).ToList(),
                        "material" => feedStockIndicators.OrderBy(m => m.MaterialName).ToList(),
                        "stock" => feedStockIndicators.OrderByDescending(m => m.TotalStock).ToList(),
                        _ => feedStockIndicators
                    };
                }

                return feedStockIndicators;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    throw;

                return new List<FeedStockIndicatorDTO>();
            }
        }

        public List<HenBatchConsumptionDTO> GetLastClosedWeekConsumptions(List<Guid> henBatchIds, FilterStockCardsDTO filters = null, HenStage? henStage = null)
        {
            try
            {
                if (henBatchIds == null || !henBatchIds.Any())
                {
                    return new List<HenBatchConsumptionDTO>();
                }

                var lastClosedWeekConsumptions = new List<HenBatchConsumptionDTO>();


                // Get the current date and week
                var currentDate = DateTime.Now;

                // Get parent HenBatchPerformance records (ParentId is null)
                var parentPerformancesQuery = henBatchPerformanceService.GetAll()
                    .Where(hp => henBatchIds.Contains(hp.HenBatchId) && hp.ParentId == null);

                // Filter by hen stage if provided
                if (henStage.HasValue)
                {
                    parentPerformancesQuery = parentPerformancesQuery
                        .Where(hp => hp.HenBatch.HenStage == henStage.Value);
                }

                var parentPerformances = parentPerformancesQuery
                    .OrderByDescending(hp => hp.WeekNumber)
                    .ToList();

                if (parentPerformances.Any())
                {
                    var batchGroups = parentPerformances.GroupBy(hp => hp.HenBatchId);

                    foreach (var batchGroup in batchGroups)
                    {
                        var latestPerformance = batchGroup.OrderByDescending(hp => hp.WeekNumber).FirstOrDefault();

                        if (latestPerformance != null)
                        {
                            var weekEndDate = latestPerformance.Date.AddDays(7);
                            var isWeekClosed = weekEndDate < currentDate;

                            if (!isWeekClosed && batchGroup.Count() > 1)
                            {
                                latestPerformance = batchGroup.OrderByDescending(hp => hp.WeekNumber).Skip(1).FirstOrDefault();
                            }

                            var henBatch = henBatchService.Get(latestPerformance.HenBatchId);

                            if (henBatch != null)
                            {
                                // Get materials associated with this batch
                                var batchMaterials = containerService.GetAll()
                                    .Where(c => c.Id == henBatch.Id)
                                    .SelectMany(c => c.MaterialContainers)
                                    .Where(mc => mc.Material.CapacityUnitId == FoodCapacityUnitId)
                                    .Select(mc => mc.MaterialId)
                                    .Distinct()
                                    .ToList();

                                foreach (var materialId in batchMaterials)
                                {
                                    var totalFeedIntake = latestPerformance.FeedIntakeFemale + latestPerformance.FeedIntakeMale;

                                    var dailyConsumption = Math.Round(totalFeedIntake / 7m, 2);

                                    lastClosedWeekConsumptions.Add(new HenBatchConsumptionDTO
                                    {
                                        MaterialId = materialId,
                                        TotalConsumption = totalFeedIntake,
                                        DailyConsumption = dailyConsumption
                                    });
                                }
                            }
                        }
                    }
                }

                if (!lastClosedWeekConsumptions.Any())
                {

                    var endDate = DateTime.Now;
                    var startDate = endDate.AddDays(-7);

                    var shippingNotes = shippingNoteService.GetAll()
                        .Where(sn => sn.DestinationId.HasValue && henBatchIds.Contains(sn.DestinationId.Value) &&
                               sn.Date >= startDate && sn.Date <= endDate &&
                               (sn.Name.Contains("ConsumedFoodStep") || sn.Name.Contains("ConsumedFoodFemaleStep") || sn.Name.Contains("ConsumedFoodMaleStep")))
                        .Include(sn => sn.MaterialsShipped)
                        .ToList();

                    if (shippingNotes.Any())
                    {
                        var materialConsumptions = shippingNotes
                            .SelectMany(sn => sn.MaterialsShipped)
                            .Where(ms => ms.Material != null && ms.Material.CapacityUnitId == FoodCapacityUnitId)
                            .GroupBy(ms => ms.MaterialId)
                            .Select(g => new HenBatchConsumptionDTO
                            {
                                MaterialId = g.Key,
                                TotalConsumption = g.Sum(ms => ms.Quantity),
                                DailyConsumption = Math.Round(g.Sum(ms => ms.Quantity) / 7m, 2) // Average daily consumption
                            })
                            .ToList();

                        lastClosedWeekConsumptions.AddRange(materialConsumptions);
                    }
                }

                if (!lastClosedWeekConsumptions.Any())
                {
                    // Get all food materials from containers (with capacity unit ID for food)
                    var materials = containerService.GetAll()
                        .SelectMany(c => c.MaterialContainers)
                        .Where(mc => mc.Quantity > 0 && mc.Material.CapacityUnitId == FoodCapacityUnitId)
                        .Select(mc => mc.MaterialId)
                        .Distinct()
                        .ToList();

                    foreach (var materialId in materials)
                    {
                        var stock = containerService.GetAll()
                            .SelectMany(c => c.MaterialContainers)
                            .Where(mc => mc.MaterialId == materialId)
                            .Sum(mc => mc.Quantity);

                        lastClosedWeekConsumptions.Add(new HenBatchConsumptionDTO
                        {
                            MaterialId = materialId,
                            TotalConsumption = Math.Round(stock * 0.07m, 2), // 7% per week
                            DailyConsumption = Math.Round(stock * 0.01m, 2)  // 1% per day
                        });
                    }
                }

                // Aggregate consumption by material ID
                var aggregatedConsumptions = lastClosedWeekConsumptions
                    .GroupBy(c => c.MaterialId)
                    .Select(g => new HenBatchConsumptionDTO
                    {
                        MaterialId = g.Key,
                        TotalConsumption = g.Sum(c => c.TotalConsumption),
                        DailyConsumption = g.Sum(c => c.DailyConsumption)
                    })
                    .ToList();

                return aggregatedConsumptions;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    throw;

                return new List<HenBatchConsumptionDTO>();
            }
        }

        public async Task<IEnumerable<MaterialConsumptionDTO>> GetMaterialStockConsumption(FilterStockCardsDTO filters)
        {
            try
            {
                var query = containerService.GetAll()
                    .Include(c => c.MaterialContainers)
                        .ThenInclude(mc => mc.Material)
                            .ThenInclude(m => m.CapacityUnit)
                    .Include(c => c.Farm)
                        .ThenInclude(f => f.Company)
                            .ThenInclude(c => c.Regional)
                    .AsQueryable();

                // Aplica os filtros
                query = HandleFilters(query, filters);

                // Log após aplicar filtros
                var containers = await query.ToListAsync();

                if (!containers.Any())
                {
                    return Enumerable.Empty<MaterialConsumptionDTO>();
                }

                // Primeiro filtra os MaterialContainers pelo material específico
                var result = containers
                    .SelectMany(c => c.MaterialContainers
                        .Where(mc => string.IsNullOrEmpty(filters.Material) ||
                                   mc.Material.InternalId == filters.Material)
                    )
                    .GroupBy(mc => new
                    {
                        mc.MaterialId,
                        MaterialName = mc.Material.Name,
                        CapacityUnitSymbol = mc.Material.CapacityUnit.Symbol ?? "un"
                    })
                    .Select(g => new MaterialConsumptionDTO
                    {
                        MaterialId = g.Key.MaterialId,
                        Material = g.First().Material,
                        Quantity = g.Sum(mc => mc.Quantity),
                        Date = DateTime.Now
                    });

                // Filter out zero stock
                result = result.Where(r => r.Quantity > 0);

                // Aplica ordenação
                if (!string.IsNullOrEmpty(filters.SortBy))
                {
                    result = filters.SortBy.ToLower() switch
                    {
                        "days" => result.OrderBy(r => r.Quantity),
                        "material" => result.OrderBy(r => r.Material.Name),
                        "stock" => result.OrderByDescending(r => r.Quantity),
                        _ => result
                    };
                }

                return result.ToList();
            }
            catch
            {
                throw;
            }
        }

        public List<MaterialStockConsumptionDTO> GetMaterialStockConsumption(IQueryable<Container> containers, List<HenBatchConsumptionDTO> consumptions, string sortBy = null)
        {
            try
            {
                // Get all materials from containers with their MaterialType and CapacityUnit
                var materialsQuery = containers
                     .Include(c => c.MaterialContainers)
                         .ThenInclude(mc => mc.Material)
                             .ThenInclude(m => m.CapacityUnit)
                    .SelectMany(c => c.MaterialContainers)
                    .Where(mc => mc.Material.CapacityUnitId == FoodCapacityUnitId)
                    .Select(mc => new
                    {
                        mc.MaterialId,
                        mc.Material.Name,
                        Stock = mc.Quantity,
                        Unity = mc.Material.CapacityUnit.Symbol ?? "un"
                    });

                var materials = materialsQuery.ToList();

                if (!materials.Any())
                    return new List<MaterialStockConsumptionDTO>();

                // Get all containers with their farms for producer names
                var containersWithFarms = containers
                    .Include(c => c.Farm)
                    .ToList();

                // Group materials by container to get producer names
                var materialsByContainer = containersWithFarms
                    .SelectMany(c => c.MaterialContainers
                        .Where(mc => mc.Material.CapacityUnitId == FoodCapacityUnitId)
                        .Select(mc => new
                        {
                            mc.MaterialId,
                            mc.Material.Name,
                            Stock = mc.Quantity,
                            Unity = mc.Material.CapacityUnit.Symbol ?? "un",
                            ProducerName = c.Farm?.Name ?? "Unknown"
                        })
                    )
                    .ToList();

                var materialStocks = materialsByContainer
                    .GroupBy(m => new { m.MaterialId, m.Name, m.Unity, m.ProducerName })
                    .Select(g => new MaterialStockConsumptionDTO
                    {
                        MaterialId = g.Key.MaterialId,
                        MaterialName = g.Key.Name,
                        TotalStock = g.Sum(m => m.Stock),
                        Unity = g.Key.Unity,
                        ConsumptionDailyAVG = 0,
                        DaysRemaining = 999,
                        ProducerName = g.Key.ProducerName
                    })
                    .Where(dto => dto.TotalStock > 0)
                    .ToList();

                foreach (var material in materialStocks)
                {
                    var materialConsumptions = consumptions
                        .Where(c => c.MaterialId == material.MaterialId)
                        .ToList();

                    if (materialConsumptions.Any())
                    {

                        var totalWeeklyConsumption = materialConsumptions.Sum(c => c.TotalConsumption);

                        material.ConsumptionDailyAVG = Math.Round(totalWeeklyConsumption / 7m, 2);

                        if (material.ConsumptionDailyAVG > 0)
                        {
                            material.DaysRemaining = Math.Round(material.TotalStock / material.ConsumptionDailyAVG, 2);
                        }
                        else
                        {
                            material.DaysRemaining = 999;
                        }
                    }
                    else
                    {
                        material.ConsumptionDailyAVG = 0;
                        material.DaysRemaining = 999;
                    }
                }

                // Filter out zero stock
                materialStocks = materialStocks.Where(m => m.TotalStock > 0).ToList();

                if (!string.IsNullOrEmpty(sortBy))
                {
                    materialStocks = sortBy.ToLower() switch
                    {
                        "days" => materialStocks.OrderBy(m => m.DaysRemaining).ToList(),
                        "material" => materialStocks.OrderBy(m => m.MaterialName).ToList(),
                        "stock" => materialStocks.OrderByDescending(m => m.TotalStock).ToList(),
                        _ => materialStocks
                    };
                }

                return materialStocks;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    throw;

                return new List<MaterialStockConsumptionDTO>();
            }
        }

        #endregion
    }
}
