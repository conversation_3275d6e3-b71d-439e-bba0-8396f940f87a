﻿using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Helpers.Excel;
using Binit.Framework.Interfaces.DAL;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using DevExtreme.AspNet.Mvc;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Entities.Model.Views;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.HenBatchDTOs;
using Domain.Logic.DTOs.Export;
using Domain.Logic.Interfaces;
using Domain.Logic.Validations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Quartz;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WebApp.Constants;
using WebApp.Models;
using WebApp.WebTools.Tree;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.HenBatchController;

namespace WebApp.Controllers
{
    [Authorize]
    public class HenBatchController : ContainerController
    {
        private readonly IClusterService clusterService;
        private readonly IFormulaService formulaService;
        private readonly IGeneticBusinessLogic geneticBusinessLogic;
        private readonly IHenBatchBusinessLogic henBatchBusinessLogic;
        private readonly IHenBatcheJobScheduleBusinessLogic henBatcheJobScheduleBusinessLogic;
        private readonly IHenBatchService henBatchService;
        private readonly IHenReportService henReportService;
        private readonly ILineService lineService;
        private readonly IOperationContext operationContext;
        private readonly IHenWarehouseService henWarehouseService;
        private readonly SelectListItemComparer selectListItemComparer;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly ISpikingReceptionReportService spikingReceptionReportService;
        private readonly IHenBatchCategoryService henBatchCategoryService;
        private readonly ICompanyService companyService;
        private readonly ISpreadSheetExportService spreadSheetExportService;

        public HenBatchController(
            IClusterService clusterService,
            IFarmService farmService,
            IFormulaService formulaService,
            IGeneticBusinessLogic geneticBusinessLogic,
            ICapacityUnitService capacityUnitService,
            IContainerService<Container> containerService,
            IHenBatchBusinessLogic henBatchBusinessLogic,
            IHenBatcheJobScheduleBusinessLogic henBatcheJobScheduleBusinessLogic,
            IHenReportService henReportService,
            IHenBatchService henBatchService,
            ILineService lineService,
            IStringLocalizer<SharedResources> localizer,
            IMaterialTypeService materialTypeService,
            IOperationContext operationContext,
            IHenWarehouseService henWarehouseService,
            IService<TenantConfiguration> tenantConfigurationService,
            ISpikingReceptionReportService spikingReceptionReportService,
            IHenBatchCategoryService henBatchCategoryService,
            ICompanyService companyService,
            ISpreadSheetExportService spreadSheetExportService) : base(
                capacityUnitService,
                containerService,
                farmService,
                materialTypeService,
                localizer)
        {
            this.clusterService = clusterService;
            this.formulaService = formulaService;
            this.geneticBusinessLogic = geneticBusinessLogic;
            this.henBatchBusinessLogic = henBatchBusinessLogic;
            this.henBatcheJobScheduleBusinessLogic = henBatcheJobScheduleBusinessLogic;
            this.henBatchService = henBatchService;
            this.henReportService = henReportService;
            this.lineService = lineService;
            this.operationContext = operationContext;
            this.henWarehouseService = henWarehouseService;
            this.tenantConfigurationService = tenantConfigurationService;
            this.henBatchCategoryService = henBatchCategoryService;
            this.selectListItemComparer = new SelectListItemComparer();
            this.spikingReceptionReportService = spikingReceptionReportService;
            this.companyService = companyService;
            this.spreadSheetExportService = spreadSheetExportService;
        }

        public IActionResult Index(HenStage? henStage = null, string validationError = "")
        {
            if (!this.operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator))
            {
                if (!henStage.HasValue)
                {
                    if (CheckAuthorization(HenStage.Laying, ControllerActions.Index))
                        henStage = HenStage.Laying;
                    else if (CheckAuthorization(HenStage.Breeding, ControllerActions.Index))
                        henStage = HenStage.Breeding;
                    else
                        return Forbid();
                }
                else
                {
                    if (!CheckAuthorization(henStage, ControllerActions.Index))
                        return Forbid();
                }
            }
            if (henStage.HasValue)
                ViewData["HenStage"] = henStage.Value.ToString();
            else
                ViewData["HenStages"] = GetHenStages();

            // These are required in order to localize datatables and other strings.
            ViewData["Title"] = localizer[Lang.IndexTitle] + GetHenStageString(henStage);
            ViewData["DatatableResources"] = JsLocalizer.GetLocalizedResources(JsLang.Datatables, this.localizer);
            ViewData["NoPlaceAvailable"] = localizer[Lang.NoPlaceAvailable].Value;
            ViewData["HenBatchIndexResources"] = JsLocalizer.GetLocalizedResources(JsLang.HenBatchIndex, this.localizer);
            ViewData["Genetics"] = henBatchBusinessLogic.GetGenetics(henStage);
            ViewData["Farms"] = GetAllFarms(henStage);
            ViewData["Clusters"] = GetAllClusters(null, henStage);
            ViewData["Warehouses"] = GetAllHenWarehouses(null, henStage);
            ViewData["Lines"] = GetAllLines(null, henStage);
            ViewData["Formulas"] = GetFormulas(henStage);
            ViewData["URL"] = $"/HenBatch?henStage={henStage}";
            ViewData["TableId"] = $"hen-batch-tree-{henStage}";
            //1) You cannot edit a henbatch that has at least one hen report.
            //2) You need to indicate a henStage before creating a henBatch.
            //3) No place available to create a new henbatch
            if (validationError == "0")
                ViewData["ValidationError"] = localizer[Lang.HenBatchIsFull].Value;
            else if (validationError == "1")
                ViewData["ValidationError"] = localizer[Lang.HasReportsEx].Value;
            else if (validationError == "2")
                ViewData["ValidationError"] = localizer[Lang.NoHenStageEx].Value;
            else if (validationError == "3")
                ViewData["ValidationError"] = localizer[Lang.NoPlaceAvailable].Value;
            else if (validationError == "4")
                ViewData["ValidationError"] = localizer[Lang.NoBirdsToSend].Value;
            else if (!string.IsNullOrEmpty(validationError))
                ViewData["ValidationError"] = validationError;
            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            ViewData["HasClusters"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");
            ViewData["HasSectors"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Sectors && s.Value == "True");
            ViewData["HasHenBatchCategory"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");

            return View();
        }

        #region Get
        [HttpPost]
        public async Task<object> GetTree(DataSourceLoadOptions loadOptions, [FromForm] Dictionary<string, string> customFilters)
        {
            IQueryable<HenBatchGridView> henBatches = henBatchBusinessLogic.GetAllForIndex(customFilters);

            Guid[] availableParents = henBatchBusinessLogic.GetAvailableParents().Select(p => p.Id).ToArray();

            LoadResult response = await DataSourceLoader.LoadAsync(henBatches, loadOptions);

            if (response.data is List<HenBatchGridView>)
            {
                response.data = response.data.Cast<HenBatchGridView>().Select(henBatch =>
                {
                    return new HenBatchTreeNode(henBatch, localizer)
                    {
                        CanDistribute = availableParents.Contains(henBatch.Id)
                    };
                });
            }

            return response;
        }
        #endregion

        public async Task<IActionResult> Export(DataSourceLoadOptions loadOptions, Dictionary<string, string> filters, [FromServices] IScheduler scheduler)
        {
            filters.Add("entity", nameof(HenBatchExport));
            filters.Add("host", Request.Host.Value);
            filters.Add("scheme", Request.Scheme);

            int totalCount = int.Parse(filters["totalCount"]);

            if (totalCount < 2000)
            {
                var (uri, file) = await spreadSheetExportService.Export(loadOptions, filters);
                return Created(uri, new { file });
            }
            else
            {
                string loadOptionsJson = JsonConvert.SerializeObject(loadOptions);
                string filtersJson = JsonConvert.SerializeObject(filters);
                string email = operationContext.GetUsername();

                JobDataMap data = new JobDataMap
                {
                    ["loadOptions"] = loadOptionsJson,
                    ["filters"] = filtersJson,
                    ["email"] = email,
                    ["culture"] = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName

                };
                await scheduler.TriggerJob(new JobKey("ExportJob", "Startup"), data);

                return Accepted(new
                {
                    message = localizer.GetString(
                    Binit.Framework.Localization.LocalizationConstants.DomainLogic.JobScheduler.Jobs.ExportJob.ExportResultMessage,
                    email).Value
                });
            }
        }

        #region Create
        public IActionResult Create(HenStage? henStage)
        {
            if (!henStage.HasValue || !CheckAuthorization(henStage, ControllerActions.Create))
                return Forbid();

            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.CreateTitle] + GetHenStageString(henStage);
            ViewData["Action"] = ControllerActions.Create;
            ViewData["ActiveFormulas"] = GetFormulas();
            ViewData["HasBirds"] = false;
            ViewData["UserLanguage"] = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            ViewData["UserIsAdmin"] = operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator);
            HenBatchViewModel model = new HenBatchViewModel(henStage.Value);

            string initializationError = InitLists(model.ToDTO(), ControllerActions.Create);
            if (!string.IsNullOrEmpty(initializationError))
                return RedirectToAction("Index", "HenBatch", new { henStage, validationError = initializationError });

            AddPreselectedMaterials(model);
            string error = CheckAvailableSpace(henStage);
            if (!string.IsNullOrEmpty(error))
                return RedirectToAction("Index", "HenBatch", new { henStage, validationError = error });

            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            ViewData["HasClusters"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");
            ViewData["HasSectors"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Sectors && s.Value == "True");
            model.TenantAllowsToReceiveBirdsBeforeStartDate = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.AllowToReceiveBirdsBeforeStartDate && s.Value == "True");
            model.HasTenantHenBatchCategory = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");

            return View("CreateOrEdit", model);
        }

        [HttpPost]
        public async Task<IActionResult> Create(HenBatchViewModel henBatch)
        {
            if (!CheckAuthorization(henBatch.HenStage, ControllerActions.Create))
                return Forbid();

            // Check if model is valid
            if (ModelState.IsValid)
            {
                try
                {
                    HenBatch parent = await henBatchBusinessLogic.SetPropertiesAndCreate(henBatch.ToEntities(henBatch.HenStage));
                    await henBatcheJobScheduleBusinessLogic.ScheduleAssignBestPractice(parent);
                    return RedirectToAction("Index", "HenBatch", new { henStage = henBatch.HenStage });
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(string.Empty, error.Value);
                }
            }

            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.CreateTitle] + GetHenStageString(henBatch.HenStage);
            ViewData["Action"] = ControllerActions.Create;
            ViewData["HasBirds"] = false;
            //Setup entities lists for view SelectLists
            HenBatchFilterDTO data = henBatch.ToDTO();
            InitLists(data, ControllerActions.Create, henBatch.CompanyId);
            ViewData["UserIsAdmin"] = operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator);

            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            ViewData["HasClusters"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");
            ViewData["HasSectors"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Sectors && s.Value == "True");
            henBatch.TenantAllowsToReceiveBirdsBeforeStartDate = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.AllowToReceiveBirdsBeforeStartDate && s.Value == "True");
            henBatch.HasTenantHenBatchCategory = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");

            henBatch.ContainerProperties.Code = henBatch.ContainerProperties.Code.Replace(" ", "");

            return View("CreateOrEdit", henBatch);
        }

        private string CheckAvailableSpace(HenStage? henStage)
        {
            IQueryable<Line> lines = lineService.GetAll().Include(l => l.Warehouse).Where(l => !l.HenBatches.Any(hb => hb.DateEnd == null));
            if (henStage.HasValue)
                lines = lines.Where(l => l.Warehouse.HenStage == henStage);
            if (!lines.Any())
                return "3";
            else
                return "";
        }
        private void AddPreselectedMaterials(HenBatchViewModel model)
        {
            //Hen batch pre selected birds as store
            model.ContainerProperties.MaterialTypeActions.Add(new MaterialTypeActionsViewModel()
            {
                ActionEnum = ActionsEnum.Store.ToString(),
                CapacityUnitId = CapacityUnits.Bird.ToString(),
                MaterialType = materialTypeService.Get(MaterialTypes.ActivoBiologicoProductivoAve).DetailedName,
                MaterialTypeId = MaterialTypes.ActivoBiologicoProductivoAve.ToString(),
                Preselected = true
            });

            //Hen batch pre selected formula as consume
            model.ContainerProperties.MaterialTypeActions.Add(new MaterialTypeActionsViewModel()
            {
                ActionEnum = ActionsEnum.Consume.ToString(),
                CapacityUnitId = CapacityUnits.Kilograms.ToString(),
                MaterialType = materialTypeService.Get(MaterialTypes.InsumoMateriaPrimaAlimentacionFormula).DetailedName,
                MaterialTypeId = MaterialTypes.InsumoMateriaPrimaAlimentacionFormula.ToString(),
                Preselected = true
            });
            //Hen batch pre selected healthcare as consume
            model.ContainerProperties.MaterialTypeActions.Add(new MaterialTypeActionsViewModel()
            {
                ActionEnum = ActionsEnum.Consume.ToString(),
                CapacityUnitId = CapacityUnits.Kilograms.ToString(),
                MaterialType = materialTypeService.Get(MaterialTypes.InsumoSanidad).DetailedName,
                MaterialTypeId = MaterialTypes.InsumoSanidad.ToString(),
                Preselected = true
            });

            if (model.HenStage == HenStage.Laying)
            {
                //Hen batch pre selected eggs as produce
                model.ContainerProperties.MaterialTypeActions.Add(new MaterialTypeActionsViewModel()
                {
                    ActionEnum = ActionsEnum.Produce.ToString(),
                    CapacityUnitId = CapacityUnits.Eggs.ToString(),
                    MaterialType = materialTypeService.Get(MaterialTypes.InsumoMateriaPrimaHuevo).DetailedName,
                    MaterialTypeId = MaterialTypes.InsumoMateriaPrimaHuevo.ToString(),
                    Preselected = true
                });
            }
        }

        public IActionResult GetNewDistributionRow(int index, Guid farmId, string formAction, Guid henBatch, HenStage henStage)
        {
            string message;
            try
            {
                DistributionViewModel model = new DistributionViewModel();
                IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
                bool hasClusters = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");
                return ViewComponent("DistributionRow", new { model, index, farm = farmId, action = formAction, henBatch, henStage, hasClusters });
            }
            catch (UserException ex)
            {
                HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = ex.Message;
            }
            catch (Exception)
            {
                HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = localizer[Lang.HandleUnauthorizedEx];
            }

            return new JsonResult(new
            {
                message
            });
        }

        public List<SelectListItem> GetCompanies(Guid? selected = null, HenStage? henStage = null)
        {
            List<SelectListItem> companies = lineService.GetAllWithAvailableSpace(materialTypePaths: MaterialTypePaths.ActivoBiologicoProductivoAve)
                .Where(l => l.Warehouse.Cluster.Farm.Active && (!henStage.HasValue || l.Warehouse.HenStage == henStage) && !l.HenBatches.Any(hb => hb.DateEnd == null))
                .Select(l => new SelectListItem(
                    l.Warehouse.Cluster.Farm.Company.BusinessName,
                    l.Warehouse.Cluster.Farm.CompanyId.ToString(),
                    selected.HasValue && selected.Value == l.Warehouse.Cluster.Farm.CompanyId))
                .ToList()
                .Distinct(selectListItemComparer)
                .OrderBy(c => c.Text).ToList();

            if (selected.HasValue && !companies.Any(i => i.Selected))
            {
                Company selectedCompany = companyService.Get(selected.Value);
                companies.Add(new SelectListItem(selectedCompany.BusinessName, selectedCompany.Id.ToString(), true));
            }

            if (companies.Count() == 1)
                companies.First().Selected = true;
            return companies;
        }

        //Get farms for a given hen stage. If a farm id is indicated, that farm will be selected.
        public List<SelectListItem> GetFarmsByCompany(Guid companyId, HenStage? henStage = null, Guid? selected = null, string action = null)
        {
            IEnumerable<SelectListItem> farms = lineService.GetAllWithAvailableSpace(materialTypePaths: MaterialTypePaths.ActivoBiologicoProductivoAve)
                .Where(l => l.Warehouse.Cluster.Farm.Active && l.Warehouse.Cluster.Farm.CompanyId == companyId && (!henStage.HasValue || l.Warehouse.HenStage == henStage) && !l.HenBatches.Any(hb => hb.DateEnd == null))
                .Select(l => new SelectListItem(l.Warehouse.Cluster.Farm.Code + " | " + l.Warehouse.Cluster.Farm.Name, l.Warehouse.Cluster.FarmId.ToString()))
                .ToList()
                .Distinct(selectListItemComparer)
                .OrderBy(c => c.Text);

            if (farms.Count() == 1)
                farms.First().Selected = true;

            return farms.ToList();
        }

        //Get clusters for a given farm. If a cluster id is indicated, that cluster will be selected.
        public List<SelectListItem> GetClustersByFarm(Guid farmId, HenStage? henStage = null)
        {
            return lineService.GetAllWithAvailableSpace(materialTypePaths: MaterialTypePaths.ActivoBiologicoProductivoAve)
                .Where(l => l.Warehouse.Cluster.FarmId == farmId)
                .Select(l => new SelectListItem(l.Warehouse.Cluster.Name, l.Warehouse.ClusterId.ToString()))
                .ToList()
                .Distinct(selectListItemComparer)
                .OrderBy(c => c.Text)
                .ToList();
        }

        //Get henwarehouses for a given cluster. If a henwarehouse id is indicated, that henwarehouse will be selected.
        public List<SelectListItem> GetHenWarehousesByCluster(Guid clusterId, Guid selected, string action, Guid henBatchId)
        {
            List<SelectListItem> henWarehouses = lineService.GetAllWithAvailableSpace(materialTypePaths: MaterialTypePaths.ActivoBiologicoProductivoAve)
                .Where(l => l.Warehouse.ClusterId == clusterId)
                .Select(l => new SelectListItem(l.Warehouse.Name, l.WarehouseId.ToString(), l.WarehouseId == selected))
                .ToList();

            if (action != ControllerActions.Create)
                henWarehouses.AddRange(GetCurrentHenWarehouses(henBatchId, clusterId, selected));

            return henWarehouses
                .Distinct(selectListItemComparer)
                .OrderBy(c => c.Text)
                .ToList();
        }

        //Get current henwarehouses in use in henbatches for use in update
        public List<SelectListItem> GetCurrentHenWarehouses(Guid henBatchId, Guid clusterId, Guid selected)
        {
            return henBatchService
                .GetAll()
                .Where(hb => (hb.Id == henBatchId || hb.ParentId == henBatchId) && hb.Line.Warehouse.ClusterId == clusterId)
                .Select(hb => new SelectListItem(
                    hb.Line.Warehouse.Name,
                    hb.Line.WarehouseId.ToString(),
                    hb.Line.WarehouseId == selected))
                .ToList()
                .Distinct(selectListItemComparer)
                .OrderBy(c => c.Text)
                .ToList();
        }

        //Get lines for a given henwarehouse. If a line id is indicated, that line will be selected.
        public List<SelectListItem> GetLinesByHenWarehouse(Guid warehouseId, Guid selected, string action, Guid henBatchId)
        {
            List<SelectListItem> lines = lineService.GetHenWarehouseLines(warehouseId)
                .Select(l => new SelectListItem(l.Name, l.Id.ToString(), l.Id == selected))
                .ToList();

            if (action != ControllerActions.Create)
                lines.AddRange(GetCurrentLines(henBatchId, warehouseId, selected));

            if (lines.Count() == 1)
                lines.First().Selected = true;

            return lines
                .Distinct(selectListItemComparer)
                .OrderBy(c => c.Text)
                .ToList();
        }

        //Get current lines in use in henbatches for use in update
        public List<SelectListItem> GetCurrentLines(Guid henBatchId, Guid henWarehouseId, Guid selected)
        {
            var lines = henBatchService
                .GetAll()
                .Where(hb => (hb.Id == henBatchId || hb.ParentId == henBatchId) && hb.Line.WarehouseId == henWarehouseId)
                .Select(hb => hb.Line)
                .Distinct();

            return lines.Select(c => new SelectListItem(c.Name, c.Id.ToString(), c.Id == selected || lines.Count() == 1)).ToList();
        }
        #endregion

        #region Edit
        [HttpGet]
        public IActionResult Edit(string id)
        {
            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.EditTitle];
            ViewData["Action"] = ControllerActions.Edit;

            // Get henBatch from database
            HenBatch henBatch = this.henBatchService.GetFull(new Guid(id));
            if (!CheckAuthorization(henBatch.HenStage, ControllerActions.Edit))
                return Forbid();

            ViewData["HasBirds"] = henBatch.InitialHenAmount > 0 || henBatch.InitialHenAmountFemale > 0 || henBatch.InitialHenAmountMale > 0;
            HenBatchViewModel model = new HenBatchViewModel(henBatch, GetChildren(henBatch.Id), localizer);
            InitLists(model.ToDTO(), ControllerActions.Edit, model.CompanyId);
            model = SetMaterialTypes(model);
            ViewData["UserIsAdmin"] = operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator);

            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            ViewData["HasClusters"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");
            ViewData["HasSectors"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Sectors && s.Value == "True");
            model.TenantAllowsToReceiveBirdsBeforeStartDate = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.AllowToReceiveBirdsBeforeStartDate && s.Value == "True");
            model.HasTenantHenBatchCategory = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");

            return View("CreateOrEdit", model);
        }

        public HenBatchViewModel SetMaterialTypes(HenBatchViewModel model)
        {

            if (model.ContainerProperties.MaterialTypeActions.Any(mt => mt.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve.ToString()))
                model.ContainerProperties.MaterialTypeActions.FirstOrDefault(mt => mt.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve.ToString()).Preselected = true;

            if (model.ContainerProperties.MaterialTypeActions.Any(mt => mt.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaHuevo.ToString()))
                model.ContainerProperties.MaterialTypeActions.FirstOrDefault(mt => mt.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaHuevo.ToString()).Preselected = true;

            if (model.ContainerProperties.MaterialTypeActions.Any(mt => mt.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaAlimentacionFormula.ToString()))
                model.ContainerProperties.MaterialTypeActions.FirstOrDefault(mt => mt.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaAlimentacionFormula.ToString()).Preselected = true;

            if (model.ContainerProperties.MaterialTypeActions.Any(mt => mt.MaterialTypeId == MaterialTypes.InsumoSanidad.ToString()))
                model.ContainerProperties.MaterialTypeActions.FirstOrDefault(mt => mt.MaterialTypeId == MaterialTypes.InsumoSanidad.ToString()).Preselected = true;

            return model;
        }

        // Returns children henbatches for henbatch with multiple distributions.
        private List<HenBatch> GetChildren(Guid parentId) => henBatchService.GetChildren(parentId).OrderBy(hb => hb.Line.Warehouse.Name).ThenBy(hb => hb.Line.Name).ToList();

        [HttpPost]
        public async Task<IActionResult> Edit(HenBatchViewModel henBatch)
        {
            // Check if model is valid
            if (ModelState.IsValid)
            {
                try
                {
                    await henBatchBusinessLogic.SetPropertiesAndUpdate(henBatch.ToEntities(henBatch.HenStage));
                    return RedirectToAction("Index", "HenBatch", new { henStage = henBatch.HenStage });
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(string.Empty, error.Value);
                }
            }

            // Setup View Title and Model
            ViewData["Title"] = localizer[Lang.EditTitle];
            ViewData["Action"] = "Edit";

            ViewData["HasBirds"] = henBatch.InitialAmountFemale > 0 || henBatch.InitialAmountMale > 0;

            HenBatchFilterDTO data = henBatch.ToDTO();
            data.EggColor = henBatch.EggColor;

            InitLists(data, ControllerActions.Edit, henBatch.CompanyId);

            ViewData["UserIsAdmin"] = operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator);

            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            ViewData["HasClusters"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");
            ViewData["HasSectors"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Sectors && s.Value == "True");
            henBatch.TenantAllowsToReceiveBirdsBeforeStartDate = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.AllowToReceiveBirdsBeforeStartDate && s.Value == "True");
            henBatch.HasTenantHenBatchCategory = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");

            return View("CreateOrEdit", henBatch);
        }
        #endregion

        #region Details
        public IActionResult Details(string id)
        {
            // Get henBatch from database
            HenBatch henBatch = this.henBatchService.GetFull(new Guid(id));

            if (!CheckAuthorization(henBatch.HenStage, ControllerActions.Details))
                return Forbid();

            ViewData["ActionsEnums"] = GetActions();
            ViewData["CapacityUnits"] = GetCapacityUnits();
            ViewData["Title"] = localizer[Lang.DetailsTitle] + GetHenStageString(henBatch.HenStage);
            ViewData["MaterialTypes"] = GetMaterialTypeTree(ContainerTypes.HenBatch);
            ViewData["Action"] = ControllerActions.Details;
            List<HenBatch> children = GetChildren(henBatch.Id);
            List<Guid> childrenId = children.Select(c => c.Id).ToList();
            List<SpikingReceptionReport> spikingReports = spikingReceptionReportService.GetAllWithShippingNotesAndMaterials()
                                                                                        .Where(sr => sr.ShippingNotes.Any(sn => childrenId.Contains(sn.DestinationId.Value) || Guid.Parse(id) == sn.DestinationId.Value))
                                                                                        .ToList();
            HenBatchViewModel model = new HenBatchViewModel(henBatch, children, localizer, spikingReports);
            int index = 0;
            foreach (SpikingReceptionReport sr in spikingReports)
            {
                HenBatch henBatchSpiking = this.henBatchService.GetAll()
                    .Include(hb => hb.Line).ThenInclude(l => l.Warehouse)
                    .Where(hb => hb.Id == sr.ShippingNotes.FirstOrDefault().DestinationId).FirstOrDefault();
                model.SpikingReports[index].Destination = $"{henBatchSpiking.Line.Warehouse.Name} | {henBatchSpiking.Line.Name}";
                index++;
            }

            ViewData["ActiveFormulas"] = GetFormulas(model.HenStage, model.FormulasIds);
            ViewData["HasFirstProductionDate"] = model.FirstProductionDate != null;
            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            ViewData["HasClusters"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");
            ViewData["HasSectors"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Sectors && s.Value == "True");
            model.HasTenantHenBatchCategory = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");

            // Return view with henBatch info
            return View(model);
        }
        #endregion

        #region Delete
        public async Task<JsonResult> Delete(Guid id)
        {
            if (!CheckAuthorizationAsAdministrator(""))
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                return new JsonResult(new
                {
                    localizer[Lang.HandleUnauthorizedEx].Value
                });
            }
            string message;
            try
            {
                await henBatchBusinessLogic.DeleteAsync(id, deleteDistributions: true);
                message = localizer[Lang.DeleteSuccess];
            }
            catch (NotFoundException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                message = ex.Message;
            }
            catch (ValidationException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = string.Join(" | ", ex.Errors.Values);
            }
            catch (UserException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = ex.Message;
            }
            catch (Exception)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = localizer[Lang.DeleteUnexpectedError];
            }

            return new JsonResult(new
            {
                message
            });
        }
        #endregion

        #region Close
        /// <summary>
        /// Cierra el lote activo. Establace el timestamp actual como fecha de cierre.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Close(Guid id, string reasonInput, string closingDateInput)
        {
            string message;
            try
            {
                if (string.IsNullOrEmpty(reasonInput))
                    throw new ValidationException(localizer[Lang.ReasonInputRequiredError]);
                if (string.IsNullOrEmpty(closingDateInput))
                    throw new ValidationException(localizer[Lang.ClosingDateRequiredError]);

                HenBatch henBatch = henBatchService.Get(id);

                if (!CheckAuthorizationAsAdministrator("", henBatch.HenStage))
                    return new JsonResult(new
                    {
                        localizer[Lang.HandleUnauthorizedEx].Value
                    });

                DateTime closingDate = DateTime.Parse(closingDateInput);
                BusinessValidationResult<HenBatch> response = await henBatchBusinessLogic.CloseAsync(id, reasonInput, closingDate);
                if (response.Validations.Any())
                {
                    return new JsonResult(new
                    {
                        validations = string.Join(" | ", response.Validations.Select(v => v.Validation))
                    });
                }
                message = localizer[Lang.CloseSuccess];
            }
            catch (NotFoundException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                message = ex.Message;
            }
            catch (ValidationException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = ex.Message;
            }
            catch (UserException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = ex.Message;
            }
            catch (Exception)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = localizer[Lang.DeleteUnexpectedError];
            }

            return new JsonResult(new
            {
                Message = message
            });
        }
        #endregion

        #region Bird movement
        /// <summary>
        /// Gestión del movimiento de aves.
        /// </summary>
        public IActionResult MoveBirds(HenStage henStage, string originId)
        {
            if (!CheckAuthorizationAsAdministrator("MoveBirds", henStage))
                return Forbid();

            HenBatch origin = henBatchService.Get(new Guid(originId));

            ViewData["Title"] = localizer[Lang.MoveBirdsTitle, origin.Code];
            ViewData["HenBatchMoveBirds"] = JsLocalizer.GetLocalizedResources(JsLang.HenBatchMoveBirds, this.localizer);
            ViewData["Origins"] = GetHenBatchesWithStock(origin.ParentId.Value, origin.Id);

            List<SelectListItem> destinations = GetHenBatchesWithAvailableCapacity(origin.Id);
            ViewData["Destinations"] = destinations;

            return View(new BirdMovementDTO()
            {
                Date = DateTime.Now,
                HenStage = henStage,
                HenAmountFemaleOrigin = origin.HenAmountFemale,
                HenAmountMaleOrigin = origin.HenAmountMale
            });
        }

        [HttpPost]
        public async Task<IActionResult> MoveBirds(BirdMovementDTO dto)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await henBatchBusinessLogic.MoveBirds(dto);
                    henBatchBusinessLogic.UpdateInitialHenAmount(dto);
                    return RedirectToAction("Index", "HenBatch", new { henStage = dto.HenStage });
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError("Validations", error.Value);
                }
            }
            HenBatch origin = henBatchService.Get(dto.OriginId.Value);
            ViewData["Title"] = localizer[Lang.MoveBirdsTitle, origin.Code];
            ViewData["Origins"] = GetHenBatchesWithStock(origin.ParentId.Value, origin.Id);
            ViewData["Destinations"] = GetHenBatchesWithAvailableCapacity(origin.Id, dto.DestinationId.Value);
            ViewData["HenBatchMoveBirds"] = JsLocalizer.GetLocalizedResources(JsLang.HenBatchMoveBirds, this.localizer);

            return View("MoveBirds", dto);
        }

        public IActionResult Redistribute(Guid id, HenStage henStage)
        {
            if (!CheckAuthorizationAsAdministrator("Redistribute", henStage))
                return Forbid();

            RedistributeViewModel model = RedistributeViewModel.MapQuery(henBatchService.GetAll().Where(hb => hb.ParentId == id));
            ViewData["Title"] = localizer.GetString(Lang.MoveBirdsTitle, model.HenBatchCode);
            ViewData["Messages"] = JsLocalizer.GetLocalizedResources(JsLang.HenBatchRedistribute, localizer);

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Redistribute(HenRedistributionsDTO redistribution)
        {
            string message = string.Empty;
            try
            {
                await henBatchBusinessLogic.RedistributeHen(redistribution);
                message = localizer[Lang.RedistributeSuccess];
            }
            catch (ValidationException ex)
            {
                this.HttpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
                foreach (KeyValuePair<string, string> error in ex.Errors)
                    message += $"{error.Value}{Environment.NewLine}";
            }
            catch (Exception ex)
            {
                this.HttpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
                message = ex.Message;
            }
            return Json(new { message });
        }

        public IActionResult GetRedistributionTable(Guid id)
        {
            (List<string> headers, List<Dictionary<string, string>> table) = henBatchBusinessLogic.GetDistributionQuantitiesTable(id);

            return Ok(new { headers, table });
        }

        List<SelectListItem> GetHenBatchesWithStock(Guid parent, Guid origin)
        {
            IQueryable<HenBatch> henBatches = henBatchService.GetAll().Where(hb => hb.ParentId == parent
                                                    && !hb.DateEnd.HasValue
                                                    && hb.HenAmountFemale + hb.HenAmountMale > 0);

            return henBatches.OrderBy(hb => hb.DetailedName)
                                .Select(hb => new SelectListItem(hb.DetailedName, hb.Id.ToString(), hb.Id == origin)).ToList();
        }

        [HttpGet]
        public List<SelectListItem> GetHenBatchesWithAvailableCapacity(Guid originId, Guid? destinationId = null)
        {
            HenBatch origin = henBatchService.Get(originId);
            IQueryable<HenBatch> henBatches = henBatchService.GetAll()
                .Where(hb => hb.Id != originId
                    && hb.ParentId == origin.ParentId
                    && !hb.DateEnd.HasValue
                    && (hb.AcceptedMaterialType.FirstOrDefault(am => am.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).CapacityStandarizedValue - hb.HenAmountFemale - hb.HenAmountMale > 0
                    || hb.AcceptedMaterialType.FirstOrDefault(am => am.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).CapacityStandarizedValue == 0));

            List<SelectListItem> henBatchesOptions = henBatches.OrderBy(hb => hb.DetailedName)
                             .Select(hb => new SelectListItem(hb.DetailedName, hb.Id.ToString(), destinationId == hb.Id)).ToList();

            if (henBatchesOptions.Count() == 1)
                henBatchesOptions.First().Selected = true;

            return henBatchesOptions;
        }

        public BirdsAmountDTO GetBirdsAmount(Guid originId, Guid destinationId)
        {
            HenBatch origin = henBatchService.Get(originId);

            return new BirdsAmountDTO()
            {
                HenAmountFemaleOrigin = origin.HenAmountFemale,
                HenAmountMaleOrigin = origin.HenAmountMale,
                CapacityStandarizedValueDestination = (int)henBatchService.GetFull(destinationId).AcceptedMaterialType.FirstOrDefault(am => am.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).CapacityStandarizedValue
            };
        }

        [HttpGet]
        public JsonResult GetHenBatchDTO(Guid id, bool origin = true)
        {
            HenBatch henBatch = origin ? henBatchService.GetWithMaterial(id) : henBatchService.GetFull(id);
            return new JsonResult(new HenBatchDTO(henBatch, origin));
        }

        [HttpGet]
        public List<MovementOptionDTO> GetOptions(Guid id, string intern, bool origin)
        {
            return henBatchBusinessLogic.GetBirdMovementOptions(id, intern, origin);
        }

        [HttpGet]
        public List<SelectListItem> GetMaterialOptions(bool origin = true)
            => henBatchBusinessLogic.GetMaterialOptions(origin);
        #endregion

        #region FirstProductionDate
        [HttpGet]
        public IActionResult FirstProductionDate(Guid id)
        {
            IQueryable<HenBatch> henBatches = henBatchService.GetAll()
                                                        .Include(hb => hb.Line).ThenInclude(hb => hb.Warehouse)
                                                        .Where(hb => hb.Id == id || hb.ParentId == id);
            List<HenWarehouse> henWarehouses = henBatches
                                    .Where(hb => hb.Line != null)
                                    .Select(hb => hb.Line.Warehouse)
                                    .Distinct()
                                    .OrderBy(hb => hb.Name)
                                    .ToList();

            HenBatch henBatch = henBatches.FirstOrDefault();
            List<Guid> henBatchIds = henBatches.Select(hb => hb.Id).ToList();
            FirstProductionDateViewModel firstProductionDateViewModel = new FirstProductionDateViewModel(id, henBatch);
            firstProductionDateViewModel.HenBatchIds = string.Join(" ", henBatchIds);

            foreach (HenWarehouse hw in henWarehouses)
            {
                FirstProductionDateDTO fpdDTO = new FirstProductionDateDTO();
                fpdDTO.HenWarehouse = hw.Name;
                fpdDTO.HenWarehouseId = hw.Id;
                if (henBatches.Any(hb => hb.Line.WarehouseId == hw.Id && hb.FirstProductionDate != null))
                    fpdDTO.Date = henBatches.Where(hb => hb.Line.WarehouseId == hw.Id && hb.FirstProductionDate != null)
                                            .Select(hb => hb.FirstProductionDate)
                                            .FirstOrDefault()
                                            .ToString();
                firstProductionDateViewModel.FirstProductionDateDTOs.Add(fpdDTO);
            }

            // Setup View Title
            ViewData["Title"] = localizer[Lang.FirstProductionDateTitle] + firstProductionDateViewModel.Code;

            return View("FirstProductionDate", firstProductionDateViewModel);
        }

        [HttpPost]
        public async Task<IActionResult> FirstProductionDate(FirstProductionDateViewModel firstProductionDate)
        {
            //// Check if model is valid
            ValidateFirstProductionDate(firstProductionDate);

            if (ModelState.IsValid)
            {
                try
                {
                    DateTime? capitalizationDate = !string.IsNullOrEmpty(firstProductionDate.CapitalizationDate) ? DateTime.Parse(firstProductionDate.CapitalizationDate) : (DateTime?)null;
                    await henBatchBusinessLogic.FirstProductionDate(firstProductionDate.FirstProductionDateDTOs, firstProductionDate.HenBatchId, capitalizationDate);
                    return RedirectToAction("Index", "HenBatch", new { henStage = firstProductionDate.HenStage });
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(error.Key, error.Value);
                }
            }

            // Setup View Title
            ViewData["Title"] = localizer[Lang.FirstProductionDateTitle] + firstProductionDate.Code;

            return View("FirstProductionDate", firstProductionDate);
        }
        #endregion

        #region refresh inputs
        /// <summary>
        /// Gets farms from a henStage to use in hen batch index farm filter.
        /// </summary>
        public List<SelectListItem> GetFarmsByHenStage(HenStage? henStage)
        {
            if (!CheckAuthorization(henStage, ControllerActions.Index))
                return new List<SelectListItem>();
            return henBatchBusinessLogic.GetFarms(henStage);
        }

        #endregion refresh inputs

        #region GetEntities
        public List<SelectListItem> GetAllFarms(HenStage? henStage = null, Guid? farmId = null)
        {
            return farmService.GetAll().Include(f => f.Clusters).ThenInclude(c => c.HenWarehouses)
                .Where(f => !henStage.HasValue || f.Clusters.SelectMany(c => c.HenWarehouses).Any(hw => hw.HenStage == henStage))
                .OrderBy(f => f.Name)
                .Select(f => new SelectListItem(f.Name, f.Id.ToString(), farmId.HasValue && f.Id == farmId))
                .ToList();
        }
        public List<SelectListItem> GetAllClusters(Guid? farmId = null, HenStage? henStage = null, Guid? clusterId = null)
        {
            return clusterService.GetAll()
                .Where(c => (!farmId.HasValue || c.FarmId == farmId) && (!henStage.HasValue || c.HenWarehouses.Any(hw => hw.HenStage == henStage)))
                .OrderBy(c => c.Name)
                .Select(c => new SelectListItem(c.Name, c.Id.ToString(), c.Id == clusterId))
                .ToList();
        }
        public List<SelectListItem> GetAllHenWarehouses(Guid? clusterId = null, HenStage? henStage = null, Guid? henWarehouseId = null)
        {
            return henWarehouseService.GetAll()
                .Where(hw => (!clusterId.HasValue || hw.ClusterId == clusterId) && (!henStage.HasValue || hw.HenStage == henStage))
                .OrderBy(hw => hw.Name)
                .Select(hw => new SelectListItem(hw.Name, hw.Id.ToString(), hw.Id == henWarehouseId))
                .ToList();
        }
        public List<SelectListItem> GetAllLines(Guid? henWarehouseId = null, HenStage? henStage = null, Guid? lineId = null)
        {
            return lineService.GetAll()
                .Where(l => (!henWarehouseId.HasValue || l.WarehouseId == henWarehouseId) && (!henStage.HasValue || l.Warehouse.HenStage == henStage))
                .OrderBy(l => l.DetailedName)
                .Select(l => new SelectListItem(l.DetailedName, l.Id.ToString(), l.Id == lineId))
                .ToList();
        }
        //Get hen warehouses for a given cluster. If a hen warehouse id is indicated, that one will be selected.
        public List<SelectListItem> GetHenWarehouses(HenStage? henStage = null, Guid? clusterId = null, Guid? henWarehouseId = null)
        {
            IQueryable<HenWarehouse> henWarehouses = henBatchBusinessLogic.GetAvailableHenWarehouses(henStage, clusterId);

            return henWarehouses.Select(c => new SelectListItem(c.Name, c.Id.ToString(), c.Id == henWarehouseId || henWarehouses.Count() == 1)).ToList();
        }

        //Get lines for a given hen warehouse. If a line id is indicated, that one will be selected.
        public List<SelectListItem> GetLines(Guid? henWarehouseId, Guid? lineId = null, bool includePlaceholder = true)
        {
            IQueryable<Line> lines = henBatchBusinessLogic.GetAvailableLines(henWarehouseId);

            if (!lines.Any())
                return new List<SelectListItem>();

            List<SelectListItem> options = new List<SelectListItem>();

            if (includePlaceholder)
                options.Add(new SelectListItem(localizer[Lang.PlaceholderSelectLine], "", true));

            if (lines.Count() == 1)
            {
                //If there is only one line available, it is already selected.
                Line onlyOption = lines.FirstOrDefault();
                options.Add(new SelectListItem(onlyOption.Name, onlyOption.Id.ToString(), true));
            }
            else
                options.AddRange(lines.Select(c => new SelectListItem(c.Name, c.Id.ToString(), c.Id == lineId)));
            return options;
        }

        public Tuple<List<int>, DateTime, bool, string> GetCalendarSettings(Guid farmId)
        {
            //GetDisabledWeekDays:
            bool isForOpeningDate = tenantConfigurationService.GetAll().Where(tc => tc.TenantId == operationContext.GetUserTenantId())
                .Any(tc => tc.TenantConfigurationEnum == TenantConfigurationEnum.AllowToReceiveBirdsBeforeStartDate && tc.Value == "True");
            (List<int> week, int dayOfWeek) = henBatchBusinessLogic.GetDisabledWeekDays(farmId, "farm", isForOpeningDate);
            //GetDefaultDate
            DateTime today = DateTime.Now;
            DateTime defaultDate = today.AddDays(7 - (int)today.DayOfWeek + dayOfWeek);
            bool isNotSuperAdmin = !operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator);
            string lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            return new Tuple<List<int>, DateTime, bool, string>(week, defaultDate, isNotSuperAdmin, lang);
        }
        public Tuple<DateTime, DateTime, string, string> GetReportingStartDatesRange(string openingDate)
        {
            DateTime openingDateTime = DateTime.Parse(openingDate);
            DateTime startDate = openingDateTime.AddDays((int)openingDateTime.DayOfWeek - 6);
            DateTime endDate = openingDateTime.AddDays((int)openingDateTime.DayOfWeek + 6);
            string userLanguage = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;

            return new Tuple<DateTime, DateTime, string, string>(startDate, endDate, openingDate, userLanguage);

        }

        private string GetHenStageString(HenStage? henStage)
        {
            if (henStage.HasValue)
            {
                return henStage switch
                {
                    HenStage.Breeding => localizer[Lang.Breeding],
                    _ => localizer[Lang.Laying],
                };
            }
            else
                return "";
        }


        private List<SelectListItem> GetHenStages()
        {
            List<SelectListItem> henStages = new List<SelectListItem>();
            foreach (HenStage henStage in EnumUtil.GetValues<HenStage>())
            {
                string name = EnumHelper<HenStage>.GetDisplayName(henStage, localizer);
                string id = ((int)henStage).ToString();
                henStages.Add(new SelectListItem(name, id));
            }

            return henStages.OrderBy(hs => hs.Value).ToList();
        }

        [HttpGet]
        public List<SelectListItem> GetFormulas(HenStage? henStage = null, List<string> selection = null)
        {
            List<Guid> formulasSelection = new List<Guid>();
            if (selection != null)
            {
                foreach (var id in selection)
                {
                    formulasSelection.Add(new Guid(id));
                }
            }

            List<SelectListItem> formulas = new List<SelectListItem>();
            if (henStage.HasValue)
                formulas = henBatchBusinessLogic.GetAllFormulasByHenStage(henStage.Value, true).Distinct(selectListItemComparer).ToList();
            else
            {
                if (selection != null)
                {
                    formulas = formulaService.GetAllWithOutput().Where(f => f.Active)
                    .OrderBy(m => m.Output.Name)
                    .Select(m => new SelectListItem(m.Output.Name, m.Output.Id.ToString(), formulasSelection.Contains(m.Id))).ToList();

                }
                else
                {
                    formulas = formulaService.GetAllWithOutput().Where(f => f.Active)
                    .OrderBy(m => m.Output.Name)
                    .Select(m => new SelectListItem(m.Output.Name, m.Output.Id.ToString())).ToList();
                }
            }

            return formulas;
        }

        [HttpGet]
        public List<SelectListItem> GetActivesHenBatchCategories()
        {
            return henBatchCategoryService.GetAll().Where(hbc => hbc.Active)
                    .OrderBy(m => m.Name)
                    .Select(m => new SelectListItem(m.Name, m.Id.ToString())).ToList();
        }

        [HttpGet]
        public int[] GetGeneticBoundaries(Guid geneticId, HenStage henStage) =>
            geneticBusinessLogic.GetGeneticBoundaries(geneticId, henStage);

        [HttpGet]
        public List<SelectListItem> GetGeneticList(HenStage henStage, Guid farmId)
        {
            return henBatchBusinessLogic.GetGenetics(henStage: henStage, selectedFarm: farmId);
        }

        [HttpGet]
        public IActionResult HasFirstProductionDate(Guid henBatchId)
        {
            try
            {
                // Get the hen batch
                var henBatch = henBatchService.Get(henBatchId);

                // Check if it has a first production date
                bool hasFirstProductionDate = henBatch.FirstProductionDate.HasValue;

                return Ok(hasFirstProductionDate);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        #endregion

        private string InitLists(HenBatchFilterDTO d, string action, string selectedCompanyId = null)
        {
            Guid? selectedCompany = String.IsNullOrEmpty(selectedCompanyId) ? (Guid?)null : Guid.Parse(selectedCompanyId);

            List<SelectListItem> companies = GetCompanies(selectedCompany, d.HenStage);
            if (action == ControllerActions.Edit && !string.IsNullOrEmpty(selectedCompanyId) && !companies.Any(i => string.Equals(i.Value, selectedCompanyId, StringComparison.OrdinalIgnoreCase)))
            {
                Company company = companyService.Get(Guid.Parse(selectedCompanyId));
                companies.Add(new SelectListItem(company.BusinessName, company.Id.ToString(), true));
            }
            ViewData["Companies"] = companies;

            List<SelectListItem> farms = new List<SelectListItem>();

            if (action == ControllerActions.Create)
            {
                if (selectedCompany.HasValue)
                    farms = GetFarmsByCompany(selectedCompany.Value, d.HenStage.Value, d.SelectedFarm);
                else if (companies.Count() == 1)
                    farms = GetFarmsByCompany(Guid.Parse(companies.First().Value), d.HenStage.Value, d.SelectedFarm);
            }
            else if (action == ControllerActions.Edit && selectedCompany.HasValue)
            {
                farms = GetFarmsByCompany(selectedCompany.Value, d.HenStage.Value, d.SelectedFarm, action);
                if (action == ControllerActions.Edit && d.SelectedFarm.HasValue && !farms.Any(i => string.Equals(i.Value, d.SelectedFarm.ToString(), StringComparison.OrdinalIgnoreCase)))
                {
                    Farm farm = farmService.Get(d.SelectedFarm.Value);
                    farms.Add(new SelectListItem(farm.Code + " | " + farm.Name, farm.Id.ToString(), true));
                }
            }

            if ((selectedCompany.HasValue || companies.Count() == 1) && farms.Count() == 0)
                return "3";

            ViewData["Farms"] = farms;

            if (farms != null && farms.Count() == 1)
                ViewData["Genetics"] = henBatchBusinessLogic.GetGenetics(d.HenStage, d.GeneticId, Guid.Parse(farms.First().Value));
            else
                ViewData["Genetics"] = henBatchBusinessLogic.GetGenetics(d.HenStage, d.GeneticId, d.SelectedFarm);
            ViewData["HenBatches"] = henBatchBusinessLogic.GetHenBatches(d.HenStage);
            ViewData["MaterialTypes"] = GetMaterialTypeTree(ContainerTypes.HenBatch);
            ViewData["ActionsEnums"] = GetActions();
            ViewData["HenBatchCreateOrEditResources"] = JsLocalizer.GetLocalizedResources(JsLang.HenBatchCreateOrEdit, this.localizer);
            ViewData["ActiveFormulas"] = GetFormulas();
            ViewData["DisabledReason"] = localizer[Lang.DisabledReason];
            ViewData["CapacityUnits"] = GetCapacityUnits();
            ViewData["EntityType"] = ContainerTypes.HenBatch;
            ViewData["ActiveHenBatchCategories"] = GetActivesHenBatchCategories();

            return "";
        }

        #region SetFirstProductionDate
        [HttpPost]
        public async Task<IActionResult> SetFirstProductionDate(string id, DateTime firstProductionDate)
        {
            string message;
            try
            {
                await henBatchBusinessLogic.SetFirstProductionDateAndUpdate(new Guid(id), firstProductionDate);
                message = localizer[Lang.SuccessfulUpdate];
            }

            catch (Exception ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = ex.Message;
            }

            return new JsonResult(new
            {
                Message = message
            });
        }
        #endregion

        private bool CheckAuthorization(HenStage? henStage = null, string? action = null)
        {
            if (henStage.HasValue)
            {
                if (action == ControllerActions.Details || action == ControllerActions.Index)
                {

                    switch (henStage)
                    {
                        case HenStage.Laying:
                            if (!this.operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeLayingAdministrator,
                                Roles.BackofficeLayingUser,
                                Roles.BackofficeLayingHenBatchAdministrator,
                                Roles.BackofficeLayingHenBatchUser,
                                Roles.BackofficeLayingBirdMovement,
                                Roles.BackofficeLayingBirdMovementAdjustmentApprover))
                                return false;
                            break;

                        case HenStage.Breeding:
                            if (!this.operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeBreedingAdministrator,
                                Roles.BackofficeBreedingUser,
                                Roles.BackofficeBreedingHenBatchAdministrator,
                                Roles.BackofficeBreedingHenBatchUser,
                                Roles.BackofficeBreedingBirdMovement,
                                Roles.BackofficeBreedingBirdMovementAdjustmentApprover))
                                return false;
                            break;

                        default:
                            if (!this.operationContext.UserIsInRole(
                                Roles.BackofficeSuperAdministrator))
                                return false;
                            break;

                    }
                    ;
                    return true;

                }
                else
                {
                    switch (henStage)
                    {
                        case HenStage.Laying:
                            if (!this.operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeLayingAdministrator,
                                Roles.BackofficeLayingUser,
                                Roles.BackofficeLayingHenBatchAdministrator))
                                return false;
                            break;

                        case HenStage.Breeding:
                            if (!this.operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeBreedingAdministrator,
                                Roles.BackofficeBreedingUser,
                                Roles.BackofficeBreedingHenBatchAdministrator))
                                return false;
                            break;

                        default:
                            if (!this.operationContext.UserIsInRole(
                                Roles.BackofficeSuperAdministrator))
                                return false;
                            break;

                    }
                    ;
                    return true;
                }
            }
            else
            {
                if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingUser,
                            Roles.BackofficeLayingHenBatchAdministrator,
                            Roles.BackofficeLayingHenBatchUser,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingUser,
                            Roles.BackofficeBreedingHenBatchAdministrator,
                            Roles.BackofficeBreedingHenBatchUser))
                    return false;
                else
                    return true;
            }

        }

        private bool CheckAuthorizationAsAdministrator(string action, HenStage? henStage = null)
        {
            if (henStage.HasValue)
            {
                if (action == "MoveBirds" || action == "Redistribute")
                {
                    switch (henStage)
                    {
                        case HenStage.Laying:
                            if (!this.operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeLayingAdministrator,
                                Roles.BackofficeLayingHenBatchAdministrator,
                                Roles.BackofficeLayingBirdMovement,
                                Roles.BackofficeLayingBirdMovementAdjustmentApprover))
                                return false;
                            break;

                        case HenStage.Breeding:
                            if (!this.operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeBreedingAdministrator,
                                Roles.BackofficeBreedingHenBatchAdministrator,
                                Roles.BackofficeBreedingBirdMovement,
                                Roles.BackofficeBreedingBirdMovement,
                                Roles.BackofficeBreedingBirdMovementAdjustmentApprover))
                                return false;
                            break;

                        default:
                            if (!this.operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator))
                                return false;
                            break;
                    }
                    ;
                    return true;
                }
                else
                {
                    switch (henStage)
                    {
                        case HenStage.Laying:
                            if (!this.operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeLayingAdministrator,
                                Roles.BackofficeLayingHenBatchAdministrator))
                                return false;
                            break;

                        case HenStage.Breeding:
                            if (!this.operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeBreedingAdministrator,
                                Roles.BackofficeBreedingHenBatchAdministrator))
                                return false;
                            break;

                        default:
                            if (!this.operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator))
                                return false;
                            break;
                    }
                    ;
                    return true;
                }
            }
            else
            {
                if (action == "MoveBirds" || action == "Redistribute")
                {
                    if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingHenBatchAdministrator,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingHenBatchAdministrator,
                            Roles.BackofficeLayingBirdMovement,
                            Roles.BackofficeBreedingBirdMovement))
                        return false;
                    else
                        return true;
                }
                else
                {
                    if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingHenBatchAdministrator,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingHenBatchAdministrator))
                        return false;
                    else
                        return true;
                }

            }
        }
        /// <summary>
        /// Exports hen batches an excel file.
        /// </summary>
        public async Task<FileResult> ExcelExport(string searchTerm, Dictionary<string, string> data, HenStage? henStage = null)
        {
            ExportResult exportResult = await henBatchBusinessLogic.ExportExcel(searchTerm, data, henStage);

            return File(exportResult.Stream, exportResult.ExportMimeType, exportResult.Filename);
        }

        /// <summary>
        /// Returns date of last henReport of a given henBatch
        /// </summary>
        public DateTime? ValidationHenReport(Guid henBatchId)
        {
            IQueryable<HenReport> henReport = henReportService.GetAll()
                                            .Where(hr => hr.HenBatchId == henBatchId);
            DateTime? henReportDate = null;
            if (henReport.Any())
                henReportDate = henReport.OrderByDescending(hr => hr.Date)
                                                .Select(hr => hr.Date)
                                                .FirstOrDefault();
            return henReportDate;
        }

        /// <summary>
        /// Given a henbatchId returns a string of its Code | WarehouseName | LineName
        /// </summary>
        public string GetHenBatchesName(Guid henBatchId)
        {
            HenBatch henBatch = henBatchService.GetAllWithWarehouse(henBatchId);
            var henBatchName = henBatch.Code + " | " + henBatch.Line.Warehouse.Name + " | " + henBatch.Line.Name;
            return henBatchName;
        }

        private async void ValidateFirstProductionDate(FirstProductionDateViewModel firstProductionDate)
        {
            List<string> henBatchIds = firstProductionDate.HenBatchIds.Split(' ').ToList();
            IQueryable<HenReport> henReports = henReportService.GetAll().Where(hr => henBatchIds.Contains(hr.HenBatchId.ToString())
                                                                                && (hr.CommercialEggs + hr.HatchableEggs) > 0);

            for (int i = 0; i < firstProductionDate.FirstProductionDateDTOs.Count(); i++)
            {
                if (string.IsNullOrEmpty(firstProductionDate.FirstProductionDateDTOs[i].Date) && henReports.Any())
                    ModelState.AddModelError("FirstProductionDateDTOs[" + i + "].Date", localizer[Lang.NoDate]);
            }
        }


    }
}