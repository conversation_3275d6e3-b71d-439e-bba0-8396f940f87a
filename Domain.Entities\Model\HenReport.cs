using Binit.Framework.Interfaces.DAL;
using Binit.Shaper.Interfaces.Extension;
using Domain.Entities.Model.DomainAbstractEntities;
using Domain.Entities.Model.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace Domain.Entities.Model
{
    /// <summary>
    /// Reporte diario de los lotes
    /// </summary>
    /// <remarks>
    /// Esta entidad registra los datos del parte diario y acumula en HenBatchPerformance
    /// para tener la información precalculada al momento de mostrarla en los reportes de
    /// comparación con genética.
    /// </remarks>
    [Table("HenReport")]
    public class HenReport : SectorDependentEntity, IDraftable
    {
        public HenReport()
        {
            Casualties = new List<Casualty>();
            Depopulations = new List<Depopulation>();
            ShippingNoteConciliations = new List<HenReportConciliation>();
            ClassifiedEggs = new List<HenReportClassifiedEgg>();
        }

        /// <summary>
        /// Date of the report.
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Cantidad de aves al finalizar el reporte
        /// </summary>
        public int HenAmountFemale { get; set; }

        public int HenAmountMale { get; set; }

        [NotMapped]
        public int Dead { get { return DeadFemale + DeadMale; } }

        /// <summary>
        /// Cantidad de aves muertas
        /// </summary>
        public int DeadFemale { get; set; }

        public int DeadMale { get; set; }

        /// <summary>
        /// Cantidad de alimento consumido por las aves hembra
        /// </summary>
        [Column(TypeName = "decimal(16,4)")]
        public decimal FeedIntakeFemale { get; set; }

        /// <summary>
        /// Cantidad de alimento consumido por las aves macho
        /// </summary>
        [Column(TypeName = "decimal(16,4)")]
        public decimal FeedIntakeMale { get; set; }

        /// <summary>
        /// Cantidad de agua consumida
        /// </summary>
        [Column(TypeName = "decimal(16,4)")]
        public decimal WaterConsumption { get; set; }

        /// <summary>
        /// Temperatura mínima
        /// </summary>
        [Column(TypeName = "decimal(16,4)")]
        public decimal? MinTemp { get; set; }

        /// <summary>
        /// Temperatura máxima
        /// </summary>
        [Column(TypeName = "decimal(16,4)")]
        public decimal? MaxTemp { get; set; }

        /// <summary>
        /// Humedad
        /// </summary>
        [Column(TypeName = "decimal(16,4)")]
        public decimal? Humidity { get; set; }

        public uint HatchableEggs { get; set; }

        public uint CommercialEggs { get; set; }

        public uint TotalEggs { get { return HatchableEggs + CommercialEggs + BrokenEggs; } }

        public uint BrokenEggs { get; set; }

        public List<HenReportClassifiedEgg> ClassifiedEggs { get; set; }

        /// <summary>
        /// Required
        /// </summary>
        [ForeignKey("HenBatchId")]
        public HenBatch HenBatch { get; set; }
        public Guid HenBatchId { get; set; }

        [ForeignKey("FormulaConsumedId")]
        public Material FormulaConsumed { get; set; }
        public Guid? FormulaConsumedId { get; set; }

        [ForeignKey("HenBatchPerformanceId")]
        public HenBatchPerformance HenBatchPerformance { get; set; }

        public Guid? HenBatchPerformanceId { get; set; }

        public int DepopulateFemale { get; set; }

        public int DepopulateMale { get; set; }

        public int ToFloorFemale { get; set; }

        public int ToFloorMale { get; set; }

        public int ToCageFemale { get; set; }

        public int ToCageMale { get; set; }

        public List<ShippingNote> ShippingNotes { get; set; }

        [ForeignKey("InconsistencyReportId")]
        public InconsistencyReport InconsistencyReport { get; set; }
        public Guid? InconsistencyReportId { get; set; }

        public ReportEnum ReportEnum { get; set; }
        public ReportUploadEnum UploadOrigin { get; set; }

        public List<HenReportConciliation> ShippingNoteConciliations { get; set; }

        // adjustment entities
        [NotMapped]
        public Guid DraftId { get; set; }

        [ForeignKey("RectificationHenReportId")]
        public HenReport RectificationHenReport { get; set; }
        public Guid? RectificationHenReportId { get; set; }

        public AuthorizationResponseEnum AuthorizationResponseEnum { get; set; }

        ///<summary>
        /// All the casualties, can be either female (if IsFemale == true) or male.
        /// </summary>
        public List<Casualty> Casualties { get; set; }
        [NotMapped]
        public List<Casualty> CasualtiesFemale { get { return ClassifyCasualties(true); } set => ClassifyCasualties(false); }
        [NotMapped]
        public List<Casualty> CasualtiesMale { get { return ClassifyCasualties(false); } set => ClassifyCasualties(true); }

        [Column(TypeName = "decimal(16,4)")]
        public decimal? WaterPh { get; set; }

        [Column(TypeName = "decimal(16,4)")]
        public decimal? WaterChlorineConcentration { get; set; }
        public int WaterPillQuantity { get; set; }

        public List<Depopulation> Depopulations { get; set; }

        [NotMapped]
        public List<Depopulation> DepopulationsFemale { get { return Depopulations.Where(d => d.IsFemale).ToList(); } }

        [NotMapped]
        public List<Depopulation> DepopulationsMale { get { return Depopulations.Where(d => !d.IsFemale).ToList(); } }

        public override bool CanRead(IOperationContext context)
        {
            return true;
        }

        public override bool CanWrite(IOperationContext context)
        {
            return true;
        }
        // Define the female and male casualties according to the IsFemale
        private List<Casualty> ClassifyCasualties(bool isFemale)
        {
            List<Casualty> casualties = new List<Casualty>();
            if (this.Casualties != null)
            {
                if (isFemale)
                    casualties = this.Casualties.Where(c => c.IsFemale).ToList();
                else
                    casualties = this.Casualties.Where(c => !c.IsFemale).ToList();
            }
            return casualties;
        }
    }
}

