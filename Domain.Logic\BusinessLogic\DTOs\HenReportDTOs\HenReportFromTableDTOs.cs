using System;
using System.Collections.Generic;

namespace Domain.Logic.BusinessLogic.DTOs.HenReportDTOs
{
    public class HenReportWarehouseDTO
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public List<HenReportLineDTO> Lines { get; set; }
    }

    public class HenReportLineDTO
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public List<HenReportHenBatchDTO> HenBatches { get; set; }
    }

    public class HenReportHenBatchDTO
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public int HenAmountFemale { get; set; }
        public int HenAmountMale { get; set; }
        public List<HenReportFeedIntakeOriginDTO> FeedIntakeOrigins { get; set; }
    }

    public class HenReportFeedIntakeOriginDTO
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
    }

    public class PlannedGADDTO
    {
        public Guid HenbatchId { get; set; }
        public int Week { get; set; }
        public decimal? ProgramFemale { get; set; }
        public decimal? ProgramMale { get; set; }
    }

    public class HenReportFromTableDTO
    {
        public Guid HenBatchId { get; set; }

        public decimal WaterConsumption { get; set; }
        public decimal WaterPh { get; set; }
        public decimal? WaterChlorineConcentration { get; set; }
        public decimal WaterPillQuantity { get; set; }
        public decimal MinTemp { get; set; }
        public decimal MaxTemp { get; set; }
        public decimal Humidity { get; set; }

        // Ovos Incubáveis (Hatchable Eggs)
        public int CleanNestEggs { get; set; }
        public int DirtyNestEggs { get; set; }
        public int BedEggs { get; set; }

        // Ovos Comerciais (Commercial Eggs)
        public int DoubleYolkEggs { get; set; }
        public int SmallEggs { get; set; }
        public int DefectiveEggs { get; set; }
        public int DirtyRolledEggs { get; set; }
        public int CrackedEggs { get; set; }
        public int ThinShellEggs { get; set; }
        public int EliminatedBrokenEggs { get; set; }

        public decimal FeedIntakeFemale { get; set; }
        public Guid FeedIntakeFemaleOriginId { get; set; }
        public decimal FeedIntakeMale { get; set; }
        public Guid FeedIntakeMaleOriginId { get; set; }

        public int DeadMale { get; set; }
        public int DeadFemale { get; set; }

        public int HenAmountFemale { get; set; }
        public int HenAmountMale { get; set; }

        public List<ClassifiedEggDTO> ClassifiedEggs { get; set; } = new List<ClassifiedEggDTO>();
    }

    public class ClassifiedEggDTO
    {
        public Guid MaterialId { get; set; }
        public int Quantity { get; set; }
    }

    public class CreateHenReportFromTableDTO
    {
        public string ReportDate { get; set; }
        public List<HenReportFromTableDTO> Reports { get; set; }
    }

    public class HenReportResultDTO
    {
        public Guid Id { get; set; }
        public int TotalDeaths { get; set; }
        public int TotalDepopulations { get; set; }
    }
}
