﻿using Binit.Framework;
using Domain.Entities.Constants.ReportPlanner;
using Domain.Entities.Model;
using Domain.Logic.Interfaces;
using Domain.Logic.Interfaces.PlannerBusinessLogic;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WebApp.Models.PlannerViewModel;
using WebApp.Models.PlannerViewModel.GADViewModel;

using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.PlannerController;

namespace WebApp.Controllers
{

    public class PlannerController : Controller
    {
        private readonly IGADPlannerBusinessLogic gadPlannerBusinessLogic;
        private readonly IHenBatchService henBatchService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IHenBatchBusinessLogic henBatchBusinessLogic;

        public PlannerController(
            IHenBatchBusinessLogic henBatchBusinessLogic, 
            IGADPlannerBusinessLogic gadPlannerBusinessLogic,
            IHenBatchService henBatchService,
            IStringLocalizer<SharedResources> localizer)
        {
            this.gadPlannerBusinessLogic = gadPlannerBusinessLogic;
            this.henBatchService = henBatchService;
            this.henBatchBusinessLogic = henBatchBusinessLogic;
            this.localizer = localizer;
        }

        public IActionResult Index()
        {
            return View();
        }

        #region Planner 
        [HttpGet]
        [Route("/planner/gad/male")]
        public async Task<IActionResult> PlannerMale(HenStage henstage)
        {
            ViewData["HenStage"] = henstage;
            ViewData["HenbatchesParent"] = await GetHenbatchesParent(henstage);

            List<object[]> columns = GADPlannerMaleViewModel.GetColumns(localizer);

            return View("~/Views/Planner/GAD/Male.cshtml", new PlannerViewModel(GADReportPlannerConst.ReportMale, columns, true));
        }

        [HttpGet]
        [Route("/planner/gad/female")]
        public async Task<IActionResult> PlannerFemale(HenStage henstage)
        {
            ViewData["HenStage"] = henstage;
            ViewData["HenbatchesParent"] = await GetHenbatchesParent(henstage);

            List<object[]> columns = GADPlannerFemaleViewModel.GetColumns(localizer, henstage);

            return View("~/Views/Planner/GAD/Female.cshtml", new PlannerViewModel(GADReportPlannerConst.ReportFemale, columns, true));
        }

        #endregion

        #region Load data 
        [HttpPost]
        [Route("planner/get-data")]
        public async Task<IActionResult> Get([FromBody] PlannerReqDTO req)
        {
            PlannerLoaderDTO response = new PlannerLoaderDTO();

            Range range = (req.HenStage == HenStage.Laying) ? new Range(21, 50) : new Range(1, 18);

            if (!Guid.TryParse(req.HenbatchId, out Guid henbatchId))
                return Problem(statusCode: (int)HttpStatusCode.InternalServerError, detail: localizer[Lang.HenbatchSelectorErrorMessage].Value);

            HenBatch henbatch = await henBatchService.GetAll()
                    .Include(f => f.Farm)
                    .Include(f => f.Line).ThenInclude(w => w.Warehouse)
                    .Include(f => f.Genetic)
                    .Where(hb => !hb.DateEnd.HasValue && hb.Id == henbatchId)
                    .FirstOrDefaultAsync();

            if (req.Model.PlannerName == GADReportPlannerConst.ReportFemale)
            {
                var result = await gadPlannerBusinessLogic.GetAverageGAD(henbatchId, req.HenStage, GADReportPlannerConst.ReportFemale);
                response.Data = gadPlannerBusinessLogic.GetGADFemale(henbatchId, range);
                GADPlannerFemaleViewModel model = new GADPlannerFemaleViewModel(henbatch)
                {
                    AvgGAD = result.Item2.ToString("F2"),
                    AvgBirdWeight = result.Item1.ToString("F2")
                };
                response.AdditionalInfo = model.Serialize();
            }
                
            if (req.Model.PlannerName == GADReportPlannerConst.ReportMale)
            {
                var result = await gadPlannerBusinessLogic.GetAverageGAD(henbatchId, req.HenStage, GADReportPlannerConst.ReportMale);
                response.Data = gadPlannerBusinessLogic.GetGADMale(henbatchId, range);
                GADPlannerMaleViewModel model = new GADPlannerMaleViewModel(henbatch)
                {
                    AvgGAD = result.Item2.ToString("F2"),
                    AvgBirdWeight = result.Item1.ToString("F2")
                }; ;
                response.AdditionalInfo = model.Serialize();
            }
                
            return new JsonResult(response);
        }
        #endregion~

        #region Load data 
        [HttpPost]
        [Route("planner/GetTableDataByLine")]
        public async Task<IActionResult> GetTableDataByLine([FromBody] PlannerReqDTO req)
        {
            PlannerLoaderDTO response = new PlannerLoaderDTO();

            Range range = (req.HenStage == HenStage.Laying) ? new Range(21, 50) : new Range(1, 18);

            if (!Guid.TryParse(req.HenbatchId, out Guid henbatchId))
                return Problem(statusCode: (int)HttpStatusCode.InternalServerError, detail: localizer[Lang.HenbatchSelectorErrorMessage].Value);

            HenBatch henbatch = await henBatchService.GetAll()
                    .Include(f => f.Farm)
                    .Include(f => f.Line).ThenInclude(w => w.Warehouse)
                    .Include(f => f.Genetic)
                    .Where(hb => !hb.DateEnd.HasValue && hb.Id == henbatchId)
                    .FirstOrDefaultAsync();

            if (req.Model.PlannerName == GADReportPlannerConst.ReportFemale)
            { 
                var result = await gadPlannerBusinessLogic.GetAverageGAD(henbatchId, req.HenStage, GADReportPlannerConst.ReportFemale);
                response.Data = gadPlannerBusinessLogic.GetGADFemale(henbatchId, range);
                GADPlannerFemaleViewModel model = new GADPlannerFemaleViewModel(henbatch)
                {
                    AvgGAD = result.Item2.ToString("F2"),
                    AvgBirdWeight = result.Item1.ToString("F2")
                };
                response.AdditionalInfo = model.Serialize();
            }
                
            if (req.Model.PlannerName == GADReportPlannerConst.ReportMale)
            {
                var result = await gadPlannerBusinessLogic.GetAverageGAD(henbatchId, req.HenStage, GADReportPlannerConst.ReportMale);
                response.Data = gadPlannerBusinessLogic.GetGADMale(henbatchId, range);
                GADPlannerMaleViewModel model = new GADPlannerMaleViewModel(henbatch)
                {
                    AvgGAD = result.Item2.ToString("F2"),
                    AvgBirdWeight = result.Item1.ToString("F2")
                }; ;
                response.AdditionalInfo = model.Serialize();
            }
                
            return new JsonResult(response);
        }
        #endregion

        #region Save changes 
        [HttpPost]
        [Route("planner/save-changes")]
        public async Task<IActionResult> SaveChanges([FromBody] PlannerReqDTO req)
        {
            try
            {
                if (string.IsNullOrEmpty(req.ElementsChanged))
                    return Ok();

                Guid henbatchId = Guid.Parse(req.HenbatchId);
                string plannerName = req.Model.PlannerName;

                IEnumerable<ReportPlannerProgram> changes = JsonConvert.DeserializeObject<List<ElementsChanged>>(req.ElementsChanged)
                    .Select(s => new ReportPlannerProgram
                    {
                        PlannerName = plannerName,
                        HenbatchId = henbatchId,
                        Week = s.Week,
                        Program = decimal.Parse(s.Value)
                    });

                await gadPlannerBusinessLogic.SaveProgram(plannerName, henbatchId, changes);

                return Ok();
            }
            catch (Exception ex)
            {
                return Problem(statusCode: (int)HttpStatusCode.InternalServerError, detail: ex.Message);
            }

        }
        #endregion

        #region AJAX Request 
        public async Task<List<SelectListItem>> GetHenbatchesParent(HenStage henStage, Guid? selected = null)
        {
            IQueryable<HenBatch> henBatches = this.henBatchService.GetAll(true, false)
                .Where(hb => hb.HenStage == henStage && !hb.DateEnd.HasValue && !hb.ParentId.HasValue && hb.HenAmountFemale+hb.HenAmountMale > 0).OrderBy(sli => sli.DetailedName);

            return await henBatches.Select(hb => new SelectListItem(hb.DetailedName, hb.Id.ToString(), selected.HasValue && hb.Id == selected.Value)).ToListAsync();
        }

        [HttpGet]
        [Route("planner/gad/male/ExcelExport")]
        public async Task<IActionResult> ExcelExportMale(Guid henbatchId, Guid warehouseId, HenStage henStage)
        {
            var exportResult = await gadPlannerBusinessLogic.GetGADMaleReportAsync(henbatchId, warehouseId, henStage);
            return File(exportResult.Stream, exportResult.ExportMimeType, exportResult.Filename);
        }

        [HttpGet]
        [Route("planner/gad/female/ExcelExport")]
        public async Task<IActionResult> ExcelExportFemale(Guid henbatchId, Guid warehouseId, HenStage henStage)
        {
            var exportResult = await gadPlannerBusinessLogic.GetGADFemaleReportAsync(henbatchId, warehouseId, henStage);
            return File(exportResult.Stream, exportResult.ExportMimeType, exportResult.Filename);
        }


        [HttpGet]
        [Route("planner/get-henbatch-childs")]
        public async Task<List<SelectListItem>> GetHenbatchesChild([FromQuery] HenStage henStage, [FromQuery] Guid parentId, [FromQuery] string plannerName)
        {
            IQueryable<HenBatch> henBatches = this.henBatchService.GetAll(true, false)
                .Where(hb => hb.HenStage == henStage && !hb.DateEnd.HasValue && hb.ParentId.HasValue && hb.ParentId.Value == parentId).OrderBy(sli => sli.DetailedName);

            if (plannerName == GADReportPlannerConst.ReportFemale)
                henBatches = henBatches.Where(w => w.HenAmountFemale > 0);

            if (plannerName == GADReportPlannerConst.ReportMale)
                henBatches = henBatches.Where(w => w.HenAmountMale > 0);

            return await henBatches.Select(hb => new SelectListItem(hb.DetailedName, hb.Id.ToString())).ToListAsync();
        }


        [HttpGet]
        [Route("planner/gad/GetLinesByWarehouse")] 
        public List<SelectListItem> GetLinesByWarehouse(HenStage henStage, Guid? warehouseId, Guid? henBatchId)
        {
            return henBatchBusinessLogic.GetLinesHenBatchFiltered(henStage, warehouseId, henBatchId);
        }

        [HttpGet]
        [Route("planner/GetWarehousesByHenBatch")]
        public List<SelectListItem> GetWarehousesByHenBatch(Guid selectedHenBatch)
        {
            return this.henBatchBusinessLogic.GetWarehousesByHenBatch(selectedHenBatch);
        }
        #endregion

        #region Private Methods
        #endregion
    }
}
