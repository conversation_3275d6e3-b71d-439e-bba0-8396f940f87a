﻿using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;

namespace WebApp.Controllers.Api
{
    [Route("api/[controller]")]
    [ApiController]
    public class MaterialController : ControllerBase
    {
        private readonly IMaterialService materialService;

        public MaterialController(IMaterialService materialService)
        {
            this.materialService = materialService;
        }

        [HttpGet("egg-types")]
        public IActionResult GetEggTypes()
        {
            // Filter materials by CapacityUnitId = 81E56095-8A5D-4A6C-92E6-53E6619CE7B7
            var eggTypes = materialService.GetAllWithMaterialType()
                .Where(material => material.Active &&
                       material.CapacityUnitId == Guid.Parse("81E56095-8A5D-4A6C-92E6-53E6619CE7B7"))
                .OrderBy(material => material.Name)
                .Select(material => new
                {
                    id = material.Id,
                    name = material.Name,
                    internalId = material.InternalId,
                    materialTypePath = material.MaterialType.Path
                })
                .ToList();

            return Ok(eggTypes);
        }
    }
}
