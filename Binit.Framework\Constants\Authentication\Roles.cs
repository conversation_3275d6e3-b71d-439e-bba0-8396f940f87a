using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

// Disable "Field is never used" because private fields in this class are used by reflection.
// ReSharper disable UnusedMember.Local

namespace Binit.Framework.Constants.Authentication
{
    /// <summary>
    /// Role constants class.
    /// Contains all the application roles represented by constants.
    /// </summary>
    public static class Roles
    {
        // Super admin.
        public const string BackofficeSuperAdministrator = "Backoffice.SuperAdministrator";
        private static Guid backofficeSuperAdministratorId = Guid.Parse("E68ACD69-3282-4D49-8DCF-9C78BF5D01F2");

        #region OvoFarm Roles

        public const string BackofficeDailyReportWithoutDateValidation = "Backoffice.DailyReportWithoutDateValidation";
        private static Guid backofficeDailyReportWithoutDateValidationId = Guid.Parse("9404BB75-14D4-459A-8F42-5AD847C45AE8");

        public const string BackofficeCanDeleteDailyReport = "Backoffice.CanDeleteDailyReport";
        private static Guid backofficeCanDeleteDailyReportId = Guid.Parse("79FAFF04-B7C2-4075-8F7A-CBE6F298CEF7");

        public const string BackofficeNewsAdministrator = "Backoffice.NewsAdministrator";
        private static Guid backofficeNewsAdministratorId = Guid.Parse("EE7072EA-**************-FA1B28551B2B");

        public const string BackofficeAgentUser = "Backoffice.AgentUser";
        private static Guid backofficeAgentUserId = Guid.Parse("2B1B1316-ED0F-4975-AA10-748A9E5832D4");

        public const string BackofficeCloseHappeningEditor = "Backoffice.CloseHappeningEditor";
        private static Guid backofficeCloseHappeningEditorId = Guid.Parse("9cdf7502-d4b4-4683-88d1-17f88f834c82");

        public const string BackofficeSampleCageAdministrator = "Backoffice.SampleCageAdministrator";
        private static Guid backofficeSampleCageAdministratorId = Guid.Parse("cad97eef-2a37-45c7-9a7d-8ce988598402");

        public const string BackofficeAdjustButton = "Backoffice.AdjustButton";
        private static Guid backofficeAdjustButtonId = Guid.Parse("05965b1a-e582-48e8-b6a8-4c4765191c58");
        #endregion

        #region Breeding Roles
        public const string BackofficeBreedingAdministrator = "Backoffice.BreedingAdministrator";
        private static Guid backofficeBreedingAdministratorId = Guid.Parse("5C402404-B602-4850-9279-283F01100D04");

        public const string BackofficeBreedingUser = "Backoffice.BreedingUser";
        private static Guid backofficeBreedingUserId = Guid.Parse("6C740F63-1B62-4A32-A440-9FDA39F0D001");

        public const string BackofficeBreedingDailyReportsAdministrator = "Backoffice.BreedingDailyReportsAdministrator";
        private static Guid backofficeBreedingDailyReportsAdministratorId = Guid.Parse("ABEECC07-9FEF-467E-B874-D7B1E9A8E88D");

        public const string BackofficeBreedingDailyReportsUser = "Backoffice.BreedingDailyReportsUser";
        private static Guid backofficeBreedingDailyReportsUserId = Guid.Parse("727007D8-97A9-4E69-8FF4-F9DD8E09E117");

        public const string BackofficeBreedingHenReportAdministrator = "Backoffice.BreedingHenReportAdministrator";
        private static Guid backofficeBreedingHenReportAdministratorId = Guid.Parse("35BC0EA9-A5F3-4676-BA64-C3EA5D2D02C9");

        public const string BackofficeBreedingHenReportUser = "Backoffice.BreedingHenReportUser";
        private static Guid backofficeBreedingHenReportUserId = Guid.Parse("B719C432-CE51-43D1-A07C-1EEA6B23754A");

        public const string BackofficeBreedingHappeningAdministrator = "Backoffice.BreedingHappeningAdministrator";
        private static Guid backofficeBreedingHappeningAdministratorId = Guid.Parse("04E0394A-1982-4782-989E-59947BB41A94");

        public const string BackofficeBreedingHappeningUser = "Backoffice.BreedingHappeningUser";
        private static Guid backofficeBreedingHappeningUserId = Guid.Parse("2A3E6FDD-1DC5-4437-8A07-D74A11B6699E");

        public const string BackofficeBreedingInconsistencyAdministrator = "Backoffice.BreedingInconsistencyAdministrator";
        private static Guid backofficeBreedingInconsistencyAdministratorId = Guid.Parse("f2dcfda4-14e2-4a07-b04f-705a28e670da");

        public const string BackofficeBreedingInconsistencyUser = "Backoffice.BreedingInconsistencyUser";
        private static Guid backofficeBreedingInconsistencyUserId = Guid.Parse("507a3642-1a5b-4b55-825f-0c4533af2ef6");

        public const string BackofficeBreedingMaterialAnalysisReportAdministrator = "Backoffice.BreedingMaterialAnalysisReportAdministrator";
        private static Guid backofficeBreedingMaterialAnalysisReportAdministratorId = Guid.Parse("c5468084-dce7-41fb-b2c3-06c0ac035d6c");

        public const string BackofficeBreedingMaterialAnalysisReportUser = "Backoffice.BreedingMaterialAnalysisReportUser";
        private static Guid backofficeBreedingMaterialAnalysisReportUserId = Guid.Parse("9957473f-edcd-4150-8f4c-0450d07cdbe3");

        public const string BackofficeBreedingShippingNoteUser = "Backoffice.BreedingShippingNoteUser";
        private static Guid backofficeBreedingShippingNoteUserId = Guid.Parse("04f22ae4-cff8-406c-8bd5-4e4d79555c51");

        public const string BackofficeBreedingShippingNoteAdministrator = "Backoffice.BreedingShippingNoteAdministrator";
        private static Guid backofficeBreedingShippingNoteAdministratorId = Guid.Parse("bf8a2621-7665-45ad-ac74-5deea4f9f89d");

        public const string BackofficeBreedingHenBatchAdministrator = "Backoffice.BreedingHenBatchAdministrator";
        private static Guid backofficeBreedingHenBatchAdministratorId = Guid.Parse("8ECDE8B0-FCE9-4FED-81CC-632F80083682");

        public const string BackofficeBreedingHenBatchUser = "Backoffice.BreedingHenBatchUser";
        private static Guid backofficeBreedingHenBatchUserId = Guid.Parse("D72A3967-F4B0-4C52-B547-422D511CAB8A");

        public const string BackofficeBreedingHenBatchPerformanceAdministrator = "Backoffice.BreedingHenBatchPerformanceAdministrator";
        private static Guid backofficeBreedingHenBatchPerformanceAdministratorId = Guid.Parse("F342C607-E904-4754-9F1A-A4075BB5A691");

        public const string BackofficeBreedingHenBatchPerformanceUser = "Backoffice.BreedingHenBatchPerformanceUser";
        private static Guid backofficeBreedingHenBatchPerformanceUserId = Guid.Parse("632A4529-B945-4DC8-B223-BA6B6B7EC0D2");

        public const string BackofficeBreedingGeneticReportAdministrator = "Backoffice.BreedingGeneticReportAdministrator";
        private static Guid backofficeBreedingGeneticReportAdministratorId = Guid.Parse("483DEF67-C9CC-4DE7-9C8E-2C1569352E37");

        public const string BackofficeBreedingGeneticReportUser = "Backoffice.BreedingGeneticReportUser";
        private static Guid backofficeBreedingGeneticReportUserId = Guid.Parse("2D1EA8E1-D64A-47D5-BAD1-BB90FDF5E328");

        public const string BackofficeBreedingSampleCageReportAdministrator = "Backoffice.BreedingSampleCageReportAdministrator";
        private static Guid backofficeBreedingSampleCageReportAdministratorId = Guid.Parse("B92FA1EF-4710-4845-B659-130404125A77");

        public const string BackofficeBreedingSampleCageReportUser = "Backoffice.BreedingSampleCageReportUser";
        private static Guid backofficeBreedingSampleCageReportUserId = Guid.Parse("C3EA2C65-9F6C-4AC1-A301-B2BBD63DBF40");

        public const string BackofficeBreedingStockUser = "Backoffice.BreedingStockUser";
        private static Guid backofficeBreedingStockUserId = Guid.Parse("728366fa-185e-4a8e-ba3a-eb4603c11485");

        public const string BackofficeBreedingStockAdministrator = "Backoffice.BreedingStockAdministrator";
        private static Guid backofficeBreedingStockAdministratorId = Guid.Parse("53ef923c-4315-4b39-bdfd-ac2789aa33f9");

        public const string BackofficeBreedingReportsAdministrator = "Backoffice.BreedingReportsAdministrator";
        private static Guid backofficeBreedingReportsAdministratorId = Guid.Parse("C9866EEB-FBBB-46A9-AFD5-DD9BCAA17A11");

        public const string BackofficeBreedingReportsUser = "Backoffice.BreedingReportsUser";
        private static Guid backofficeBreedingReportsUserId = Guid.Parse("CA2F69B8-44C7-4EFA-8E2D-6C609FB01919");

        public const string BackofficeBreedingTaskAdministrator = "Backoffice.BreedingTaskAdministrator";
        private static Guid backofficeBreedingTaskAdministratorId = Guid.Parse("1A92D04B-F3B9-4A19-933B-09CE281497E9");

        public const string BackofficeBreedingTaskUser = "Backoffice.BreedingTaskUser";
        private static Guid backofficeBreedingTaskUserId = Guid.Parse("FF8B0A5F-52E3-49A3-8CB9-249FB7AA2A20");

        public const string BackofficeCasualtyReasonAdministrator = "Backoffice.CasualtyReasonAdministrator";
        private static Guid backofficeCasualtyReasonAdministratorId = Guid.Parse("C486DCF5-D598-4192-85BE-50309AAF79AF");

        public const string BackofficeCasualtyReasonUser = "Backoffice.CasualtyReasonUser";
        private static Guid backofficeCasualtyReasonUserId = Guid.Parse("849EE101-D1FC-4A18-97B1-798F021F6722");

        public const string BackofficeBreedingDashboard = "Backoffice.BreedingDashboard";
        private static Guid backofficeBreedingDashboardId = Guid.Parse("FCE9882B-5E86-430D-BA4E-6CC5386F7C8B");

        public const string BackofficeDepopulationReasonAdministrator = "Backoffice.DepopulationReasonAdministrator";
        private static Guid backofficeDepopulationReasonAdministratorId = Guid.Parse("DE3B1B23-A71F-44C7-860A-DF560CA854F1");

        public const string BackofficeDepopulationReasonUser = "Backoffice.DepopulationReasonUser";
        private static Guid backofficeDepopulationReasonUserId = Guid.Parse("7B9DBA55-DFC6-44B7-8064-6F9DDADDE9AC");

        public const string BackofficeBreedingBirdMovement = "Backoffice.BreedingBirdMovement";
        private static Guid backofficeBreedingBirdMovementId = Guid.Parse("537C6B80-3C4C-495C-8516-57E32A1731E1");

        public const string BackofficeBreedingBirdMovementAdjustmentApprover = "Backoffice.BreedingBirdMovementAdjustmentApprover";
        private static Guid backofficeBreedingBirdMovementAdjustmentApproverId = Guid.Parse("01af0abd-11e2-43de-bec0-528ea4bceff9");

        public const string BackofficeBreedingBulkLoad = "Backoffice.BreedingBulkLoad";
        private static Guid backofficeBreedingBulkLoadId = Guid.Parse("619e9f92-e407-4c62-9260-ab3b247299f9");

        public const string HenReportBreedingAdjustmentApprover = "Backoffice.BreedingAdjustmentApprover";
        private static Guid henReportBreedingAdjustmentApproverId = Guid.Parse("bcc1dd8a-0fa1-4497-9d22-f633b2aac81a");

        public const string BackofficeBreedingBenchmark = "Backoffice.BreedingBenchmark";
        private static Guid backofficeBreedingBenchmarkId = Guid.Parse("90D0E1EA-83FE-4894-A4F4-438F0392D080");

        public const string SearaDashboardCardsBreeding = "Backoffice.SearaDashboardCardsBreeding";
        private static Guid searaDashboardCardsBreedingId = Guid.Parse("5CB3DA71-9877-4FFB-A5DE-5C1103FB2DF0");

        public const string SearaDashboardChartsBreeding = "Backoffice.SearaDashboardChartsBreeding";
        private static Guid searaDashboardChartsBreedingId = Guid.Parse("16322FD6-4D23-4EEE-A8A2-8728EA1FF497");

        public const string ManagerialDashboardBreeding = "Backoffice.ManagerialDashboardBreeding";
        private static Guid managerialDashboardBreedingId = Guid.Parse("5B126990-78CF-4ECA-90C8-B4C01A02001A");

        public const string ManagerialInventoryBreeding = "Backoffice.ManagerialInventoryBreeding";
        private static Guid managerialInventoryBreedingId = Guid.Parse("D5BA39BE-8B25-42B8-8262-EACB3C350EB1");
        #endregion

        #region Laying Roles
        public const string BackofficeLayingAdministrator = "Backoffice.LayingAdministrator";
        private static Guid backofficeLayingAdministratorId = Guid.Parse("5287B1FC-B549-46C9-92C2-4A0002F90423");

        public const string BackofficeStockConciliationAdministrator = "Backoffice.StockConciliationAdministrator";
        private static Guid backofficeStockConciliationAdministratorId = Guid.Parse("cc386712-02cb-4185-95e3-e008adb91784");

        public const string BackofficeLayingUser = "Backoffice.LayingUser";
        private static Guid backofficeLayingUserId = Guid.Parse("6e37dacc-2bc7-4bf0-a6a1-89a28f06b8a0");

        public const string BackofficeLayingDailyReportsAdministrator = "Backoffice.LayingDailyReportsAdministrator";
        private static Guid backofficeLayingDailyReportsAdministratorId = Guid.Parse("61DCB3BF-FE3F-466A-B481-52B3A7B43FB7");

        public const string BackofficeLayingDailyReportsUser = "Backoffice.LayingDailyReportsUser";
        private static Guid backofficeLayingDailyReportsUserId = Guid.Parse("57A104C0-CDBE-45B8-96F8-880B68BF5CA9");

        public const string BackofficeLayingGeneticReportAdministrator = "Backoffice.LayingGeneticReportAdministrator";
        private static Guid backofficeLayingGeneticReportAdministratorId = Guid.Parse("9e35af02-364c-41b0-b5ac-069f36510672");

        public const string BackofficeLayingGeneticReportUser = "Backoffice.LayingGeneticReportUser";
        private static Guid backofficeLayingGeneticReportUserId = Guid.Parse("c0f3f61a-c789-44c0-a056-8126b08a1c7c");

        public const string BackofficeLayingHappeningAdministrator = "Backoffice.LayingHappeningAdministrator";
        private static Guid backofficeLayingHappeningAdministratorId = Guid.Parse("DD2F589C-F8F2-4460-8ACF-CB41DBB40ABD");

        public const string BackofficeLayingHappeningUser = "Backoffice.LayingHappeningUser";
        private static Guid backofficeLayingHappeningUserId = Guid.Parse("4F51FFD3-0308-492A-8122-8FC95B266109");

        public const string BackofficeLayingHenBatchAdministrator = "Backoffice.LayingHenBatchAdministrator";
        private static Guid backofficeLayingHenBatchAdministratorId = Guid.Parse("cf941da2-154d-45aa-b7cb-1632d8cec217");

        public const string BackofficeLayingHenBatchUser = "Backoffice.LayingHenBatchUser";
        private static Guid backofficeLayingHenBatchUserId = Guid.Parse("439666d5-cb6c-4756-95e1-ab7285967711");

        public const string BackofficeLayingHenBatchPerformanceAdministrator = "Backoffice.LayingHenBatchPerformanceAdministrator";
        private static Guid backofficeLayingHenBatchPerformanceAdministratorId = Guid.Parse("db7c4abb-4174-4b61-a357-b618803b7ede");

        public const string BackofficeLayingHenBatchPerformanceUser = "Backoffice.LayingHenBatchPerformanceUser";
        private static Guid backofficeLayingHenBatchPerformanceUserId = Guid.Parse("b568ed18-68f5-4175-9571-720048258f40");

        public const string BackofficeLayingHenReportAdministrator = "Backoffice.LayingHenReportAdministrator";
        private static Guid backofficeLayingHenReportAdministratorId = Guid.Parse("00A6666A-3C5A-4EC6-9463-27C0F5DD90CD");

        public const string BackofficeLayingHenReportUser = "Backoffice.LayingHenReportUser";
        private static Guid backofficeLayingHenReportUserId = Guid.Parse("12A4406C-F2B9-4F13-A60E-E62A2317FFA6");

        public const string BackofficeLayingInconsistencyAdministrator = "Backoffice.LayingInconsistencyAdministrator";
        private static Guid backofficeLayingInconsistencyAdministratorId = Guid.Parse("11c8f8be-5fad-421c-8512-06e5b11aa63e");

        public const string BackofficeLayingInconsistencyUser = "Backoffice.LayingInconsistencyUser";
        private static Guid backofficeLayingInconsistencyUserId = Guid.Parse("1c2577aa-0da0-4a0e-91aa-85f4c87a5fd1");

        public const string BackofficeLayingMaterialAnalysisReportAdministrator = "Backoffice.LayingMaterialAnalysisReportAdministrator";
        private static Guid backofficeLayingMaterialAnalysisReportAdministratorId = Guid.Parse("85ea2045-40d1-4e42-86d1-46dcc13f60c3");

        public const string BackofficeLayingMaterialAnalysisReportUser = "Backoffice.LayingMaterialAnalysisReportUser";
        private static Guid backofficeLayingMaterialAnalysisReportUserId = Guid.Parse("8b0154c5-8269-4650-a3e9-afdd1aa1669f");

        public const string BackofficeLayingShippingNoteAdministrator = "Backoffice.LayingShippingNoteAdministrator";
        private static Guid backofficeLayingShippingNoteAdministratorId = Guid.Parse("e02a3955-aa34-4575-9c9f-81b56c15cd2a");

        public const string BackofficeLayingShippingNoteUser = "Backoffice.LayingShippingNoteUser";
        private static Guid backofficeLayingShippingNoteUserId = Guid.Parse("6da78d0d-9122-4642-b845-086f887f0972");

        public const string BackofficeLayingReportsAdministrator = "Backoffice.LayingReportsAdministrator";
        private static Guid backofficeLayingReportsAdministratorId = Guid.Parse("67847112-93B7-488C-960F-E78D8FF428C0");

        public const string BackofficeLayingReportsUser = "Backoffice.LayingReportsUser";
        private static Guid backofficeLayingReportsUserId = Guid.Parse("D9FD9948-C0B9-4041-9804-A1F3EDE4085B");

        public const string BackofficeLayingSampleCageReportAdministrator = "Backoffice.LayingSampleCageReportAdministrator";
        private static Guid backofficeLayingSampleCageReportAdministratorId = Guid.Parse("5f5925ab-ed19-4c53-a19d-ce615f93882a");

        public const string BackofficeLayingSampleCageReportUser = "Backoffice.LayingSampleCageReportUser";
        private static Guid backofficeLayingSampleCageReportUserId = Guid.Parse("5c8a6bff-9b17-48c8-8aa7-ea6afd9b150a");

        public const string BackofficeLayingStockAdministrator = "Backoffice.LayingStockAdministrator";
        private static Guid backofficeLayingStockAdministratorId = Guid.Parse("f5a0ad3f-5793-4a4a-8ed6-6d49ca69b2fd");

        public const string BackofficeLayingStockUser = "Backoffice.LayingStockUser";
        private static Guid backofficeLayingStockUserId = Guid.Parse("21519a26-afc3-4636-9a7f-f0af49bcdb29");

        public const string BackofficeLayingTaskAdministrator = "Backoffice.LayingTaskAdministrator";
        private static Guid backofficeLayingTaskAdministratorId = Guid.Parse("DB204C3F-7709-46C7-8BBA-F0489F40379C");

        public const string BackofficeLayingTaskUser = "Backoffice.LayingTaskUser";
        private static Guid backofficeLayingTaskUserId = Guid.Parse("25C6E6C5-D7D7-4B1E-8094-2E7CCCFD4549");

        public const string BackofficeLayingDashboard = "Backoffice.LayingDashboard";
        private static Guid backofficeLayingDashboardId = Guid.Parse("C4F2974D-4975-4600-84CA-815012A9DF8A");

        public const string BackofficeHenBatchFirstProductionDate = "Backoffice.HenBatchFirstProductionDate";
        private static Guid backofficeHenBatchFirstProductionDateId = Guid.Parse("8E30291E-EA2E-4A4C-BE49-A6EB8E790B8A");

        public const string BackofficeLayingBirdMovement = "Backoffice.LayingBirdMovement";
        private static Guid backofficeLayingBirdMovementId = Guid.Parse("5C553666-432D-49DE-BC0A-D47BEA2D8F1F");

        public const string BackofficeLayingBirdMovementAdjustmentApprover = "Backoffice.LayingBirdMovementAdjustmentApprover";
        private static Guid backofficeLayingBirdMovementAdjustmentApproverId = Guid.Parse("542f4d21-3aaf-419e-834f-0cb07f201f9f");

        public const string BackofficeLayingBulkLoad = "Backoffice.LayingBulkLoad";
        private static Guid backofficeLayingBulkLoadId = Guid.Parse("42dfec52-7d8c-4067-b0f5-10e6a29ca7a6");

        public const string HenReportLayingAdjustmentApprover = "Backoffice.LayingAdjustmentApprover";
        private static Guid henReportLayingAdjustmentApproverId = Guid.Parse("f25f7732-b203-4060-9bb0-70e0e6f554a7");

        public const string BackofficeLayingBenchmark = "Backoffice.LayingBenchmark";
        private static Guid backofficeLayingBenchmarkId = Guid.Parse("051A3F8E-8D5C-44AC-9B7E-20B4027BA211");

        public const string SearaDashboardCardsLaying = "Backoffice.SearaDashboardCardsLaying";
        private static Guid searaDashboardCardsLayingId = Guid.Parse("96FEC4B8-91F4-4869-9ED4-7C4BC6C0B122");

        public const string SearaDashboardChartsLaying = "Backoffice.SearaDashboardChartsLaying";
        private static Guid searaDashboardChartsLayingId = Guid.Parse("*************-49C7-BA6A-4B15BD654E4C");

        public const string SearaDashboardDensityChart = "Backoffice.SearaDashboardDensityChart";
        private static Guid searaDashboardDensityChartId = Guid.Parse("37C0909A-2FC7-467B-8104-668FDBD53C9E");

        public const string ManagerialDashboardLaying = "Backoffice.ManagerialDashboardLaying";
        private static Guid managerialDashboardLayingId = Guid.Parse("B14590A2-C96F-41E2-A15E-BE379C9443EA");

        public const string ManagerialInventoryLaying = "Backoffice.ManagerialInventoryLaying";
        private static Guid managerialInventoryLayingId = Guid.Parse("B734E7D9-BB35-49F9-9C95-60A58886923A");
        #endregion

        #region Settings Roles
        public const string BackofficeSettingsAdministrator = "Backoffice.SettingsAdministrator";
        private static Guid backofficeSettingsAdministratorId = Guid.Parse("389B5CF9-8138-4839-BC32-8054F48A36D0");

        public const string BackofficeSystemAdministrator = "Backoffice.SystemAdministrator";
        private static Guid backofficeSystemAdministratorId = Guid.Parse("B808EA5E-00DA-4797-8BF3-02EB8A047710");

        public const string BackofficeTenantAdministrator = "Backoffice.TenantAdministrator";
        private static Guid backofficeTenantAdministratorId = Guid.Parse("76AE0417-7124-4784-A78B-2DBB78729171");

        public const string BackofficeCompanyAdministrator = "Backoffice.CompanyAdministrator";
        private static Guid backofficeCompanyAdministratorId = Guid.Parse("1EB36713-B788-4F48-AD28-59949EC8A749");

        public const string BackofficeGeneticAdministrator = "Backoffice.GeneticAdministrator";
        private static Guid backofficeGeneticAdministratorId = Guid.Parse("4B9B8D0A-9555-486A-815A-0EB61EE3F9A5");

        public const string BackofficeTaskAdministrator = "Backoffice.TaskAdministrator";
        private static Guid backofficeTaskAdministratorId = Guid.Parse("4b739cfc-e707-4965-bf35-0b7a8860ebfc");

        public const string BackofficeTaskUser = "Backoffice.TaskUser";
        private static Guid backofficeTaskUserId = Guid.Parse("cd134152-de1d-48f5-8426-6e3734e5c303");

        public const string BackofficeShippingNoteAdministrator = "Backoffice.ShippingNoteAdministrator";
        private static Guid backofficeShippingNoteAdministratorId = Guid.Parse("2C6E30F0-DEC8-49CE-9019-DAA091305476");

        public const string BackofficeShippingNoteUser = "Backoffice.ShippingNoteUser";
        private static Guid backofficeShippingNoteUserId = Guid.Parse("7C2EA5AA-132F-4F61-8A7F-2D114A007138");

        public const string BackofficeStockAdministrator = "Backoffice.StockAdministrator";
        private static Guid backofficeStockAdministratorId = Guid.Parse("AEED4512-DD3D-4C07-A1AB-BC88D64F532C");

        public const string BackofficeStockUser = "Backoffice.StockUser";
        private static Guid backofficeStockUserId = Guid.Parse("0F55CF5E-A55F-47D8-8EEC-400E5C329C69");

        public const string BackofficeInconsistencyAdministrator = "Backoffice.InconsistencyAdministrator";
        private static Guid backofficeInconsistencyAdministratorId = Guid.Parse("172fd928-4550-44ac-ae74-7d2ce9702270");

        public const string BackofficeInconsistencyUser = "Backoffice.InconsistencyUser";
        private static Guid backofficeInconsistencyUserId = Guid.Parse("7938ba7c-bfc6-4542-9915-d8afe48a59ae");

        public const string BackofficeMaterialAnalysisReportAdministrator = "Backoffice.MaterialAnalysisReportAdministrator";
        private static Guid backofficeMaterialAnalysisReportAdministratorId = Guid.Parse("4604781b-05e1-498e-af71-9f6031f2ad68");

        public const string BackofficeMaterialAnalysisReportUser = "Backoffice.MaterialAnalysisReportUser";
        private static Guid backofficeMaterialAnalysisReportUserId = Guid.Parse("46cf1b93-93f0-434a-97c1-8e3cf57a5e7d");

        public const string BackofficeHappeningAdministrator = "Backoffice.HappeningAdministrator";
        private static Guid backofficeHappeningAdministratorId = Guid.Parse("0bbbff3e-0641-4b78-a2ca-05bac46372bb");

        public const string BackofficeHappeningUser = "Backoffice.HappeningUser";
        private static Guid backofficeHappeningUserId = Guid.Parse("fc7b94df-95d5-4fc5-a276-0b74d1dad9cb");

        public const string BackofficeGeneralFilesAdministrator = "Backoffice.GeneralFilesAdministrator";
        private static Guid backofficeGeneralFilesAdministratorId = Guid.Parse("c97bd425-22d0-44c5-89df-e9b938a25909");

        public const string BackofficeGeneralFilesUser = "Backoffice.GeneralFilesUser";
        private static Guid backofficeGeneralFilesUserId = Guid.Parse("9c5a1a54-8622-4055-a460-2e36b4ae89a3");

        public const string BackofficeFarmAdministrator = "Backoffice.FarmAdministrator";
        private static Guid backofficeFarmAdministratorId = Guid.Parse("159c6fa8-5703-46df-bf19-f8afa17cd741");

        public const string BackofficeFarmUser = "Backoffice.FarmUser";
        private static Guid backofficeFarmUserId = Guid.Parse("E54EB97C-D27C-4A28-ACB2-943A8887F594");
        #endregion

        #region FeedProduction

        public const string BackofficeFeedFactoryAdministrator = "Backoffice.FeedFactoryAdministrator";
        private static Guid backofficeFeedFactoryAdministratorId = Guid.Parse("A3CB9872-C0E8-438A-9F2F-AFAB5EC26968");

        public const string BackofficeFeedFactoryReportAdministrator = "Backoffice.FeedFactoryReportAdministrator";
        private static Guid backofficeFeedFactoryReportAdministratorId = Guid.Parse("4E5B2B8C-249D-4950-A3FE-2921F229A422");

        public const string BackofficeFeedFactoryHappeningAdministrator = "Backoffice.FeedFactoryHappeningAdministrator";
        private static Guid backofficeFeedFactoryHappeningAdministratorId = Guid.Parse("34ea0169-dbf0-40e6-a07e-ef0446029637");

        public const string BackofficeFeedFactoryHappeningUser = "Backoffice.FeedFactoryHappeningUser";
        private static Guid backofficeFeedFactoryHappeningUserId = Guid.Parse("*************-48f1-aa4a-7314a6bd372e");

        public const string BackofficeFeedFactoryStockAdministrator = "Backoffice.FeedFactoryStockAdministrator";
        private static Guid backofficeFeedFactoryStockAdministratorId = Guid.Parse("b0e27f8d-c473-4052-9d82-581060b08952");

        public const string BackofficeFeedFactoryStockUser = "Backoffice.FeedFactoryStockUser";
        private static Guid backofficeFeedFactoryStockUserId = Guid.Parse("6818d02d-f2e0-4a10-b69f-e009aa59369a");

        public const string BackofficeFeedFactoryFormulaAdministrator = "Backoffice.FeedFactoryFormulaAdministrator";
        private static Guid backofficeFeedFactoryFormulaAdministratorId = Guid.Parse("B674EEE3-9845-4296-89AE-4B2B206F2417");

        public const string BackofficeFeedFactoryShippingNoteAdministrator = "Backoffice.FeedFactoryShippingNoteAdministrator";
        private static Guid backofficeFeedFactoryShippingNoteAdministratorId = Guid.Parse("B5164749-DB93-4E58-86A0-B867315E2317");

        public const string BackofficeFeedFactoryShippingNoteUser = "Backoffice.FeedFactoryShippingNoteUser";
        private static Guid backofficeFeedFactoryShippingNoteUserId = Guid.Parse("ca52305a-5730-4d23-a6b8-19be66f7ed33");

        public const string BackofficeFeedFactoryInconsistencyAdministrator = "Backoffice.FeedFactoryInconsistencyAdministrator";
        private static Guid backofficeFeedFactoryInconsistencyAdministratorId = Guid.Parse("C446455B-2F24-4E34-9513-27C362E20579");

        public const string BackofficeFeedFactoryInconsistencyUser = "Backoffice.FeedFactoryInconsistencyUser";
        private static Guid backofficeFeedFactoryInconsistencyUserId = Guid.Parse("d265a9d8-c43c-4286-8a6b-4bb9b5f2c72e");

        public const string BackofficeFeedFactoryMaterialAnalysisReportAdministrator = "Backoffice.FeedFactoryMaterialAnalysisReportAdministrator";
        private static Guid backofficeFeedFactoryMaterialAnalysisReportAdministratorId = Guid.Parse("229c8215-4fb9-49e3-aecc-4f6377717f73");

        public const string BackofficeFeedFactoryMaterialAnalysisReportUser = "Backoffice.FeedFactoryMaterialAnalysisReportUser";
        private static Guid backofficeFeedFactoryMaterialAnalysisReportUserId = Guid.Parse("457e06ff-6cac-457e-8d68-85843560651c");

        public const string BackofficeFeedFactoryProductionReportAdministrator = "Backoffice.FeedFactoryProductionReportAdministrator";
        private static Guid backofficeFeedFactoryProductionReportAdministratorId = Guid.Parse("87C2974D-FCC2-4A38-B6FA-6852565B11B7");

        public const string BackofficeFeedFactoryMaterialAdministrator = "Backoffice.FeedFactoryMaterialAdministrator";
        private static Guid backofficeFeedFactoryMaterialAdministratorId = Guid.Parse("6ED608D5-27F8-495C-89C0-18DE3E2B6728");

        public const string BackofficeFeedFactoryExtraordinaryFoodManufacturingAdministrator = "Backoffice.FeedFactoryExtraordinaryFoodManufacturingAdministrator";
        private static Guid backofficeFeedFactoryExtraordinaryFoodManufacturingAdministratorId = Guid.Parse("ABD7C620-0CC7-4B25-AFE8-5F429261FD34");

        public const string BackofficeFeedFactoryExtraordinaryFoodManufacturingUser = "Backoffice.FeedFactoryExtraordinaryFoodManufacturingUser";
        private static Guid backofficeFeedFactoryExtraordinaryFoodManufacturingUserId = Guid.Parse("8A535D6B-18C4-4A8C-A480-FFAB114CD41E");
        #endregion

        #region Classification

        public const string BackofficeClassificationAdministrator = "Backoffice.ClassificationAdministrator";
        private static Guid backofficeClassificationAdministratorId = Guid.Parse("6d6c2c8b-985e-4da0-aee1-8e618dc3111a");

        public const string BackofficeClassificationUser = "Backoffice.ClassificationUser";
        private static Guid backofficeClassificationUserId = Guid.Parse("d20e1f39-aef6-434f-8e99-b2db8264a156");

        public const string BackofficeClassificationReportAdministrator = "Backoffice.ClassificationReportAdministrator";
        private static Guid backofficeClassificationReportAdministratorId = Guid.Parse("226e8941-ebdf-4f97-8f61-df6d5688e56b");

        public const string BackofficeClassificationReportUser = "Backoffice.ClassificationReportUser";
        private static Guid backofficeClassificationReportUserId = Guid.Parse("2181bb49-0307-4fa1-bfd2-a7e84c54a885");

        public const string BackofficeClassificationReportWithoutDateValidation = "Backoffice.ClassificationReportWithoutDateValidation";
        private static Guid backofficeClassificationReportWithoutDateValidationId = Guid.Parse("f5d231c9-d26c-4295-9bb0-a451a2f39bce");

        public const string BackofficeClassificationHappeningAdministrator = "Backoffice.ClassificationHappeningAdministrator";
        private static Guid backofficeClassificationHappeningAdministratorId = Guid.Parse("c1bf3b84-9a50-496f-9424-2293151ec9bd");

        public const string BackofficeClassificationHappeningUser = "Backoffice.ClassificationHappeningUser";
        private static Guid backofficeClassificationHappeningUserId = Guid.Parse("773fa32c-f42f-4204-adfd-c822ec99f790");

        public const string BackofficeClassificationStockAdministrator = "Backoffice.ClassificationStockAdministrator";
        private static Guid backofficeClassificationStockAdministratorId = Guid.Parse("bc462473-736a-4301-af3c-48c4b1ddd002");

        public const string BackofficeClassificationStockUser = "Backoffice.ClassificationStockUser";
        private static Guid backofficeClassificationStockUserId = Guid.Parse("46e34d4b-dc0c-4d46-a018-c6ee30897095");

        public const string BackofficeClassificationShippingNoteAdministrator = "Backoffice.ClassificationShippingNoteAdministrator";
        private static Guid backofficeClassificationShippingNoteAdministratorId = Guid.Parse("8d391f15-f9aa-4a57-8a2e-aa018b22c65a");

        public const string BackofficeClassificationShippingNoteUser = "Backoffice.ClassificationShippingNoteUser";
        private static Guid backofficeClassificationShippingNoteUserId = Guid.Parse("ddd11c6f-242d-40c3-b122-f995a2b4107b");

        public const string BackofficeClassificationInconsistencyAdministrator = "Backoffice.ClassificationInconsistencyAdministrator";
        private static Guid backofficeClassificationInconsistencyAdministratorId = Guid.Parse("47a90562-ffd6-42d3-bbbd-5a526024d5e0");

        public const string BackofficeClassificationInconsistencyUser = "Backoffice.ClassificationInconsistencyUser";
        private static Guid backofficeClassificationInconsistencyUserId = Guid.Parse("d2ef5416-a1a8-4bd2-b3f7-ed51bca0fbcd");

        public const string BackofficeClassificationMaterialAnalysisReportAdministrator = "Backoffice.ClassificationMaterialAnalysisReportAdministrator";
        private static Guid backofficeClassificationMaterialAnalysisReportAdministratorId = Guid.Parse("cb0771ba-8d5b-45d2-9f34-4c73f19a7017");

        public const string BackofficeClassificationMaterialAnalysisReportUser = "Backoffice.ClassificationMaterialAnalysisReportUser";
        private static Guid backofficeClassificationMaterialAnalysisReportUserId = Guid.Parse("efdc5215-a981-485a-8400-663bd1f671d6");

        #endregion

        #region Packing

        public const string BackofficePackingAdministrator = "Backoffice.PackingAdministrator";
        private static Guid backofficePackingAdministratorId = Guid.Parse("2aee1fb3-987e-442d-baa2-abbf073c475b");

        public const string BackofficePackingHappeningAdministrator = "Backoffice.PackingHappeningAdministrator";
        private static Guid backofficePackingHappeningAdministratorId = Guid.Parse("9cd7c44b-53f5-42f8-a204-b266d312f55a");

        public const string BackofficePackingHappeningUser = "Backoffice.PackingHappeningUser";
        private static Guid backofficePackingHappeningUserId = Guid.Parse("a4de02cc-c75f-4681-8cf1-d8a8ca12cf5f");

        public const string BackofficePackingStockAdministrator = "Backoffice.PackingStockAdministrator";
        private static Guid backofficePackingStockAdministratorId = Guid.Parse("ce1293d0-9753-4cae-b296-e344ecc24545");

        public const string BackofficePackingStockUser = "Backoffice.PackingStockUser";
        private static Guid backofficePackingStockUserId = Guid.Parse("1790c7f6-93ba-4ac1-bfe0-5450e3e76b9a");

        public const string BackofficePackingShippingNoteAdministrator = "Backoffice.PackingShippingNoteAdministrator";
        private static Guid backofficePackingShippingNoteAdministratorId = Guid.Parse("0401f4c6-9140-42d5-b84a-bbe1d7127849");

        public const string BackofficePackingShippingNoteUser = "Backoffice.PackingShippingNoteUser";
        private static Guid backofficePackingShippingNoteUserId = Guid.Parse("d61b4d35-a6bd-4dd9-8980-ad331af093c5");

        public const string BackofficePackingInconsistencyAdministrator = "Backoffice.PackingInconsistencyAdministrator";
        private static Guid backofficePackingInconsistencyAdministratorId = Guid.Parse("d15460df-07d3-4a5a-85f1-c14e46e04e97");

        public const string BackofficePackingInconsistencyUser = "Backoffice.PackingInconsistencyUser";
        private static Guid backofficePackingInconsistencyUserId = Guid.Parse("7b0c4800-1a96-41a8-b484-1c67ab3b4f26");

        public const string BackofficePackingMaterialAnalysisReportAdministrator = "Backoffice.PackingMaterialAnalysisReportAdministrator";
        private static Guid backofficePackingMaterialAnalysisReportAdministratorId = Guid.Parse("396756d2-fe04-4168-9448-80f9402932dc");

        public const string BackofficePackingMaterialAnalysisReportUser = "Backoffice.PackingMaterialAnalysisReportUser";
        private static Guid backofficePackingMaterialAnalysisReportUserId = Guid.Parse("6bc34513-d66e-46ce-a719-5a10155664f0");

        public const string BackofficePackingMovementReportUser = "Backoffice.PackingMovementReportUser";
        private static Guid backofficePackingMovementReportUserId = Guid.Parse("9ff2b8d2-7035-46e2-b6bc-18ae806bb8e3");

        public const string BackofficePackingMovementReportAdministrator = "Backoffice.PackingMovementReportAdministrator";
        private static Guid backofficePackingMovementReportAdministratorId = Guid.Parse("60a9f892-9701-4bf9-8fee-624d9c3163b7");

        public const string BackofficeOrderPreparationUser = "Backoffice.OrderPreparationUser";
        private static Guid backofficeOrderPreparationUserId = Guid.Parse("50f16bee-1df6-4db7-8982-fa3829f63b37");

        public const string BackofficeOrderPreparationAdministrator = "Backoffice.PreparationAdministrator";
        private static Guid backofficeOrderPreparationAdministratorId = Guid.Parse("bf902d77-d1e9-4253-8eb7-c48077985e4b");

        public const string BackofficePackingReportUser = "Backoffice.PackingReportUser";
        private static Guid backofficePackingReportUserId = Guid.Parse("b376c69f-b3e3-4095-8501-d0dbf73ad5df");

        public const string BackofficePackingReportAdministrator = "Backoffice.PackingReportAdministrator";
        private static Guid backofficePackingReportAdministratorId = Guid.Parse("e93311ad-db57-454e-91ad-60cc819e4d6b");

        #endregion

        #region Dispatch

        public const string BackofficeDispatchAdministrator = "Backoffice.DispatchAdministrator";
        private static Guid backofficeDispatchAdministratorId = Guid.Parse("129cbee0-8a4c-470f-8c12-19491b0f9c1b");

        public const string BackofficeDispatchHappeningAdministrator = "Backoffice.DispatchHappeningAdministrator";
        private static Guid backofficeDispatchHappeningAdministratorId = Guid.Parse("040b5635-b8d2-491c-be88-ca02f65ce2c5");

        public const string BackofficeDispatchHappeningUser = "Backoffice.DispatchHappeningUser";
        private static Guid backofficeDispatchHappeningUserId = Guid.Parse("8c599858-b5be-416f-827e-9b4577da9da9");

        public const string BackofficeDispatchStockAdministrator = "Backoffice.DispatchStockAdministrator";
        private static Guid backofficeDispatchStockAdministratorId = Guid.Parse("bbbae10b-bbfe-471c-8799-2f542a6a7101");

        public const string BackofficeDispatchStockUser = "Backoffice.DispatchStockUser";
        private static Guid backofficeDispatchStockUserId = Guid.Parse("f471b52b-508f-4ce6-87bd-c98decb361ca");

        public const string BackofficeDispatchShippingNoteAdministrator = "Backoffice.DispatchShippingNoteAdministrator";
        private static Guid backofficeDispatchShippingNoteAdministratorId = Guid.Parse("7f3150cd-a798-408f-9594-1bc152bf9548");

        public const string BackofficeDispatchShippingNoteUser = "Backoffice.DispatchShippingNoteUser";
        private static Guid backofficeDispatchShippingNoteUserId = Guid.Parse("cc1d256a-c5df-4269-9022-ccf8a56cefa8");

        public const string BackofficeDispatchInconsistencyAdministrator = "Backoffice.DispatchInconsistencyAdministrator";
        private static Guid backofficeDispatchInconsistencyAdministratorId = Guid.Parse("89cc1840-80a4-4d69-ac67-6d94cbb74c03");

        public const string BackofficeDispatchInconsistencyUser = "Backoffice.DispatchInconsistencyUser";
        private static Guid backofficeDispatchInconsistencyUserId = Guid.Parse("254a6d98-c374-48fb-97e2-cd172f4e9147");

        public const string BackofficeDispatchMaterialAnalysisReportAdministrator = "Backoffice.DispatchMaterialAnalysisReportAdministrator";
        private static Guid backofficeDispatchMaterialAnalysisReportAdministratorId = Guid.Parse("ad7fdcf6-cdd4-4127-9262-e24e1cbcf555");

        public const string BackofficeDispatchMaterialAnalysisReportUser = "Backoffice.DispatchMaterialAnalysisReportUser";
        private static Guid backofficeDispatchMaterialAnalysisReportUserId = Guid.Parse("0ead3b09-263f-4594-83a2-7bd449c8b01c");

        public const string BackofficeDispatchMovementReportUser = "Backoffice.DispatchMovementReportUser";
        private static Guid backofficeDispatchMovementReportUserId = Guid.Parse("33855c81-36fb-4b03-b2e8-1635715ca777");

        public const string BackofficeDispatchMovementReportAdministrator = "Backoffice.DispatchMovementReportAdministrator";
        private static Guid backofficeDispatchMovementReportAdministratorId = Guid.Parse("eba70162-de74-4aea-b5c9-a33d99fe56da");

        public const string BackofficeLoadingOrderUser = "Backoffice.LoadingOrderUser";
        private static Guid backofficeLoadingOrderUserId = Guid.Parse("004b57b3-efbf-42fa-af76-d486bd2f86cf");

        public const string BackofficeLoadingOrderAdministrator = "Backoffice.LoadingOrderAdministrator";
        private static Guid backofficeLoadingOrderAdministratorId = Guid.Parse("a976a730-e846-4a32-b240-ae69b6f33923");

        #endregion

        #region HealthCare

        public const string BackofficeHealthCareHappeningAdministrator = "Backoffice.HealthCareHappeningAdministrator";
        private static Guid backofficeHealthCareHappeningAdministratorId = Guid.Parse("faf4cd8a-2ae3-458c-af42-91df589e1f2e");

        public const string BackofficeHealthCareHappeningUser = "Backoffice.HealthCareHappeningUser";
        private static Guid backofficeHealthCareHappeningUserId = Guid.Parse("79bb9834-9a0c-4cf2-9ec4-28a73d9eb403");

        public const string BackofficeHealthCareStockAdministrator = "Backoffice.HealthCareStockAdministrator";
        private static Guid backofficeHealthCareStockAdministratorId = Guid.Parse("66fdc748-9343-4f10-a04e-7a5b2527f863");

        public const string BackofficeHealthCareStockUser = "Backoffice.HealthCareStockUser";
        private static Guid backofficeHealthCareStockUserId = Guid.Parse("7e436410-714c-4ddc-89b5-edb483116c75");

        public const string BackofficeHealthCareShippingNoteAdministrator = "Backoffice.HealthCareShippingNoteAdministrator";
        private static Guid backofficeHealthCareShippingNoteAdministratorId = Guid.Parse("f0216c7e-8934-4170-887b-dde3db1381f5");

        public const string BackofficeHealthCareShippingNoteUser = "Backoffice.HealthCareShippingNoteUser";
        private static Guid backofficeHealthCareShippingNoteUserId = Guid.Parse("4afce329-5a17-42e1-a280-f310284fb18d");

        public const string BackofficeHealthCareInconsistencyAdministrator = "Backoffice.HealthCareInconsistencyAdministrator";
        private static Guid backofficeHealthCareInconsistencyAdministratorId = Guid.Parse("37c98639-f3c9-474a-be68-c455c7a9980e");

        public const string BackofficeHealthCareInconsistencyUser = "Backoffice.HealthCareInconsistencyUser";
        private static Guid backofficeHealthCareInconsistencyUserId = Guid.Parse("b62629cf-6a03-410d-b540-8e947cc64878");

        public const string BackofficeHealthCareMaterialAnalysisReportAdministrator = "Backoffice.HealthCareMaterialAnalysisReportAdministrator";
        private static Guid backofficeHealthCareMaterialAnalysisReportAdministratorId = Guid.Parse("69865001-1d94-45f4-b50b-15ffbe33b624");

        public const string BackofficeHealthCareMaterialAnalysisReportUser = "Backoffice.HealthCareMaterialAnalysisReportUser";
        private static Guid backofficeHealthCareMaterialAnalysisReportUserId = Guid.Parse("5aa77697-865e-4664-9231-5931d46da53c");

        public const string BackofficeHealthCareAdministrator = "Backoffice.HealthCareAdministrator";
        private static Guid backofficeHealthCareAdministratorId = Guid.Parse("7702665D-89C8-4DD1-B448-86A6168564F4");

        public const string BackofficeHealthCareBestPracticeAdministrator = "Backoffice.HealthCareBestPracticeAdministrator";
        private static Guid backofficeHealthCareBestPracticeAdministratorId = Guid.Parse("2472780B-A62D-432F-9D80-BB5DF4286712");

        public const string BackofficeHealthCareBestPracticeUser = "Backoffice.HealthCareBestPracticeUser";
        private static Guid backofficeHealthCareBestPracticeUserId = Guid.Parse("511E906D-3991-4D91-9F7E-AC74605156B4");

        public const string BackofficeHealthCareTaskAdministrator = "Backoffice.HealthCareTaskAdministrator";
        private static Guid backofficeHealthCareTaskAdministratorId = Guid.Parse("EC1C6E6B-EF1A-4E2F-92A4-85D169FE34DE");

        public const string BackofficeHealthCareTaskUser = "Backoffice.HealthCareTaskUser";
        private static Guid backofficeHealthCareTaskUserId = Guid.Parse("0A353AA2-C6D2-4A27-9E64-20E77D4F7C28");

        public const string BackofficeHealthCareReportAdministrator = "Backoffice.HealthCareReportAdministrator";
        private static Guid backofficeHealthCareReportAdministratorId = Guid.Parse("96A1D9B3-66AA-41A5-94D6-E9456386A3B4");

        public const string BackofficeHealthCareReportUser = "Backoffice.HealthCareReportUser";
        private static Guid backofficeHealthCareReportUserId = Guid.Parse("E6C7962A-09E7-49C1-B726-B9C9E8EEBCF1");

        #endregion

        #region SalesOrder
        public const string BackofficeSalesOrderCreator = "Backoffice.SalesOrderCreator";
        private static Guid backofficeSalesOrderCreatorId = Guid.Parse("7DD34E38-7158-4248-B5FE-3D365C4F2DC3");

        public const string BackofficeSalesOrderApprover = "Backoffice.SalesOrderApprover";
        private static Guid backofficeSalesOrderApproverId = Guid.Parse("A7650DB1-2F9A-44A6-BB8B-9CE0BE92B8E3");

        public const string BackofficeSalesOrderAdministrator = "Backoffice.SalesOrderAdministrator";
        private static Guid backofficeSalesOrderAdministratorId = Guid.Parse("181DF703-55AE-46F3-B0F1-605B1380C925");
        #endregion

        #region Devolutions
        public const string BackofficeReturnsAdministrator = "Backoffice.ReturnsAdministrator";
        private static Guid backofficeReturnsAdministratorId = Guid.Parse("423ac199-393d-453f-977f-a21b41d337b1");

        public const string BackofficeReturnReportAdministrator = "Backoffice.ReturnReportAdministrator";
        private static Guid backofficeReturnReportAdministratorId = Guid.Parse("D22525A1-CC8B-40A9-9D3D-41B5CFB64063");

        public const string BackofficeReturnsStockAdministrator = "Backoffice.ReturnsStockAdministrator";
        private static Guid backofficeReturnsStockAdministratorId = Guid.Parse("aeb5f416-d039-4856-9220-e0facb2f7c48");

        public const string BackofficeReturnsStockUser = "Backoffice.ReturnsStockUser";
        private static Guid backofficeReturnsStockUserId = Guid.Parse("859f82b0-9b42-415b-9eb1-c0ce5f5406e3");
        #endregion

        #region MovementReports
        public const string BackofficeMovementReportUser = "Backoffice.MovementReportUser";
        private static Guid backofficeMovementReportUserId = Guid.Parse("83DD56E3-590C-4BD0-9F0F-B9B06C910666");

        public const string BackofficeMovementReportAdministrator = "Backoffice.MovementReportAdministrator";
        private static Guid backofficeMovementReportAdministratorId = Guid.Parse("87ABC925-EE94-450F-A1AC-43F5229BCB1F");
        #endregion

        #region IgniteEntityRoles
        // Roles per entity.
        public const string BackofficeBackofficeUserAdministrator = "Backoffice.BackofficeUserAdministrator";
        private static Guid backofficeBackofficeUserAdministratorId = Guid.Parse("ED63B004-3AA6-4A01-92EE-C43D0BF6AA7C");

        public const string BackofficeFrontUserAdministrator = "Backoffice.FrontUserAdministrator";
        private static Guid backofficeFrontUserAdministratorId = Guid.Parse("A4E0835D-78D3-403A-872E-2E5D01F6B7BA");

        public const string BackofficeSystemUser = "Backoffice.SystemUser";
        private static Guid backofficeSystemUserId = Guid.Parse("BB9DF2C4-E9F1-4488-8A84-41B36CE5FFAD");

        public const string BackofficeSchedulerAdministrator = "Backoffice.SchedulerAdministrator";
        private static Guid backofficeSchedulerAdministratorId = Guid.Parse("47e534c6-67d6-4fd0-8587-9b58c35a09f9");

        public const string BackofficeSchedulerUser = "Backoffice.SchedulerUser";
        private static Guid backofficeSchedulerUserId = Guid.Parse("6B7232EB-F2EF-4578-AC54-F34F9EFF3718");

        public const string BackofficeTransactionDocumentUser = "Backoffice.TransactionDocumentUser";
        private static Guid backofficeTransactionDocumentUserId = Guid.Parse("69c32ae1-fe94-4e30-8a1f-ac821825e5d5");

        #endregion

        #region Front Realm

        // Super admin.
        public const string FrontSuperAdministrator = "Front.SuperAdministrator";
        private static Guid frontSuperAdministratorId = Guid.Parse("1792BCC1-7A65-47BD-B0FC-AD0A3B1962B4");

        // Roles per entity.
        public const string FrontFrontUserAdministrator = "Front.FrontUserAdministrator";
        private static Guid frontFrontUserAdministratorId = Guid.Parse("9E11FC55-C2EA-47E4-83E0-6A6D3C5E2BBC");

        #endregion

        #region Maintenance

        public const string BackofficeMaintenanceAdministrator = "Backoffice.MaintenanceAdministrator";
        private static Guid backofficeMaintenanceAdministratorId = Guid.Parse("a1381faf-27a7-40ac-b809-66b0c3921959");

        public const string BackofficeMaintenanceHappeningAdministrator = "Backoffice.MaintenanceHappeningAdministrator";
        private static Guid backofficeMaintenanceHappeningAdministratorId = Guid.Parse("2900b47b-e29e-410a-b385-09bd423c2adb");

        public const string BackofficeMaintenanceHappeningUser = "Backoffice.MaintenanceHappeningUser";
        private static Guid backofficeMaintenanceHappeningUserId = Guid.Parse("cb0d9970-93c6-4070-948e-371fa028261a");
        #endregion

        #region OperationsPivotGrid

        public const string BackofficeOperationsPivotGridAdministrator = "Backoffice.OperationsPivotGridAdministrador";
        private static Guid backofficeOperationsPivotGridAdministratorId = Guid.Parse("4ce4120e-6602-4831-bceb-4ff719e31f41");

        public const string BackofficeOperationsPivotGridUser = "Backoffice.OperationsPivotGridUsuario";
        private static Guid backofficeOperationsPivotGridUserId = Guid.Parse("5cea6b26-a2b7-44cb-bf5b-67bd4ec851fc");
        #endregion

        #region Analytics 

        public const string BreedingAnalyticsCorporateAdministrator = "Backoffice.BreedingAnalyticsCorporateAdministrator";
        private static Guid breedingAnalyticsCorporateAdministratorId = Guid.Parse("cb1ccdc9-5190-471d-bfab-7b4724bf78be");

        public const string BreedingAnalyticsPersonalAdministrator = "Backoffice.BreedingAnalyticsPersonalAdministrator";
        private static Guid breedingAnalyticsPersonalAdministratorId = Guid.Parse("46b80afd-88c1-4ef2-b7f3-0a4359f99d28");

        public const string BreedingAnalyticsUser = "Backoffice.BreedingAnalyticsUser";
        private static Guid breedingAnalyticsUserId = Guid.Parse("3781911c-7cce-4d74-8003-c18e98be1745");

        public const string LayingAnalyticsCorporateAdministrator = "Backoffice.LayingAnalyticsCorporateAdministrator";
        private static Guid layingAnalyticsCorporateAdministratorId = Guid.Parse("18f4583b-d4cf-4869-bf7f-00149d2fbdc6");

        public const string LayingAnalyticsPersonalAdministrator = "Backoffice.LayingAnalyticsPersonalAdministrator";
        private static Guid layingAnalyticsPersonalAdministratorId = Guid.Parse("a2005bcc-2a77-42db-8bb1-e9b936da1d8d");

        public const string LayingAnalyticsUser = "Backoffice.LayingAnalyticsUser";
        private static Guid layingAnalyticsUserId = Guid.Parse("b77e6a28-1147-411e-a2ab-3f5f82281508");
        #endregion

        #region Notification 
        public const string NotificationAdministrator = "Backoffice.NotificationAdministrator";
        private static Guid notificationAdministratorId = Guid.Parse("745227fd-2ba4-4abc-b714-330b12b83a12");

        public const string NotificationUser = "Backoffice.NotificationUser";
        private static Guid notificationUserId = Guid.Parse("1202434f-2ce1-4312-ac6d-1e62c320bb13");

        public const string NotificationTypeAdministrator = "Backoffice.NotificationTypeAdministrator";
        private static Guid notificationTypeAdministratorId = Guid.Parse("44af1373-f5cf-4be5-a39f-d7f27a1b38b6");

        public const string NotificationTypeUser = "Backoffice.NotificationTypeUser";
        private static Guid notificationTypeUserId = Guid.Parse("92e24057-b0e2-4ca8-bbe1-d4b5a1b92b09");
        #endregion

        /// <summary>
        /// Obtains and returns all the roles defined as constants in this class by reflection.
        /// </summary>
        /// <param name="includeSuperAdmin">Indicates whether the super administrator is included</param>
        public static ICollection<string> GetAllRoles(bool includeSuperAdmin = false, bool includeSettingsRoles = false)
        {
            IEnumerable<string> roles = typeof(Roles)
                .GetFields(BindingFlags.Static | BindingFlags.Public)
                .Select(f => f.GetValue(null).ToString());

            if (!includeSuperAdmin)
            {
                roles = roles.Where(f => f != BackofficeSuperAdministrator && f != FrontSuperAdministrator);
            }

            if (!includeSettingsRoles)
            {
                string[] settingsRoles = {
                    BackofficeSettingsAdministrator,
                    BackofficeSystemAdministrator,
                    BackofficeTenantAdministrator,
                    BackofficeGeneticAdministrator
                };
                roles = roles.Except(settingsRoles);
            }

            return roles.ToList();
        }

        /// <summary>
        /// Obtains and returns all the roles from the current realm.
        /// </summary>
        /// <param name="includeSuperAdmin">Indicates whether the super administrator is included</param>
        /// <param name="realm">Indicates the current realm (front or backoffice)</param>
        public static ICollection<string> GetRolesByRealm(string realm, bool includeSuperAdmin = false, bool includeSettingsRoles = false)
        {
            realm = realm == UserTypes.FrontUser ? "Front" : "Backoffice";
            return GetAllRoles(includeSuperAdmin, includeSettingsRoles)
                .Where(r => r.StartsWith($"{realm}."))
                .ToList();
        }

        /// <summary>
        /// Get the id from the current rol
        /// </summary>
        /// <param name="rol">Name of the rol you want to get the id</param>
        public static Guid GetId(string rol)
        {
            var publicFields = typeof(Roles).GetFields(BindingFlags.Static | BindingFlags.Public);
            var rolName = publicFields.FirstOrDefault(f => f.GetValue(null).ToString() == rol).Name;

            var idFieldName = $"{Char.ToLowerInvariant(rolName[0])}{rolName.Substring(1)}Id";
            var privateFields = typeof(Roles).GetFields(BindingFlags.Static | BindingFlags.NonPublic);
            return Guid.Parse(privateFields.FirstOrDefault(f => f.Name == idFieldName).GetValue(null).ToString());
        }

        /// <summary>
        /// Get the name property of the roleId 
        /// </summary>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public static string GetName(Guid roleId)
        {
            var privateFields = typeof(Roles).GetFields(BindingFlags.Static | BindingFlags.NonPublic);
            var publicFields = typeof(Roles).GetFields(BindingFlags.Static | BindingFlags.Public);

            var roleIdField = privateFields.FirstOrDefault(f => (Guid)f.GetValue(null) == roleId);
            var roleNameField = publicFields.FirstOrDefault(f => f.Name.ToLower() == roleIdField.Name[0..^2].ToLower());
            return roleNameField.GetValue(null).ToString();
        }

        /// <summary>
        /// Get the role name by his localization
        /// </summary>
        /// <param name="localizer">Localizer that provides the string from shared resources for the role name you want to get</param>
        /// <param name="role">Role name you want to get the localized name</param>
        public static string GetDisplayName(string role, IStringLocalizer<SharedResources> localizer)
        {
            var localizationConstant = $"{typeof(Roles).FullName}.{role}";

            return localizer[localizationConstant];
        }
    }
}