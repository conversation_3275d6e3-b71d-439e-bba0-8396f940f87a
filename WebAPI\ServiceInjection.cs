using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Extensions;
using Binit.Framework.Helpers;
using Binit.Framework.Helpers.Configuration;
using Binit.Framework.Helpers.Email;
using Binit.Framework.Helpers.Jwt;
using Binit.Framework.Interfaces.Cache;
using Binit.Framework.Interfaces.Configuration;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.Email;
using Binit.Framework.OperationContext;
using Domain.Entities.Model;
using Domain.Logic.BatchProcessFactory;
using Domain.Logic.BatchProcessFactory.BatchEntities;
using Domain.Logic.BusinessLogic;
using Domain.Logic.BusinessLogic.BatchBusinessLogic;
using Domain.Logic.BusinessLogic.ChartsBusinessLogic;
using Domain.Logic.BusinessLogic.ContainerBusinessLogic;
using Domain.Logic.BusinessLogic.HenReportBusinessLogic;
using Domain.Logic.BusinessLogic.InconsistencyReportBusinessLogic;
using Domain.Logic.BusinessLogic.PackingReportBusinessLogic;
using Domain.Logic.BusinessLogic.UpdateHenBatchPerformanceBusinessLogic;
using Domain.Logic.ExternalBusinessLogics.ERP.Material;
using Domain.Logic.ExternalBusinessLogics.ERP.Persons;
using Domain.Logic.ExternalServices.ERP;
using Domain.Logic.ExternalServices.ERP.Account;
using Domain.Logic.ExternalServices.FileManager;
using Domain.Logic.Interfaces;
using Domain.Logic.Logging;
using Domain.Logic.Services;
using Microsoft.AspNetCore.Authentication.OAuth;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using WebAPI.Middleware;

namespace WebAPI
{
    /// <summary>
    /// WebAPI's service injection.
    /// </summary>
    public class ServiceInjection : AbstractServiceInjection
    {
        public ServiceInjection(IServiceCollection services, IConfiguration configuration)
            : base(services, configuration)
        {
        }

        /// <summary>
        /// Registers all services from the Domain.Logic Layer required by this specific Project.
        /// </summary>
        public override IServiceCollection Initialize()
        {
            Services.AddApplicationInsightsTelemetry();
            Services.AddScoped<ExceptionHandlerMiddleware>();
            Services.AddScoped<IOperationContext, WebOperationContext>();
            Services.AddScoped<IContextStorageMemoryCache, ContextStorageMemoryCache>();
            Services.AddScoped<JWTHelper>();
            Services.AddHttpClient("erpExternalService");
            AddConfigurations();
            AddServices();
            AddBatchEntityProcessorStrategies();
            AddBusinessLogics();
            AddEmailService();
            AddAuthentication();

            Services.AddScoped<MtechAuditoryLog>();

            return Services;
        }

        private void AddConfigurations()
        {
            Services.AddScoped<IGeneralConfiguration, GeneralConfiguration>();
            Services.AddScoped<IRealmConfiguration, RealmConfiguration>();
        }

        private void AddServices()
        {
            Services.AddScoped<IAccountService, AccountService>();
            Services.AddScoped<ICapacityUnitService, CapacityUnitService>();
            Services.AddScoped<ICasualtyReasonService, CasualtyReasonService>();
            Services.AddScoped<IReportPlannerProgramService, ReportPlannerProgramService>();
            Services.AddScoped<ICertificationService, CertificationService>();
            Services.AddScoped<IClassificationReportService, ClassificationReportService>();
            Services.AddScoped<IClassificationWarehouseService, ClassificationWarehouseService>();
            Services.AddScoped<IClusterService, ClusterService>();
            Services.AddScoped<ICompanyService, CompanyService>();
            Services.AddScoped<ICompositionService, CompositionService>();
            Services.AddScoped(typeof(IContainerService<>), typeof(ContainerService<>));
            Services.AddScoped<IContainerService<Container>, ContainerService<Container>>();
            Services.AddScoped<IDepopulationReasonService, DepopulationReasonService>();
            Services.AddScoped<IEggDensityReportService, EggDensityReportService>();
            Services.AddScoped<IEggQualityReportService, EggQualityReportService>();
            Services.AddScoped<IEggWeightReportService, EggWeightReportService>();
            Services.AddScoped<IExternalServiceService, ExternalServiceService>();
            Services.AddScoped<IFarmService, FarmService>();
            Services.AddScoped<IFeedFactoryService, FeedFactoryService>();
            Services.AddScoped<IFertilityReportService, FertilityReportService>();
            Services.AddScoped<IFileManagerService, FileManagerService>();
            Services.AddScoped<IFormulaService, FormulaService>();
            Services.AddScoped<IFileManagerService, FileManagerService>();
            Services.AddScoped<IGeneticBusinessLogic, GeneticBusinessLogic>();
            Services.AddScoped<IGeneticsParameterService, GeneticsParametersService>();
            Services.AddScoped<IGeneticService, GeneticService>();
            Services.AddScoped<IFertilityReportService, FertilityReportService>();
            Services.AddScoped<IHatcheringReportService, HatcheringReportService>();
            Services.AddScoped<IHenBatchCategoryService, HenBatchCategoryService>();
            Services.AddScoped<IHenBatchPerformanceService, HenBatchPerformanceService>();
            Services.AddScoped<IHenBatchService, HenBatchService>();
            Services.AddScoped<IHenReportService, HenReportService>();
            Services.AddScoped<IHenWarehouseService, HenWarehouseService>();
            Services.AddScoped<IInconsistencyReportService, InconsistencyReportService>();
            Services.AddScoped<IIgniteAddressService, IgniteAddressService>();
            Services.AddScoped<ILineService, LineService>();
            Services.AddScoped<IMaterialService, MaterialService>();
            Services.AddScoped<IMaterialReceptionReportService, MaterialReceptionReportService>();
            Services.AddScoped<IMaterialTypeService, MaterialTypeService>();
            Services.AddScoped<IPackingReportService, PackingReportService>();
            Services.AddScoped<IPackingWarehouseService, PackingWarehouseService>();
            Services.AddScoped<IPersonService, PersonService>();
            Services.AddScoped<ISalesOrderService, SalesOrderService>();
            Services.AddScoped<ISampleCageReportService, SampleCageReportService>();
            Services.AddScoped<ISectorService, SectorService>();
            Services.AddScoped<IShippingNoteConciliationService, ShippingNoteConciliationService>();
            Services.AddScoped<IShippingNoteService, ShippingNoteService>();
            Services.AddScoped<ISiloService, SiloService>();
            Services.AddScoped<ISlaughterhouseService, SlaughterhouseService>();
            Services.AddScoped<ISKUService, SKUService>();
            Services.AddScoped<IStorageWarehouseService, StorageWarehouseService>();
            Services.AddScoped<ITenantService, TenantService>();
            Services.AddScoped(typeof(IUserService<>), typeof(UserService<>));
            Services.AddScoped<IMaterialReceptionReportService, MaterialReceptionReportService>();
            Services.AddScoped<IMaterialDistributionReportService, MaterialDistributionReportService>();

            #region ExternalServices
            Services.AddScoped<IERPAPIAccountService, ERPAPIAccountService>();
            Services.AddScoped<IERPOperationContext, ERPOperationContext>();
            Services.AddSingleton<IContextStorageMemoryCache, ContextStorageMemoryCache>();
            #endregion
        }

        private void AddBusinessLogics()
        {
            Services.AddScoped<ICapacityUnitBusinessLogic, CapacityUnitBusinessLogic>();
            Services.AddScoped<IClassificationReportBusinessLogic, ClassificationReportBusinessLogic>();
            Services.AddScoped<ICommonChartsBusinessLogic, CommonChartsBusinessLogic>();
            Services.AddScoped<ICompanyBusinessLogic, CompanyBusinessLogic>();
            Services.AddScoped<ICompositionBusinessLogic, CompositionBusinessLogic>();
            Services.AddScoped<IContainerBusinessLogic, ContainerBusinessLogic>();
            Services.AddScoped<ICompositionBusinessLogic, CompositionBusinessLogic>();
            Services.AddScoped<IExcelExportBusinessLogic, ExcelExportBusinessLogic>();
            Services.AddScoped<IFarmBusinessLogic, FarmBusinessLogic>();
            Services.AddScoped<IFertilityReportBusinessLogic, FertilityReportBusinessLogic>();
            Services.AddScoped<IHatcheringReportBusinessLogic, HatcheringReportBusinessLogic>();
            Services.AddScoped<IHenBatchBusinessLogic, HenBatchBusinessLogic>();
            Services.AddScoped<IHenBatchPerformanceBusinessLogic, HenBatchPerformanceBusinessLogic>();
            Services.AddScoped<IHenReportBusinessLogic, HenReportBusinessLogic>();
            Services.AddScoped<IHenWarehouseBusinessLogic, HenWarehouseBusinessLogic>();
            Services.AddScoped<IInconsistencyReportBusinessLogic, InconsistencyReportBusinessLogic>();
            Services.AddScoped<IMaterialBusinessLogic, MaterialBusinessLogic>();
            Services.AddScoped<IMaterialReceptionReportBusinessLogic, MaterialReceptionReportBusinessLogic>();
            Services.AddScoped<IPackingReportBusinessLogic, PackingReportBusinessLogic>();
            Services.AddScoped<IUpdateHenBatchPerformanceBusinessLogic, UpdateHenBatchPerformanceBusinessLogic>();
            Services.AddScoped<ISampleCageReportBusinessLogic, SampleCageReportBusinessLogic>();
            Services.AddScoped<ISKUBusinessLogic, SKUBusinessLogic>();
            Services.AddScoped<IUpsertHenbatchBusinessLogic, UpsertHenbatchBusinessLogic>();
            Services.AddScoped<IEggWeightReportBusinessLogic, EggWeightReportBusinessLogic>();

            #region ExternalBusinessLogic
            Services.AddScoped<IERPAPIPersonBusinessLogic, ERPAPIPersonBusinessLogic>();
            Services.AddScoped<IERPAPIMaterialBusinessLogic, ERPAPIMaterialBusinessLogic>();
            #endregion
        }

        private void AddBatchEntityProcessorStrategies()
        {
            Services.AddTransient<IBatchProcessService, BatchProcessService>();

            Services.AddTransient<HatcheringReportProcessor>();
            Services.AddTransient<FertilityReportProcessor>();

            Services.AddTransient<Func<string, IBatchEntityProcessor>>(serviceProvider => key => key switch
            {
                nameof(HatcheringReport) => serviceProvider.GetService<HatcheringReportProcessor>(),
                nameof(FertilityReport) => serviceProvider.GetService<FertilityReportProcessor>(),
                _ => throw new ArgumentException("no existe una estrategia para la entidad")
            });

            Services.AddTransient<IBatchProcessHenbatchPerformanceBusinessLogic, BatchProcessHenbatchPerformanceBusinessLogic>();
        }

        private void AddEmailService()
        {
            //TODO:  
            //Use AppSettings / User Secret

            SmtpConfiguration smtpConfig = new SmtpConfiguration().Bind(Configuration);

            //TODO: If not configured there must be an exception telling so
            Services.AddFluentEmail(smtpConfig.Address)
                .AddRazorRenderer()
                .AddSmtpSender(smtpConfig.GenerateSmtpClient());

            Services.AddTransient<IEmailSender, EmailSender>();
        }

        private void AddAuthentication()
        {
            Services.AddAuthentication()
                .AddGoogle(googleOptions =>
                {
                    OAuthOptions options = Configuration.GetAuthenticationSection<OAuthOptions>(SocialLoginConstants.Google);
                    googleOptions.ClientId = options.ClientId;
                    googleOptions.ClientSecret = options.ClientSecret;
                })
                .AddFacebook(facebookOptions =>
                {
                    OAuthOptions options = Configuration.GetAuthenticationSection<OAuthOptions>(SocialLoginConstants.Facebook);
                    facebookOptions.ClientId = options.ClientId;
                    facebookOptions.ClientSecret = options.ClientSecret;
                })
                .AddTwitter(twitterOptions =>
                {
                    OAuthOptions options = Configuration.GetAuthenticationSection<OAuthOptions>(SocialLoginConstants.Twitter);
                    twitterOptions.ConsumerKey = options.ClientId;
                    twitterOptions.ConsumerSecret = options.ClientSecret;
                });
        }
    }
}
