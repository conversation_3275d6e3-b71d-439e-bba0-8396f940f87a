using Binit.Framework;
using Binit.Framework.Attributes;
using Binit.Framework.Constants.SeedEntities;
using Domain.Entities.Model;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using WebApp.Attributes;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Models.SampleCageReportViewModel;

namespace WebApp.Models
{
    public class WarehouseSampleCageReportViewModel : EntityViewModel, IValidatableObject
    {
        [Display(Name = Lang.Date)]
        [Required(ErrorMessage = Lang.DateRequired)]
        [FutureDatesNotAllowed(ErrorMessage = Lang.GreaterThanToday)]
        public string Date { get; set; }

        [Required(ErrorMessage = "Selecione uma fazenda")]
        public Guid? FarmId { get; set; }

        [Required(ErrorMessage = Lang.WarehouseRequired)]
        [Display(Name = Lang.WarehouseLabel)]
        public string WarehouseId { get; set; }

        [Display(Name = Lang.WarehouseLabel)]
        public string WarehouseName { get; set; }

        public List<SampleCageMeasurementViewModel> SampleCageMeasurements { get; set; }

        public HenStage HenStage { get; set; }

        public bool ReportConfig { get; set; }
        public bool HasFemale { get; set; }
        public bool HasMale { get; set; }

        public string ValidationErrors { get; set; }
        [Range(0, 10000, ErrorMessage = Lang.HenWarehouseAvgFemaleBirdWeightRangeError)]
        [Display(Name = Lang.HenWarehouseAvgFemaleBirdWeightLabel)]
        public decimal? HenWarehouseAvgFemaleBirdWeight { get; set; }
        [Range(0, 10000, ErrorMessage = Lang.HenWarehouseAvgMaleBirdWeightRangeError)]
        [Display(Name = Lang.HenWarehouseAvgMaleBirdWeightLabel)]
        public decimal? HenWarehouseAvgMaleBirdWeight { get; set; }

        [Display(Name = Lang.BirdWeightMeasure)]
        public string HenWarehouseFemaleBirdWeightMeasureId { get; set; }

        [Display(Name = Lang.BirdWeightMeasure)]
        public string HenWarehouseMaleBirdWeightMeasureId { get; set; }

        [Display(Name = Lang.HenWarehouseVariationCoefficientFemaleLabel)]
        [Range(0, 100, ErrorMessage = Lang.HenWarehouseVariationCoefficientFemaleRangeError)]
        public decimal? HenWarehouseVariationCoeficentFemale { get; set; }

        [Display(Name = Lang.HenWarehouseVariationCoefficientMaleLabel)]
        [Range(0, 100, ErrorMessage = Lang.HenWarehouseVariationCoefficientMaleRangeError)]
        public decimal? HenWarehouseVariationCoeficentMale { get; set; }

        [Display(Name = Lang.HenWarehouseUniformityFemaleLabel)]
        [Range(0, 100, ErrorMessage = Lang.HenWarehouseUniformityFemaleRangeError)]
        public decimal? HenWarehouseUniformityFemale { get; set; }

        [Display(Name = Lang.HenWarehouseUniformityMaleLabel)]
        [Range(0, 100, ErrorMessage = Lang.HenWarehouseUniformityMaleRangeError)]
        public decimal? HenWarehouseUniformityMale { get; set; }
        public List<SelectListItem> HenWarehouseFemaleBirdWeightMeasures { get; set; }
        public List<SelectListItem> HenWarehouseMaleBirdWeightMeasures { get; set; }
        public string HenBatchId { get; set; }

        public WarehouseSampleCageReportViewModel()
        {
            this.Id = new Guid().ToString();
            this.Date = DateTime.Now.ToString();
            this.SampleCageMeasurements = new List<SampleCageMeasurementViewModel>();
            this.HenBatchId = HenBatchId;
        }

        //Model to create a sample cage report 
        public WarehouseSampleCageReportViewModel(HenStage henStage)
        {
            this.Id = new Guid().ToString();
            this.Date = DateTime.Now.ToString();
            this.HenStage = henStage;
            this.SampleCageMeasurements = new List<SampleCageMeasurementViewModel>();
        }
        public WarehouseSampleCageReportViewModel(SampleCageReport sampleCageReport, bool reportConfig)
        {
            this.Id = sampleCageReport.Id.ToString();
            this.Date = sampleCageReport.Date.ToString();
            this.HenStage = sampleCageReport.HenBatch.HenStage;
            this.SampleCageMeasurements = new List<SampleCageMeasurementViewModel>();
            this.ReportConfig = reportConfig;
            this.HenWarehouseAvgFemaleBirdWeight = sampleCageReport.HenWarehouseAvgFemaleBirdWeight;
            this.HenWarehouseAvgMaleBirdWeight = sampleCageReport.HenWarehouseAvgMaleBirdWeight;

            if (sampleCageReport.HenWarehouseUniformityFemale.HasValue)
                this.HenWarehouseUniformityFemale = decimal.Round(sampleCageReport.HenWarehouseUniformityFemale.Value, 2);

            if (sampleCageReport.HenWarehouseUniformityMale.HasValue)
                this.HenWarehouseUniformityMale = decimal.Round(sampleCageReport.HenWarehouseUniformityMale.Value, 2);

            if (sampleCageReport.HenWarehouseVariationCoefficientFemale.HasValue)
                this.HenWarehouseVariationCoeficentFemale = decimal.Round(sampleCageReport.HenWarehouseVariationCoefficientFemale.Value, 2);

            if (sampleCageReport.HenWarehouseVariationCoefficientMale.HasValue)
                this.HenWarehouseVariationCoeficentMale = decimal.Round(sampleCageReport.HenWarehouseVariationCoefficientMale.Value, 2);

            this.HenBatchId = sampleCageReport.HenBatchId.ToString();
            if (sampleCageReport.SampleCageMeasurement != null && sampleCageReport.SampleCageMeasurement.Any())
            {
                foreach (SampleCageMeasurement cage in sampleCageReport.SampleCageMeasurement)
                {
                    this.SampleCageMeasurements.Add(new SampleCageMeasurementViewModel(cage));
                }
            }
        }

        public List<SampleCageReport> ToEntity()
        {
            List<SampleCageReport> sampleCageReports = new List<SampleCageReport>();
            SampleCageMeasurementViewModel warehouseMeasures = SampleCageMeasurements.FirstOrDefault(sc => sc.WarehouseMeasures);
            foreach (IGrouping<string, SampleCageMeasurementViewModel> measures in SampleCageMeasurements.Where(sc => !sc.WarehouseMeasures).GroupBy(scm => scm.HenBatchId))
            {
                SampleCageReport report = new SampleCageReport()
                {
                    Date = DateTime.Parse(this.Date),
                    SampleCageMeasurement = new List<SampleCageMeasurement>(),
                    HenBatchId = Guid.Parse(measures.Key),
                    HenWarehouseAvgFemaleBirdWeight = warehouseMeasures == null
                    ? (decimal?)null
                    : warehouseMeasures.AvgFemaleBirdWeight,
                    HenWarehouseAvgMaleBirdWeight = warehouseMeasures == null
                    ? (decimal?)null
                    : warehouseMeasures.AvgMaleBirdWeight,
                    HenWarehouseUniformityFemale = warehouseMeasures == null
                    ? (decimal?)null
                    : warehouseMeasures.UniformityFemale,
                    HenWarehouseUniformityMale = warehouseMeasures == null
                    ? (decimal?)null
                    : warehouseMeasures.UniformityMale,
                    HenWarehouseVariationCoefficientFemale = warehouseMeasures == null
                    ? (decimal?)null
                    : warehouseMeasures.VariationCoefficientFemale,
                    HenWarehouseVariationCoefficientMale = warehouseMeasures == null
                    ? (decimal?)null
                    : warehouseMeasures.VariationCoefficientMale
                };


                if (warehouseMeasures != null && warehouseMeasures.FemaleBirdWeightMeasureId != null)
                    report.HenWarehouseFemaleBirdWeightCapacityUnitId = Guid.Parse(warehouseMeasures.FemaleBirdWeightMeasureId);
                if (warehouseMeasures != null && warehouseMeasures.MaleBirdWeightMeasureId != null)
                    report.HenWarehouseMaleBirdWeightCapacityUnitId = Guid.Parse(warehouseMeasures.MaleBirdWeightMeasureId);

                if (!string.IsNullOrEmpty(measures.FirstOrDefault().HenBatchPerformanceId))
                    report.HenBatchPerformanceId = new Guid(measures.FirstOrDefault().HenBatchPerformanceId);

                if (measures.Any())
                {
                    if (measures.Any(s => s.AvgFemaleBirdWeight > 0))
                        report.AvgFemaleBirdWeight = measures.Where(s => s.AvgFemaleBirdWeight > 0).Average(s => s.AvgFemaleBirdWeight);
                    else
                        report.AvgFemaleBirdWeight = 0;

                    if (measures.Any(s => s.AvgMaleBirdWeight > 0))
                        report.AvgMaleBirdWeight = measures.Where(s => s.AvgMaleBirdWeight > 0).Average(s => s.AvgMaleBirdWeight);
                    else
                        report.AvgMaleBirdWeight = 0;
                    // Set sample cage measurements
                    foreach (SampleCageMeasurementViewModel cage in measures)
                    {
                        report.SampleCageMeasurement.Add(new SampleCageMeasurement()
                        {
                            Id = new Guid(cage.Id),
                            AvgFemaleBirdWeight = cage.AvgFemaleBirdWeight,
                            AvgMaleBirdWeight = cage.AvgMaleBirdWeight,
                            SampleCageId = new Guid(cage.SampleCageId),
                            SampleCageReportId = report.Id,
                            Name = cage.Name,
                            FemaleBirdWeightCapacityUnitId = string.IsNullOrEmpty(cage.FemaleBirdWeightMeasureId) ? CapacityUnits.Kilograms : new Guid(cage.FemaleBirdWeightMeasureId),
                            MaleBirdWeightCapacityUnitId = string.IsNullOrEmpty(cage.MaleBirdWeightMeasureId) ? CapacityUnits.Kilograms : new Guid(cage.MaleBirdWeightMeasureId),
                            VariationCoefficientFemale = cage.VariationCoefficientFemale,
                            VariationCoefficientMale = cage.VariationCoefficientMale,
                            UniformityFemale = cage.UniformityFemale,
                            UniformityMale = cage.UniformityMale
                        });
                    }
                }
                // Aviario values not considered
                IEnumerable<SampleCageMeasurementViewModel> measurementsWithBatch = this.SampleCageMeasurements.Where(s => !string.IsNullOrEmpty(s.HenBatchId));

                if (measurementsWithBatch.Any(s => s.VariationCoefficientFemale > 0))
                    report.AvgVariationCoefficientFemale = measurementsWithBatch.Where(s => s.VariationCoefficientFemale > 0).Average(s => s.VariationCoefficientFemale.Value);
                else
                    report.AvgVariationCoefficientFemale = 0;

                if (measurementsWithBatch.Any(s => s.VariationCoefficientMale > 0))
                    report.AvgVariationCoefficientMale = measurementsWithBatch.Where(s => s.VariationCoefficientMale > 0).Average(s => s.VariationCoefficientMale.Value);
                else
                    report.AvgVariationCoefficientMale = 0;

                if (measurementsWithBatch.Any(s => s.UniformityFemale > 0))
                    report.AvgUniformityFemale = measurementsWithBatch.Where(s => s.UniformityFemale > 0).Average(s => s.UniformityFemale.Value);
                else
                    report.AvgUniformityFemale = 0;

                if (measurementsWithBatch.Any(s => s.UniformityMale > 0))
                    report.AvgUniformityMale = measurementsWithBatch.Where(s => s.UniformityMale > 0).Average(s => s.UniformityMale.Value);
                else
                    report.AvgUniformityMale = 0;

                sampleCageReports.Add(report);
            }
            return sampleCageReports;
        }

        public SampleCageReport ToEntityFromWarehouse()
        {
            SampleCageReport report = new SampleCageReport()
            {
                Date = DateTime.Parse(this.Date),
                HenWarehouseAvgFemaleBirdWeight = HenWarehouseAvgFemaleBirdWeight,
                HenWarehouseAvgMaleBirdWeight = HenWarehouseAvgMaleBirdWeight,
                HenWarehouseUniformityFemale = HenWarehouseUniformityFemale,
                HenWarehouseUniformityMale = HenWarehouseUniformityMale,
                HenWarehouseVariationCoefficientFemale = HenWarehouseVariationCoeficentFemale,
                HenWarehouseVariationCoefficientMale = HenWarehouseVariationCoeficentMale,
                HenWarehouseFemaleBirdWeightCapacityUnitId = HenWarehouseFemaleBirdWeightMeasureId != null ? Guid.Parse(HenWarehouseFemaleBirdWeightMeasureId) : new Guid(),
                HenWarehouseMaleBirdWeightCapacityUnitId = HenWarehouseMaleBirdWeightMeasureId != null ? Guid.Parse(HenWarehouseMaleBirdWeightMeasureId) : new Guid(),
                HenBatchId = Guid.Parse(HenBatchId)
            };
            return report;
        }
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            IStringLocalizer<SharedResources> localizer = (IStringLocalizer<SharedResources>)validationContext.GetService(typeof(IStringLocalizer<SharedResources>));
            List<ValidationResult> errors = new List<ValidationResult>();

            if (SampleCageMeasurements.Any() && SampleCageMeasurements.Where(scm => !scm.WarehouseMeasures).All(scm => scm.IsExcluded))
                errors.Add(new ValidationResult(localizer[Lang.SampleCageRequired], new string[] { "ValidationErrors" }));

            foreach (SampleCageMeasurementViewModel cages in SampleCageMeasurements)
            {
                if (!cages.IsExcluded)
                {
                    int i = SampleCageMeasurements.IndexOf(cages);
                    if (cages.HasFemaleHen && cages.AvgFemaleBirdWeight > 0)
                    {
                        if (!cages.VariationCoefficientFemale.HasValue)
                            errors.Add(new ValidationResult(localizer[Lang.Required], new string[] { $"SampleCageMeasurements[{i}].VariationCoefficientFemale" }));

                        if (!cages.UniformityFemale.HasValue)
                            errors.Add(new ValidationResult(localizer[Lang.Required], new string[] { $"SampleCageMeasurements[{i}].UniformityFemale" }));
                    }

                    if (cages.HasMaleHen && cages.AvgMaleBirdWeight > 0)
                    {
                        if (!cages.VariationCoefficientMale.HasValue)
                            errors.Add(new ValidationResult(localizer[Lang.Required], new string[] { $"SampleCageMeasurements[{i}].VariationCoefficientMale" }));

                        if (!cages.UniformityMale.HasValue)
                            errors.Add(new ValidationResult(localizer[Lang.Required], new string[] { $"SampleCageMeasurements[{i}].UniformityMale" }));
                    }

                    if (cages.AvgFemaleBirdWeight <= 0 && cages.AvgMaleBirdWeight <= 0)
                        errors.Add(new ValidationResult(localizer[Lang.WeightRequired], new string[] { $"SampleCageMeasurements[{i}].ValidationErrors" }));
                }

            }
            return errors;
        }
    }
}
