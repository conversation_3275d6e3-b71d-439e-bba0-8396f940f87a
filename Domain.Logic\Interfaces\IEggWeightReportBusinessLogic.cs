using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Domain.Logic.Interfaces
{
    public interface IEggWeightReportBusinessLogic
    {
        /// <summary>
        /// creates warehouse reports and add them to the hen batch performance
        /// </summary>
        Task CreateReports(List<EggWeightReport> reports);

        /// <summary>
        /// Returns all egg weight report includes some  relationships.
        /// </summary>
        IQueryable<EggWeightReportDTO> GetAll();

        /// <summary>
        /// Creates an egg weight report from a sample cage report
        /// </summary>
        Task CreateEggWeightReportFromSampleCageReport(Guid henBatchId, DateTime date, decimal eggWeight, Guid? sampleCageId = null);
    }
}