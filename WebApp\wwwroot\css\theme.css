/* Variables */
:root {
  --primary-color-dark: #29313e;
  --primary-color: #29313e;
  --primary-color-light: #ffffff;
  --secondary-color: #cff300;
  --secondary-color-dark: #c2e400;
  --primary-color-lightest: #b4a3e4;
  --warning-color: #ff5c6c;
  --success-color: #24d2b5;
  --sidebar-color: #242a33;
  --login-bg-color: #678dc9;
  --sidebar-secondary-color: #cff300;
}

/* Topbar styles */

.topbar {
  background: var(--primary-color-light);
}

.topbar .navbar-header {
  background-color: var(--primary-color-light);
}

.topbar ul.dropdown-user li a:hover {
  color: var(--primary-color);
}

/* Sidebar styles */

.left-sidebar,
.mini-sidebar .sidebar-nav .sidebarnav > li:hover > a,
.mini-sidebar .sidebar-nav .sidebarnav > li > ul {
  background-color: var(--sidebar-color);
}

.sidebar-nav {
  background-color: var(--sidebar-color);
}

.sidebar-nav > ul > li > a,
.sidebar-nav > ul > li > a i {
  color: white !important;
}

.sidebar-nav > ul > li > a.active,
.sidebar-nav > ul > li > a:hover {
  background-color: #1c1c1c;
}

.sidebar-nav .has-arrow::after {
  border-color: white;
}

.sidebar-nav > ul > li.active > a {
  border-color: var(--sidebar-secondary-color);
}

.sidebarnav li a {
  color: white;
}

/* Forms styles */
.select2-container--default
  .select2-results__option--highlighted[aria-selected],
.floating-labels .bar::before,
.floating-labels .bar::after {
  background: var(--primary-color);
}

.form-control:focus {
  box-shadow: 0 0 0 0.2rem rgba(61, 0, 229, 0.25);
}

/* Login styles */

#login {
  background-image: url("/images/bg-login-register.png");
  background-size: auto;
}

/* ------------- Success Styles ------------- */
.text-success,
.label-light-success {
  color: var(--success-color) !important;
}

.bg-success,
.round.round-success,
.label-success,
.badge-success,
.jq-icon-success,
.pagination-circle li.active a,
.pagination-circle li a:hover,
.custom-control-input:checked ~ .custom-control-indicator,
.color-table.success-table thead th,
.color-bordered-table.success-bordered-table thead th,
.full-color-table.full-success-table thead th,
.full-color-table.full-success-table tr:hover {
  background-color: var(--success-color) !important;
}

.btn-success.disabled,
.btn-success.disabled:hover {
  background: var(--success-color);
  border: 1px solid var(--success-color);
}

.btn-outline-success {
  color: var(--success-color);
  border-color: var(--success-color);
}

.btn-outline-success:hover,
.btn-outline-success:focus {
  background: var(--success-color);
  border-color: var(--success-color);
}

.validate .form-control {
  border-color: var(--success-color);
}

.color-bordered-table.success-bordered-table {
  border: 2px solid var(--success-color);
}

/* ------------- Danger Styles ------------- */
.text-danger,
.label-light-danger {
  color: var(--warning-color) !important;
}

.bg-danger,
.round.round-danger,
.label-danger,
.badge-danger,
.notify .point,
.jq-icon-error,
.color-table.danger-table thead th,
.color-bordered-table.danger-bordered-table thead th,
.full-color-table.full-danger-table thead th,
.full-color-table.full-danger-table tr:hover {
  background-color: var(--warning-color) !important;
}

.notify .heartbit {
  border: 5px solid var(--warning-color);
}

.btn-danger.disabled,
.btn-danger.disabled:hover {
  background: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.btn-outline-danger {
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-outline-danger:hover,
.btn-outline-danger:focus {
  background: var(--warning-color);
  border-color: var(--warning-color);
}

.error .form-control {
  border-color: var(--warning-color);
}

.color-bordered-table.danger-bordered-table {
  border: 2px solid var(--warning-color);
}

/* ------------- Primary Styles ------------- */
.btn-themecolor,
.btn-themecolor.disabled {
  background: var(--secondary-color);
  color: #29313e;
  border: 1px solid var(--secondary-color);
}

.btn-themecolor:hover,
.btn-themecolor.disabled:hover {
  background: var(--secondary-color);
  opacity: 0.7;
  border: 1px solid var(--secondary-color);
}

.btn-themecolor.active,
.btn-themecolor:focus,
.btn-themecolor.disabled:active,
.btn-themecolor.disabled:focus {
  background: var(--secondary-color-dark);
}

.text-themecolor {
  color: var(--primary-color-dark) !important;
  font-weight: 600;
}

html body .text-primary,
.label-light-primary {
  color: var(--primary-color) !important;
}

.bg-primary,
.round.round-primary,
.twitter-typeahead .tt-menu .tt-suggestion:hover,
.label-primary,
.badge-primary,
.paging_simple_numbers .pagination .paginate_button.active a,
.paging_simple_numbers .pagination .paginate_button:hover a,
.color-table.primary-table thead th,
.color-bordered-table.primary-bordered-table thead th,
.full-color-table.full-primary-table thead th,
.full-color-table.full-primary-table tr:hover {
  background-color: var(--primary-color) !important;
}

.btn-primary.disabled,
.btn-primary.disabled:hover {
  background: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover,
.btn-outline-primary.focus,
.select2-container--default
  .select2-selection--multiple
  .select2-selection__choice {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.color-bordered-table.primary-bordered-table {
  border: 2px solid var(--primary-color);
}

.dx-datagrid
  .dx-datagrid-content
  .dx-datagrid-table
  .dx-row
  .dx-command-edit-with-icons
  .dx-link {
  padding: 5px;
  margin: auto;
  width: 40px;
  height: auto;
  color: white;
}

.dx-datagrid
  .dx-datagrid-content
  .dx-datagrid-table
  .dx-row
  .dx-command-edit-with-icons
  .dx-link.themecolor {
  color: #29313e;
}

.dx-treelist
  .dx-treelist-content
  .dx-treelist-table
  .dx-row
  .dx-command-edit-with-icons
  .dx-link {
  padding: 5px;
  margin: auto;
  width: 40px;
  height: auto;
  color: white;
}

.dx-treelist
  .dx-treelist-content
  .dx-treelist-table
  .dx-row
  .dx-command-edit-with-icons
  .dx-link.themecolor {
  color: #ffffff;
}

tr.dx-row.dx-data-row td.flex-column-button-cell {
  display: grid;
  grid-template-columns: 40px 1px 40px 1px 40px 1px;
  grid-gap: 1px;
}
