using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Extensions;
using Binit.Framework.Helpers;
using Binit.Framework.Helpers.Configuration;
using Binit.Framework.Helpers.Email;
using Binit.Framework.Helpers.Jwt;
using Binit.Framework.Interfaces.Cache;
using Binit.Framework.Interfaces.Configuration;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.Email;
using Binit.Framework.JobScheduler;
using Binit.Framework.OperationContext;
using Domain.Entities.Model;
using Domain.Logic.BatchProcessFactory;
using Domain.Logic.BatchProcessFactory.BatchEntities;
using Domain.Logic.BusinessLogic;
using Domain.Logic.BusinessLogic.BatchBusinessLogic;
using Domain.Logic.BusinessLogic.ChartsBusinessLogic;
using Domain.Logic.BusinessLogic.ContainerBusinessLogic;
using Domain.Logic.BusinessLogic.DispositionReportBusinessLogic;
using Domain.Logic.BusinessLogic.HenReportBusinessLogic;
using Domain.Logic.BusinessLogic.InconsistencyReportBusinessLogic;
using Domain.Logic.BusinessLogic.MaterialDistributionReportBusinessLogic;
using Domain.Logic.BusinessLogic.MovementReportBusinessLogic;
using Domain.Logic.BusinessLogic.PackingReportBusinessLogic;
using Domain.Logic.BusinessLogic.PlannerBusinessLogic;
using Domain.Logic.BusinessLogic.UpdateHenBatchPerformanceBusinessLogic;
using Domain.Logic.BusinessLogic.WorkerBusinessLogic;
using Domain.Logic.ExternalBusinessLogics.ERP.Material;
using Domain.Logic.ExternalBusinessLogics.ERP.Persons;
using Domain.Logic.ExternalServices.Academy;
using Domain.Logic.ExternalServices.Academy.Account;
using Domain.Logic.ExternalServices.ERP;
using Domain.Logic.ExternalServices.ERP.Account;
using Domain.Logic.ExternalServices.FileManager;
using Domain.Logic.Interfaces;
using Domain.Logic.Interfaces.PlannerBusinessLogic;
using Domain.Logic.JobScheduler;
using Domain.Logic.JobScheduler.Jobs;
using Domain.Logic.Services;
using Microsoft.ApplicationInsights.DependencyCollector;
using Microsoft.AspNetCore.Authentication.OAuth;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using WebApp.ActionFilters;
using WebApp.Middleware;

namespace WebApp
{
    /// <summary>
    /// WebApp's service injection.
    /// </summary>
    public class ServiceInjection : AbstractServiceInjection
    {
        public ServiceInjection(IServiceCollection services, IConfiguration configuration)
            : base(services, configuration)
        {
        }

        public override IServiceCollection Initialize()
        {
            Services.AddHttpClient();
            Services.AddApplicationInsightsTelemetry();
            Services.ConfigureTelemetryModule<DependencyTrackingTelemetryModule>((module, o) => { module.EnableSqlCommandTextInstrumentation = true; });
            Services.AddScoped<RazorViewRender>();
            Services.AddScoped<ExceptionHandlerMiddleware>();
            Services.AddScoped<JWTHelper>();
            Services.AddScoped<IOperationContext, WebOperationContext>();
            Services.AddTransient<ERPRefreshTokenDelegatingHandler>();
            Services.AddHttpClient("erpExternalService")
                    .AddHttpMessageHandler<ERPRefreshTokenDelegatingHandler>();
            this.Services.AddTransient<AcademyRefreshTokenDelegatingHandler>();
            this.Services.AddHttpClient("igniteExternalService")
                    .AddHttpMessageHandler<AcademyRefreshTokenDelegatingHandler>();
            AddServices();
            AddBatchEntityProcessorStrategies();
            AddBusinessLogics();
            AddConfigurations();
            AddJobs();
            AddEmailService();
            AddAuthentication();
            AddExternalProviders();
            Services.AddScoped<ContainerPropertiesActionFilter>();

            return Services;
        }

        private void AddServices()
        {
            Services.AddScoped<IAccountService, AccountService>();
            Services.AddScoped<IAnalyticsService, AnalyticsService>();
            Services.AddScoped<IAuditReportService, AuditReportService>();
            Services.AddScoped<IBestPracticeService, BestPracticeService>();
            Services.AddScoped<ICapacityUnitService, CapacityUnitService>();
            Services.AddScoped<ICasualtyReasonService, CasualtyReasonService>();
            Services.AddScoped<ICreateHenReports, CreateHenReports>();
            Services.AddScoped<ICertificationService, CertificationService>();
            Services.AddScoped<IClassificationReportService, ClassificationReportService>();
            Services.AddScoped<IClassificationWarehouseService, ClassificationWarehouseService>();
            Services.AddScoped<IClusterService, ClusterService>();
            Services.AddScoped<ICompositionService, CompositionService>();
            Services.AddScoped<IContainerService<Container>, ContainerService<Container>>();
            Services.AddScoped(typeof(IContainerService<>), typeof(ContainerService<>));
            Services.AddScoped<ICompanyService, CompanyService>();
            Services.AddScoped<IDepopulationReasonService, DepopulationReasonService>();
            Services.AddScoped<IDispositionWarehouseService, DispositionWarehouseService>();
            Services.AddScoped<IDispositionReportService, DispositionReportService>();
            Services.AddScoped<IEggDensityReportService, EggDensityReportService>();
            Services.AddScoped<IEggQualityReportService, EggQualityReportService>();
            Services.AddScoped<IEggStockService, EggStockService>();
            Services.AddScoped<IEggWeightReportService, EggWeightReportService>();
            Services.AddScoped<IFarmService, FarmService>();
            Services.AddScoped<IFeedFactoryService, FeedFactoryService>();
            Services.AddScoped<IFertilityReportService, FertilityReportService>();
            Services.AddScoped<IFeedProductionReportService, FeedProductionReportService>();
            Services.AddScoped<IFertilityReportService, FertilityReportService>();
            Services.AddScoped<IFileManagerService, FileManagerService>();
            Services.AddScoped<IFolderService, FolderService>();
            Services.AddScoped<IFormulaService, FormulaService>();
            Services.AddScoped<IGeneralFileService, GeneralFileService>();
            Services.AddScoped<IGeneticBusinessLogic, GeneticBusinessLogic>();
            Services.AddScoped<IGeneticsParameterService, GeneticsParametersService>();
            Services.AddScoped<IGeneticService, GeneticService>();
            Services.AddScoped<IHappeningService, HappeningService>();
            Services.AddScoped<IHatcheringReportService, HatcheringReportService>();
            Services.AddScoped<IHealthCareReportService, HealthCareReportService>();
            Services.AddScoped<IHenBatchService, HenBatchService>();
            Services.AddScoped<IHenBatchCategoryService, HenBatchCategoryService>();
            Services.AddScoped<IHenPhaseService, HenPhaseService>();
            Services.AddScoped<IHenReportService, HenReportService>();
            Services.AddScoped<IHenWarehouseService, HenWarehouseService>();
            Services.AddScoped<IHolidayService, HolidayService>();
            Services.AddScoped<IInconsistencyReportService, InconsistencyReportService>();
            Services.AddScoped<IIndicatorsBusinessLogic, IndicatorsBusinessLogic>();
            Services.AddScoped<ILineService, LineService>();
            Services.AddScoped<ILoadingOrderService, LoadingOrderService>();
            Services.AddScoped<IMaterialAnalysisReportService, MaterialAnalysisReportService>();
            Services.AddScoped<IMaterialReceptionReportBusinessLogic, MaterialReceptionReportBusinessLogic>();
            Services.AddScoped<IMaterialReceptionReportService, MaterialReceptionReportService>();
            Services.AddScoped<IMaterialExitReportBusinessLogic, MaterialExitReportBusinessLogic>();
            Services.AddScoped<IMaterialExitReportService, MaterialExitReportService>();
            Services.AddScoped<IMaterialDistributionReportBusinessLogic, MaterialDistributionReportBusinessLogic>();
            Services.AddScoped<IMaterialDistributionReportService, MaterialDistributionReportService>();
            Services.AddScoped<IMaterialService, MaterialService>();
            Services.AddScoped<IMaterialTypeService, MaterialTypeService>();
            Services.AddScoped<IMessageService, MessageService>();
            Services.AddScoped<IMovementReportService, MovementReportService>();
            Services.AddScoped<INewsService, NewsService>();
            Services.AddScoped<INotificationService, NotificationService>();
            Services.AddScoped<INotificationTypeService, NotificationTypeService>();
            Services.AddScoped<IOrderPreparationReportService, OrderPreparationReportService>();
            Services.AddScoped<IPackingWarehouseService, PackingWarehouseService>();
            Services.AddScoped<IPackingReportService, PackingReportService>();
            Services.AddScoped<IPersonService, PersonService>();
            Services.AddScoped<IPreparationWarehouseService, PreparationWarehouseService>();
            Services.AddScoped<IReportRectificationBusinessLogic, ReportRectificationBusinessLogic>();
            Services.AddScoped<ISalesOrderService, SalesOrderService>();
            Services.AddScoped<ISampleCageReportService, SampleCageReportService>();
            Services.AddScoped<ISectorService, SectorService>();
            Services.AddScoped<ISecurityProfileService, SecurityProfileService>();
            Services.AddScoped<ISerologyAnalysisReportService, SerologyAnalysisReportService>();
            Services.AddScoped<ISerologyAnalysisResultService, SerologyAnalysisResultService>();
            Services.AddScoped<IServiceTenantDependent<HappeningType>, HappeningTypeService>();
            Services.AddScoped<ISpreadSheetExportService, SpreadSheetExportService>();
            Services.AddScoped<IShippingNoteConciliationService, ShippingNoteConciliationService>();
            Services.AddScoped<IShippingNoteService, ShippingNoteService>();
            Services.AddScoped<ISiloService, SiloService>();
            Services.AddScoped<ISlaughterhouseService, SlaughterhouseService>();
            Services.AddScoped<ISKUService, SKUService>();
            Services.AddScoped<IStorageWarehouseService, StorageWarehouseService>();
            Services.AddScoped<ITaskEntityService, TaskEntityService>();
            Services.AddScoped<ITemplateBestPracticeService, TemplateBestPracticeService>();
            Services.AddScoped<ITenantService, TenantService>();
            Services.AddScoped(typeof(IUserService<>), typeof(UserService<>));
            Services.AddScoped<IVehicleService, VehicleService>();
            Services.AddScoped<IVehicleTypeService, VehicleTypeService>();
            Services.AddScoped<IReturnReportService, ReturnReportService>();
            Services.AddScoped<IFoodProductionOrderService, FoodProductionOrderService>();
            Services.AddScoped<ISpikingReceptionReportService, SpikingReceptionReportService>();
            Services.AddScoped<IReportPlannerProgramService, ReportPlannerProgramService>();
            Services.AddScoped<IRegionalService, RegionalService>();

            #region ExternalServices
            Services.AddScoped<IERPAPIAccountService, ERPAPIAccountService>();
            Services.AddScoped<IERPOperationContext, ERPOperationContext>();
            Services.AddSingleton<IContextStorageMemoryCache, ContextStorageMemoryCache>();
            Services.AddScoped<IAcademyAPIAccountService, AcademyAPIAccountService>();
            Services.AddScoped<IAcademyOperationContext, AcademyOperationContext>();
            #endregion
        }

        private void AddBusinessLogics()
        {
            Services.AddScoped<IAlertEmailBusinessLogic, AlertEmailBusinessLogic>();
            Services.AddScoped<IBestPracticeBusinessLogic, BestPracticeBusinessLogic>();
            Services.AddScoped<IHenBatchesBestPracticeBusinessLogic, HenBatchesBestPracticeBusinessLogic>();
            Services.AddScoped<IBirdsTrackingChartsBusinessLogic, BirdsTrackingChartsBusinessLogic>();
            Services.AddScoped<ISearaChartsBusinessLogic, SearaChartsBusinessLogic>();
            Services.AddScoped<ISearaBreedingChartsBusinessLogic, SearaBreedingChartsBusinessLogic>();
            Services.AddScoped<ISearaLayingChartsBusinessLogic, SearaLayingChartsBusinessLogic>();
            Services.AddScoped<ICapacityUnitBusinessLogic, CapacityUnitBusinessLogic>();
            Services.AddScoped<IClaimBusinessLogic, ClaimBusinessLogic>();
            Services.AddScoped<IClassificationReportBusinessLogic, ClassificationReportBusinessLogic>();
            Services.AddScoped<ICompanyBusinessLogic, CompanyBusinessLogic>();
            Services.AddScoped<ICompositionBusinessLogic, CompositionBusinessLogic>();
            Services.AddScoped<IDispatchOrderBusinessLogic, DispatchOrderBusinessLogic>();
            Services.AddScoped<IDispositionReportBusinessLogic, DispositionReportBusinessLogic>();
            Services.AddScoped<IEggsTrackingChartsBusinessLogic, EggsTrackingChartsBusinessLogic>();
            Services.AddScoped<IEventBusinessLogic, EventBusinessLogic>();
            Services.AddScoped<IFarmBusinessLogic, FarmBusinessLogic>();
            Services.AddScoped<IFarmStructureBusinessLogic, FarmStructureBusinessLogic>();
            Services.AddScoped<IFeedProductionReportBusinessLogic, FeedProductionReportBusinessLogic>();
            Services.AddScoped<IFertilityReportBusinessLogic, FertilityReportBusinessLogic>();
            Services.AddScoped<IFoodTrackingChartsBusinessLogic, FoodTrackingChartsBusinessLogic>();
            Services.AddScoped<IFormulaBusinessLogic, FormulaBusinessLogic>();
            Services.AddScoped<IGeneticReportBusinessLogic, GeneticReportBusinessLogic>();
            Services.AddScoped<IEggProductionByCategoryBusinessLogic, EggProductionByCategoryBusinessLogic>();
            Services.AddScoped<IWeightUniformityReportBusinessLogic, WeightUniformityReportBusinessLogic>();
            Services.AddScoped<IManagerialLayingReportBusinessLogic, ManagerialLayingReportBusinessLogic>();
            Services.AddScoped<IHappeningBusinessLogic, HappeningBusinessLogic>();
            Services.AddScoped<IHatcheringReportBusinessLogic, HatcheringReportBusinessLogic>();
            Services.AddScoped<IHenBatchBusinessLogic, HenBatchBusinessLogic>();
            Services.AddScoped<IHenBatchPerformanceService, HenBatchPerformanceService>();
            Services.AddScoped<IHenBatchPerformanceBusinessLogic, HenBatchPerformanceBusinessLogic>();
            Services.AddScoped<IHenReportBusinessLogic, HenReportBusinessLogic>();
            Services.AddScoped<IHenWarehouseBusinessLogic, HenWarehouseBusinessLogic>();
            Services.AddScoped<IHenBatcheJobScheduleBusinessLogic, HenBatcheJobScheduleBusinessLogic>();
            Services.AddScoped<IIgniteAddressService, IgniteAddressService>();
            Services.AddScoped<IInconsistencyReportBusinessLogic, InconsistencyReportBusinessLogic>();
            Services.AddScoped<IMaterialAnalysisReportBusinessLogic, MaterialAnalysisReportBusinessLogic>();
            Services.AddScoped<IMaterialBusinessLogic, MaterialBusinessLogic>();
            Services.AddScoped<IMessageBusinessLogic, MessageBusinessLogic>();
            Services.AddScoped<IMortalityByGenderBusinessLogic, MortalityByGenderBusinessLogic>();
            Services.AddScoped<INewsBusinessLogic, NewsBusinessLogic>();
            Services.AddScoped<INotificationBusinessLogic, NotificationBusinessLogic>();
            Services.AddScoped<IOperationStatisticsBusinessLogic, OperationStatisticsBusinessLogic>();
            Services.AddScoped<IPackingReportBusinessLogic, PackingReportBusinessLogic>();
            Services.AddScoped<IPersonBusinessLogic, PersonBusinessLogic>();
            Services.AddScoped<IReturnReasonBusinessLogic, ReturnReasonBusinessLogic>();
            Services.AddScoped<IReturnReportBusinessLogic, ReturnReportBusinessLogic>();
            Services.AddScoped<ISalesOrderManagementBusinessLogic, SalesOrderManagementBusinessLogic>();
            Services.AddScoped<ISalesOrderBusinessLogic, SalesOrderBusinessLogic>();
            Services.AddScoped<ISecurityProfileBusinessLogic, SecurityProfileBusinessLogic>();
            Services.AddScoped<IShippingNoteBusinessLogic, ShippingNoteBusinessLogic>();
            Services.AddScoped<ISampleCageReportBusinessLogic, SampleCageReportBusinessLogic>();
            Services.AddScoped<ISectorBusinessLogic, SectorBusinessLogic>();
            Services.AddScoped<ISerologyAnalysisReportBusinessLogic, SerologyAnalysisReportBusinessLogic>();
            Services.AddScoped<ISerologyBusinessLogic, SerologyBusinessLogic>();
            Services.AddScoped<ISKUBusinessLogic, SKUBusinessLogic>();
            Services.AddScoped<ISKUCompositionBusinessLogic, SKUCompositionBusinessLogic>();
            Services.AddScoped<IStatisticsBusinessLogic, StatisticsBusinessLogic>();
            Services.AddScoped<ITrackingChartsBusinessLogic, TrackingChartsBusinessLogic>();
            Services.AddScoped<ITreeIndexBusinessLogic, TreeIndexBusinessLogic>();
            Services.AddScoped<IHealthCareReportBusinessLogic, HealthCareReportBusinessLogic>();
            Services.AddScoped<ITemplateBestPracticeBusinessLogic, TemplateBestPracticeBusinessLogic>();
            Services.AddScoped<IMovementReportBusinessLogic, MovementReportBusinessLogic>();
            Services.AddScoped<IExcelExportBusinessLogic, ExcelExportBusinessLogic>();
            Services.AddScoped<IExternalServiceService, ExternalServiceService>();
            Services.AddScoped<ICommonChartsBusinessLogic, CommonChartsBusinessLogic>();
            Services.AddScoped<IInventoryBusinessLogic, InventoryBusinessLogic>();
            Services.AddScoped<IHenBatchChartsBusinessLogic, HenBatchChartsBusinessLogic>();
            Services.AddScoped<IHenStageChartsBusinessLogic, HenStageChartsBusinessLogic>();
            Services.AddScoped<ILayingChartsBusinessLogic, LayingChartsBusinessLogic>();
            Services.AddScoped<IStockChartsBusinessLogic, StockChartsBusinessLogic>();
            Services.AddScoped<IFeedFactoryChartsBusinessLogic, FeedFactoryChartsBusinessLogic>();
            Services.AddScoped<IClassificationChartsBusinessLogic, ClassificationChartsBusinessLogic>();
            Services.AddScoped<IPackingChartsBusinessLogic, PackingChartsBusinessLogic>();
            Services.AddScoped<IReportBusinessLogic, ReportBusinessLogic>();
            Services.AddScoped<IReportRectificationService, ReportRectificationService>();
            Services.AddScoped<IReturnAndDispositionChartsBusinessLogic, ReturnAndDispositionChartsBusinessLogic>();
            Services.AddScoped<IUpdateHenBatchPerformanceBusinessLogic, UpdateHenBatchPerformanceBusinessLogic>();
            Services.AddScoped<IUpdateHenBatchPerformanceDateBusinessLogic, UpdateHenBatchPerformanceDateBusinessLogic>();
            Services.AddScoped<ITaskEmailNotificationBusinessLogic, TaskEmailNotificationBusinessLogic>();
            Services.AddScoped<ITenantBusinessLogic, TenantBusinessLogic>();
            Services.AddScoped<IVehicleBusinessLogic, VehicleBusinessLogic>();
            Services.AddScoped<IFoodProductionOrderBusinessLogic, FoodProductionOrderBusinessLogic>();
            Services.AddTransient<IContainerBusinessLogic, ContainerBusinessLogic>();
            Services.AddScoped<IProcessesBusinessLogic, ProcessesBusinessLogic>();
            Services.AddScoped<IStorageWarehouseBusinessLogic, StorageWarehouseBusinessLogic>();
            Services.AddScoped<IWarningBusinessLogic, WarningBusinessLogic>();
            Services.AddScoped<IEggWeightReportBusinessLogic, EggWeightReportBusinessLogic>();
            Services.AddScoped<IIgniteAddressService, IgniteAddressService>();
            Services.AddScoped<IUpdateHenBatchPerformanceBusinessLogic, UpdateHenBatchPerformanceBusinessLogic>();
            Services.AddScoped<IGADPlannerBusinessLogic, GADPlannerBusinessLogic>();

            #region ExternalBusinessLogic
            Services.AddScoped<IERPAPIPersonBusinessLogic, ERPAPIPersonBusinessLogic>();
            Services.AddScoped<IERPAPIMaterialBusinessLogic, ERPAPIMaterialBusinessLogic>();
            #endregion
        }

        private void AddConfigurations()
        {
            Services.AddScoped<IGeneralConfiguration, GeneralConfiguration>();
            Services.AddScoped<IRealmConfiguration, RealmConfiguration>();
            Services.AddScoped<IGoogleMapsConfiguration, GoogleMapsConfiguration>();
            Services.AddScoped<IGoogleAnalyticsConfiguration, GoogleAnalyticsConfiguration>();
            Services.AddScoped<ISolutionConfiguration, SolutionConfiguration>();
        }

        private void AddJobs()
        {
            Services.AddTransient<JobListenerBase, JobListener>();
            Services.AddTransient<ProcessHenReportJob>();
            Services.AddTransient<TaskEmailNotificationJob>();
            Services.AddTransient<AlertEmailForThresholdsExceededJob>();
            Services.AddTransient<UpdateHenBatchPerformanceJob>();
            Services.AddTransient<UpdateHenBatchPerformanceDatesJob>();
            Services.AddTransient<AutomaticallyAssignBestPracticesJob>();
            Services.AddTransient<UpdateHenWarehouseJob>();
            Services.AddTransient<UpdateReportRectificationJob>();
            Services.AddTransient<BatchStoredHenbatchPerformanceJob>();

            Services.AddTransient<ExportJob>();
            Services.AddTransient<UpdateContainersJob>();
        }

        private void AddBatchEntityProcessorStrategies()
        {
            Services.AddScoped<IBatchProcessService, BatchProcessService>();

            Services.AddScoped<HatcheringReportProcessor>();
            Services.AddScoped<FertilityReportProcessor>();

            Services.AddScoped<Func<string, IBatchEntityProcessor>>(serviceProvider => key => key switch
            {
                nameof(HatcheringReport) => serviceProvider.GetService<HatcheringReportProcessor>(),
                nameof(FertilityReport) => serviceProvider.GetService<FertilityReportProcessor>(),
                _ => throw new ArgumentException("no existe una estrategia para la entidad")
            });

            Services.AddScoped<IBatchProcessHenbatchPerformanceBusinessLogic, BatchProcessHenbatchPerformanceBusinessLogic>();
        }

        private void AddEmailService()
        {
            //TODO:  
            //Use AppSettings / User Secret

            Services.AddTransient<IEmailSender, EmailSender>();

            SmtpConfiguration smtpConfig = new SmtpConfiguration().Bind(Configuration);

            //TODO: If not configured there must be an exception telling so
            Services.AddFluentEmail(smtpConfig.Address)
                .AddRazorRenderer()
                .AddSmtpSender(smtpConfig.GenerateSmtpClient());
        }

        private void AddAuthentication()
        {
            Services.ConfigureApplicationCookie(config =>
            {
                config.LoginPath = "/Identity/Account/Login";
                config.AccessDeniedPath = "/Identity/Account/AccessDenied";
            });
        }

        private void AddExternalProviders()
        {
            Services.AddAuthentication()
                .AddGoogle(googleOptions =>
                {
                    OAuthOptions options = Configuration.GetAuthenticationSection<OAuthOptions>(SocialLoginConstants.Google);
                    googleOptions.ClientId = options.ClientId;
                    googleOptions.ClientSecret = options.ClientSecret;
                })
                .AddFacebook(facebookOptions =>
                {
                    OAuthOptions options = Configuration.GetAuthenticationSection<OAuthOptions>(SocialLoginConstants.Facebook);
                    facebookOptions.ClientId = options.ClientId;
                    facebookOptions.ClientSecret = options.ClientSecret;
                })
                .AddTwitter(twitterOptions =>
                {
                    OAuthOptions options = Configuration.GetAuthenticationSection<OAuthOptions>(SocialLoginConstants.Twitter);
                    twitterOptions.ConsumerKey = options.ClientId;
                    twitterOptions.ConsumerSecret = options.ClientSecret;
                });
        }
    }
}