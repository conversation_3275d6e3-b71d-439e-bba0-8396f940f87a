﻿using Binit.Framework;
using Binit.Framework.Helpers.Excel;
using Binit.Framework.Interfaces.Excel;
using Domain.Entities.Constants.ReportPlanner;
using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs.GAD.Planner;
using Domain.Logic.Helpers;
using Domain.Logic.Interfaces;
using Domain.Logic.Interfaces.PlannerBusinessLogic;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

using ExportFemale = Domain.Logic.BusinessLogic.DTOs.GAD.Planner.Female.ExcelExportDTO;
using ExportMale = Domain.Logic.BusinessLogic.DTOs.GAD.Planner.Male.ExcelExportDTO;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.PlannerBusinessLogic;

namespace Domain.Logic.BusinessLogic.PlannerBusinessLogic
{
    /// <inheritdoc/>
    public class GADPlannerBusinessLogic : IGADPlannerBusinessLogic
    {
        private readonly IExcelExportBusinessLogic excelExportBusinessLogic;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IHenBatchService henBatchService;
        private readonly IContainerService<Container> containerService;

        private readonly IReportPlannerProgramService reportPlannerProgramService;
        private readonly IStringLocalizer<SharedResources> localizer;

        public GADPlannerBusinessLogic(
            IExcelExportBusinessLogic excelExportBusinessLogic,
            IGeneticsParameterService geneticsParameterService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IHenBatchService henBatchService,
            IContainerService<Container> containerService,
            IReportPlannerProgramService reportPlannerProgramService,
            IStringLocalizer<SharedResources> localizer
            )
        {
            this.excelExportBusinessLogic = excelExportBusinessLogic;
            this.geneticsParameterService = geneticsParameterService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.henBatchService = henBatchService;
            this.containerService = containerService;
            this.reportPlannerProgramService = reportPlannerProgramService;
            this.localizer = localizer;
        }

        #region Public Methods 

        public async Task<(decimal, decimal)> GetAverageGAD(Guid henbatchId, HenStage henStage, string plannerName)
        {
            Guid? parentId = await henBatchPerformanceService.GetAll().Where(hp => hp.HenBatchId == henbatchId)
               .Select(hp => hp.HenBatch.ParentId).FirstOrDefaultAsync();

            var weeks = await henBatchPerformanceService.GetAll()
                .Where(hp => parentId != null ? hp.HenBatchId == parentId : hp.HenBatchId == henbatchId)
                .Select(hp => hp.WeekNumber)
                .ToListAsync();

            int lastWeekWithData = weeks.Any() ? weeks.Max() : 0; // Default to 0 if no weeks are found

            List<Guid> henBatchesIds = parentId is null
                ? new List<Guid> { henbatchId }
                : GetChildBatches(parentId.Value, henStage, plannerName);

            if (plannerName == GADReportPlannerConst.ReportFemale)
                return await CalculateAverageGADFemale(henBatchesIds, lastWeekWithData);

            return await CalculateAverageGADMale(henBatchesIds, lastWeekWithData);
        }

        private async Task<(decimal, decimal)> CalculateAverageGADMale(List<Guid> henBatchesIds, int lastWeekWithData)
        {
            var henBatches = henBatchPerformanceService.GetAll(true, false)
                  .Where(hp => henBatchesIds.Contains(hp.HenBatchId)
                      && hp.WeekNumber == lastWeekWithData
                  );

            if (!henBatches.Any())
            {
                return (0, 0); // Retorna valores predeterminados si no hay datos en henBatches.
            }

            decimal averageWeight = await geneticsParameterService.GetAll(true, false)
                .Where(gp => henBatches.Any(hb => hb.GeneticId == gp.GeneticsId) && gp.TimePeriodValue == lastWeekWithData)
                .Select(gp => gp.WeightMale * 1000)
                .Where(gp => gp > 0)
                .AverageAsync();


            var data = await henBatches
                  .Select(hp => new
                  {
                      FeedIntakeMaleHp = hp.FeedIntakeMale,
                      HenAmountMalePerformance = hp.HenAmountMale > 0 ? hp.HenAmountMale : (hp.HenAmountMale == 0 ? hp.HenBatch.InitialHenAmountMale : (int?)null),
                      HenAmountMaleContainer = hp.HenBatch.HenAmountMale,
                  })
                  .Select(s => new
                  {
                      GadReal = (s.HenAmountMalePerformance ?? s.HenAmountMaleContainer) > 0
                   ? Math.Round((s.FeedIntakeMaleHp * 1000) / (s.HenAmountMalePerformance ?? s.HenAmountMaleContainer) / 7, 2)
                   : -1
                  }).ToListAsync();

            return (averageWeight, data.Where(a => a.GadReal > 0).Any() ? data.Where(a => a.GadReal > 0).Average(a => a.GadReal) : 0);
        }

        private async Task<(decimal, decimal)> CalculateAverageGADFemale(List<Guid> henBatchesIds, int lastWeekWithData)
        {
            var data = await henBatchPerformanceService.GetAll(true, false)
                  .Where(hp => henBatchesIds.Contains(hp.HenBatchId)
                      && hp.WeekNumber == lastWeekWithData
                  )
                  .Select(hp => new
                  {
                      WeightFemaleHp = hp.AvgFemaleBirdWeight * 1000,
                      HenAmountFemalePerformance = hp.HenAmountFemale > 0 ? hp.HenAmountFemale : (hp.HenAmountFemale == 0 ? hp.HenBatch.InitialHenAmountFemale : (int?)null),
                      FeedIntakeFemaleHp = hp.FeedIntakeFemale,
                      HenAmountFemaleContainer = hp.HenBatch.HenAmountFemale
                  })
                  .Select(s => new
                  {
                      GadReal = (s.HenAmountFemalePerformance ?? s.HenAmountFemaleContainer) > 0
                     ? Math.Round((s.FeedIntakeFemaleHp * 1000) / (s.HenAmountFemalePerformance ?? s.HenAmountFemaleContainer) / 7, 2)
                     : -1,
                      AverageWeight = s.WeightFemaleHp ?? -1,
                  }).ToListAsync();

            return (
                data.Where(a => a.AverageWeight > 0).Any() ? data.Where(a => a.AverageWeight > 0).Average(a => a.AverageWeight) : 0,
                data.Where(a => a.GadReal > 0).Any() ? data.Where(a => a.GadReal > 0).Average(a => a.GadReal) : 0);
        }

        /// <inheritdoc/>
        public List<GADPlannerFemaleDTO> GetGADFemale(Guid henbatchId, Range range)
        {
            HenBatch batch = henBatchService.GetAll(true, false)
                            .Where(hb => !hb.DateEnd.HasValue && hb.Id == henbatchId)
                            .FirstOrDefault();
            int currentWeek = henBatchService.GetCurrentWeekNumberForDate(henbatchId, DateTime.Now);

            const string materialId = "90D811EE-4FC1-435E-45CE-08D8FABC0A06";

            IQueryable<ReportPlannerProgram> reportPlannerPrograms = reportPlannerProgramService.GetAll(GADReportPlannerConst.ReportFemale).Where(w => w.HenbatchId == henbatchId);

            IQueryable<HenBatchPerformance> henBatchPerformances = henBatchPerformanceService.GetAll(true, false)
                .Include(i => i.HenBatch)
                .Include(i => i.HenReports).ThenInclude(th => th.ClassifiedEggs)
                .Where(w => w.HenBatchId == batch.Id);

            IQueryable<GeneticsParametersReference> geneticsParametersReferences = geneticsParameterService.GetAll(true, false)
                .Where(w => w.GeneticsId == batch.GeneticId);

            IQueryable<BedEggQuantityDTO> henReportClassifiedEgg = henBatchPerformances.SelectMany(sm => sm.HenReports).SelectMany(sm => sm.ClassifiedEggs)
                .Where(w => w.MaterialId == Guid.Parse(materialId))
                .GroupBy(gr => gr.HenReport.HenBatchPerformanceId.Value)
                .Select(s => new BedEggQuantityDTO
                {
                    HenbatchPerformanceId = s.Key,
                    Quantity = s.Sum(s => s.Quantity)
                });

            var test = henReportClassifiedEgg.ToList();

            var query = from number in Enumerable.Range(range.Start.Value, range.End.Value)
                        join b in henBatchService.GetAllActive().Where(w => w.Id == henbatchId) on 1 equals 1
                        join c in geneticsParametersReferences on new { week = number, geneticsId = b.GeneticId } equals new { week = c.TimePeriodValue, geneticsId = c.GeneticsId }
                        join d in henBatchPerformances on new { week = number, henbatchId = b.Id } equals new { week = d.WeekNumber, henbatchId = d.HenBatchId } into jhp
                        from hp in jhp.DefaultIfEmpty()
                        join e in reportPlannerPrograms on new { week = number, henbatchId = b.Id } equals new { week = e.Week, henbatchId = e.HenbatchId } into jrpp
                        from rp in jrpp.DefaultIfEmpty()
                        join f in henReportClassifiedEgg on hp?.Id equals f.HenbatchPerformanceId into jhre
                        from he in jhre.DefaultIfEmpty()
                        select new
                        {
                            Week = number,
                            HenBatch = b.DetailedName,
                            HenAmountFemalePerformance = hp?.HenAmountFemale > 0 ? hp.HenAmountFemale : (hp?.HenAmountFemale == 0 ? b.InitialHenAmountFemale : (int?)null),
                            HenAmountFemaleContainer = b.HenAmountFemale,
                            FeedIntakeFemaleGn = c.FeedIntakeFemale,
                            FeedIntakeFemaleHp = hp?.FeedIntakeFemale,
                            WeightFemaleGn = c.WeightFemale * 1000,
                            WeightFemaleHp = hp?.AvgFemaleBirdWeight * 1000,
                            WeightEgg = hp?.AvgWeightEggFromEWR * 1000,
                            UniFemale = hp?.AvgUniformityFemale,
                            CVFemale = hp?.AvgVariationCoefficientFemale,
                            ViabilityFemaleGn = c.ViableFemalesPercentage,
                            hp?.HenBatchLifeTimeFemaleViability,
                            ProductionEggGn = c.TotalProducedEggsWeeklyPercentage,
                            ProductionEggHp = hp == null ? 0 : hp.HatchableEggs + hp.CommercialEggs + hp.BrokenEggs,
                            EggBed = he?.Quantity,
                            IncubationEggsGn = c.CumulativeIncubatedEggsByHen,
                            IncubationEggsFemaleHp = hp == null
                                                            ? (decimal?)null
                                                            : hp.HenAmountFemale > 0
                                                            ? Math.Round((((decimal)hp.HatchableEggs / 7) / hp.HenAmountFemale) * 100, 2)
                                                            : (decimal?)null,
                            ProgramValue = rp?.Program,
                            InitialHenAmount = b.InitialHenAmountFemale // add initial hen amount
                        };

            List<GADPlannerFemaleDTO> records = query.Select(s => new GADPlannerFemaleDTO
            {
                Id = Guid.NewGuid(),
                Week = s.Week,
                HenBatch = s.HenBatch,
                AmountFemale = s.Week == range.Start.Value ? s.InitialHenAmount :
                       s.Week <= currentWeek ? (s.HenAmountFemalePerformance ?? s.HenAmountFemaleContainer) : (int?)null,
                WeightEgg = s.WeightEgg,
                UniformityPercentage = s.UniFemale,
                CV = s.CVFemale,
                GAD = new GADDTO
                {
                    Standard = Math.Round(s.FeedIntakeFemaleGn * 1000 / 7, 2),
                    Real = s.FeedIntakeFemaleHp.HasValue && (s.HenAmountFemalePerformance ?? s.HenAmountFemaleContainer) > 0
                   ? Math.Round((s.FeedIntakeFemaleHp.Value * 1000) / (s.HenAmountFemalePerformance ?? s.HenAmountFemaleContainer) / 7, 2)
                   : (decimal?)null,
                    Program = s.ProgramValue
                },
                AverageWeight = new AverageWeightFemaleDTO
                {
                    Real = s.WeightFemaleHp,
                    Standard = s.WeightFemaleGn,
                    Difference = Math.Round(((s.WeightFemaleHp ?? 0) / s.WeightFemaleGn) * 100, 2)
                },
                ViabilityFemale = new ViabilityDTO
                {
                    Real = s.HenBatchLifeTimeFemaleViability,
                    Standard = s.ViabilityFemaleGn
                },
                ProductionEgg = new ProductionEggDTO
                {
                    Real = (s.HenAmountFemalePerformance ?? s.HenAmountFemaleContainer) > 0
                            ? Math.Round(((s.ProductionEggHp / 7m) / (s.HenAmountFemalePerformance ?? s.HenAmountFemaleContainer)) * 100, 2)
                            : (decimal?)null,
                    Standard = s.ProductionEggGn
                },
                BedEgg = new BedEggDTO
                {
                    Real = s.ProductionEggHp > 0 && s.EggBed > 0
                            ? Math.Round(((decimal)(s.EggBed) / (decimal)(s.ProductionEggHp)) * 100, 2)
                            : (decimal?)null
                },
                IncubationEgg = new IncubationEggDTO
                {
                    Real = s.IncubationEggsFemaleHp,
                    Standard = s.IncubationEggsGn
                }
            }).ToList();

            return records;
        }

        /// <inheritdoc/>
        public List<GADPlannerMaleDTO> GetGADMale(Guid henbatchId, Range range)
        {
            HenBatch batch = henBatchService.GetAll()
                .Include(g => g.Genetic)
                .Where(hb => !hb.DateEnd.HasValue && hb.Id == henbatchId)
                .FirstOrDefault();

            int currentWeek = henBatchService.GetCurrentWeekNumberForDate(henbatchId, DateTime.Now);

            IQueryable<ReportPlannerProgram> reportPlannerPrograms = reportPlannerProgramService.GetAll(GADReportPlannerConst.ReportMale).Where(w => w.HenbatchId == henbatchId);

            IQueryable<HenBatchPerformance> henBatchPerformances = henBatchPerformanceService.GetAll(true, false)
                .Include(i => i.HenBatch)
                .Where(w => w.HenBatchId == batch.Id);

            IQueryable<GeneticsParametersReference> geneticsParametersReferences = geneticsParameterService.GetAll(true, false)
                .Where(w => w.GeneticsId == batch.GeneticId);

            var query = from number in Enumerable.Range(range.Start.Value, range.End.Value)
                        join b in henBatchService.GetAllActive().Where(w => w.Id == henbatchId) on 1 equals 1
                        join c in geneticsParametersReferences on new { week = number, geneticsId = b.GeneticId } equals new { week = c.TimePeriodValue, geneticsId = c.GeneticsId }
                        join d in henBatchPerformances on new { week = number, henbatchId = b.Id } equals new { week = d.WeekNumber, henbatchId = d.HenBatchId } into jhp
                        from hp in jhp.DefaultIfEmpty()
                        join e in reportPlannerPrograms on new { week = number, henbatchId = b.Id } equals new { week = e.Week, henbatchId = e.HenbatchId } into jrpp
                        from rp in jrpp.DefaultIfEmpty()
                        select new
                        {
                            Week = number,
                            HenBatch = b.DetailedName,
                            HenAmountMalePerformance = hp?.HenAmountMale > 0 ? hp.HenAmountMale : (hp?.HenAmountMale == 0 ? b.InitialHenAmountMale : (int?)null),
                            HenAmountMaleContainer = b.HenAmountMale,
                            FeedIntakeMaleGn = c.FeedIntakeMale,
                            FeedIntakeMaleHp = hp?.FeedIntakeMale,
                            WeightMaleGn = c.WeightMale * 1000,
                            WeightMaleHp = hp?.AvgMaleBirdWeight * 1000,
                            UniMale = hp?.AvgUniformityMale,
                            CVMale = hp?.AvgVariationCoefficientMale,
                            IncubationEggsMaleHp = (hp?.HenAmountMale ?? 0) > 0 && (hp?.HenAmountFemale ?? 0) > 0
                                            ? Math.Round(((hp?.HenAmountMale ?? 0) / (decimal)(hp?.HenAmountFemale ?? 1)) * 100, 2)
                                            : 0,
                            ProgramValue = rp?.Program,
                            InitialHenAmount = b.InitialHenAmountMale, // add initial hen amount
                            b.InitialHenAmountFemale // add initial hen amount
                        };
            var materializeQuery = query.ToList();

            List<GADPlannerMaleDTO> records = query.Select(s => new GADPlannerMaleDTO
            {
                Id = Guid.NewGuid(),
                Week = s.Week,
                HenBatch = s.HenBatch,
                AmountMale = s.Week == range.Start.Value ? s.InitialHenAmount :
                       s.Week <= currentWeek ? (s.HenAmountMalePerformance ?? s.HenAmountMaleContainer) : (int?)null,
                UniformityPercentage = s.UniMale,
                CV = s.CVMale,
                GAD = new GADDTO
                {
                    Standard = Math.Round(s.FeedIntakeMaleGn * 1000 / 7, 2),
                    Real = s.FeedIntakeMaleHp.HasValue && (s.HenAmountMalePerformance ?? s.HenAmountMaleContainer) > 0
                   ? Math.Round((s.FeedIntakeMaleHp.Value * 1000) / (s.HenAmountMalePerformance ?? s.HenAmountMaleContainer) / 7, 2)
                   : (decimal?)null,
                    Program = s.ProgramValue
                },
                AverageWeight = new AverageWeightMaleDTO
                {
                    Real = s.WeightMaleHp,
                    Standard = s.WeightMaleGn,
                    Weight = Math.Round(((s.WeightMaleHp ?? 0) / s.WeightMaleGn) * 100, 2)
                },
                HenRelationship = new RelationshipMaleFemaleDTO
                {
                    Real = s.Week == range.Start.Value && s.InitialHenAmount > 0 && s.InitialHenAmountFemale > 0 ?
                            Math.Round((s.InitialHenAmount / (decimal)(s.InitialHenAmountFemale)) * 100, 2) : s.IncubationEggsMaleHp,
                    Standard = 0
                }
            }).ToList();

            return records;
        }

        private List<List<ExportMale>> GetMaleDTO(Guid henbatchId, Guid warehouseId, HenStage henStage)
        {
            List<Guid> henBatchesIds = GetChildBatches(henbatchId, henStage, GADReportPlannerConst.ReportMale, warehouseId);
            Range range = (henStage == HenStage.Laying) ? new Range(21, 50) : new Range(1, 18);
            List<List<ExportMale>> planners = new List<List<ExportMale>>();
            foreach (var henBatchId in henBatchesIds)
            {
                var result = GetGADMale(henBatchId, range);
                List<ExportMale> dtos = result.Select(r => new ExportMale()
                {
                    Week = r.Week,
                    Henbatch = r.HenBatch,
                    AmountMale = r.AmountMale ?? 0,
                    AverageStandard = r.AverageWeight.Standard ?? 0,
                    AverageReal = r.AverageWeight.Real ?? 0,
                    AverageWeight = r.AverageWeight.Weight ?? 0,
                    GADStandard = r.GAD.Standard ?? 0,
                    GADProgram = r.GAD.Program ?? 0,
                    GADReal = r.GAD.Real ?? 0,
                    CV = r.CV ?? 0,
                    UniformityPercentage = r.UniformityPercentage ?? 0
                }).ToList();
                planners.Add(dtos);
            }

            // Ordena los planners basados en el código de aviario (tercer elemento del split).
            planners = planners.OrderBy(innerList =>
            {
                var henBatch = innerList.FirstOrDefault()?.Henbatch;
                if (henBatch != null)
                {
                    var parts = henBatch.Split('|');
                    return parts.Length > 2 ? parts[2].Trim() : string.Empty; // Parte de "Aviário"
                }
                return string.Empty;
            }).ToList();

            return planners; // Ensure the method always returns a value
        }

        /// <inheritdoc/>
        public Task<ExportResult> GetGADMaleReportAsync(Guid henbatchId, Guid warehouseId, HenStage henStage)
        {
            //Get all data to export
            List<List<ExportMale>> planners = GetMaleDTO(henbatchId, warehouseId, henStage);

            List<IExportableDTO> dynamicObjects = DynamicObjectBuilder.BuildDynamicObjects<ExportMale>(planners, GetDynamicPropertiesForMaleReport());

            SetDecimalWeekDataForProperty("DP1", dynamicObjects, GetParentAvgMaleBirdWeight(henbatchId));
            SetDecimalWeekDataForProperty("DP2", dynamicObjects, GetGADMaleAverage(planners));

            string type = henStage == HenStage.Laying ? localizer[Lang.Laying] : localizer[Lang.Breeding];
            string genre = localizer[Lang.ExcelExportMale];
            string fileDownloadName = string.Format(localizer[Lang.ExcelExportFilename], type, genre, DateTime.Now.ToString("yyyyMMdd"));
            return excelExportBusinessLogic.ExportExcel(dynamicObjects.AsQueryable(), fileDownloadName);
        }

        private List<List<ExportFemale>> GetFemaleDTO(Guid henbatchId, Guid warehouseId, HenStage henStage)
        {
            List<Guid> henBatchesIds = GetChildBatches(henbatchId, henStage, GADReportPlannerConst.ReportFemale, warehouseId);
            Range range = (henStage == HenStage.Laying) ? new Range(21, 50) : new Range(1, 18);

            List<List<ExportFemale>> planners = new List<List<ExportFemale>>();
            foreach (var henBatchId in henBatchesIds)
            {
                var result = GetGADFemale(henBatchId, range);
                List<ExportFemale> dtos = result.Select(r => new ExportFemale()
                {
                    Week = r.Week,
                    Henbatch = r.HenBatch,
                    EggWeight = r.AmountFemale ?? 0,
                    GADStandard = r.GAD.Standard ?? 0,
                    GADProgram = r.GAD.Program ?? 0,
                    GADReal = r.GAD.Real ?? 0,
                    AverageStandard = r.AverageWeight.Standard ?? 0,
                    AverageReal = r.AverageWeight.Real ?? 0,
                    AverageWeight = r.AverageWeight.Difference ?? 0,
                    UniformityPercentage = r.UniformityPercentage ?? 0,
                    CV = r.CV
                }).ToList();
                planners.Add(dtos);
            }

            return planners.OrderBy(innerList => innerList.FirstOrDefault()?.Henbatch).ToList();
        }
        /// <inheritdoc/>
        public Task<ExportResult> GetGADFemaleReportAsync(Guid henbatchId, Guid warehouseId, HenStage henStage)
        {
            //Get all data to export
            List<List<ExportFemale>> planners = GetFemaleDTO(henbatchId, warehouseId, henStage);

            // Construir los objetos dinámicos basados en el henStage
            List<IExportableDTO> dynamicObjects = BuildDynamicObjectsForStage(planners, henStage);

            Range range = (henStage == HenStage.Laying) ? new Range(21, 50) : new Range(1, 18);

            List<GADPlannerFemaleDTO> femalePlanner = GetGADFemale(henbatchId, range);

            // Establecer valores para las propiedades en el objeto dinámico
            SetDynamicProperties(henStage, henbatchId, dynamicObjects, femalePlanner, planners);


            string type = henStage == HenStage.Laying ? localizer[Lang.Laying] : localizer[Lang.Breeding];
            string genre = localizer[Lang.ExcelExportFemale];
            string fileDownloadName = string.Format(localizer[Lang.ExcelExportFilename], type, genre, DateTime.Now.ToString("yyyyMMdd"));
            return excelExportBusinessLogic.ExportExcel(dynamicObjects.AsQueryable(), fileDownloadName);
        }

        private List<IExportableDTO> BuildDynamicObjectsForStage(List<List<ExportFemale>> planners, HenStage henStage)
        {
            return henStage == HenStage.Laying
                ? DynamicObjectBuilder.BuildDynamicObjects<ExportFemale>(planners, GetDynamicPropertiesForFemaleReport())
                : DynamicObjectBuilder.BuildDynamicObjects<ExportFemale>(planners, GetDynamicPropertiesForFemaleReportBreeding());
        }

        private void SetDynamicProperties(HenStage henStage, Guid henbatchId, List<IExportableDTO> dynamicObjects, List<GADPlannerFemaleDTO> femalePlanner, List<List<ExportFemale>> planners)
        {
            // Propiedades comunes
            SetDecimalWeekDataForProperty("DP3", dynamicObjects, GetPlanFemaleWeek(femalePlanner, f => f.ViabilityFemale.Standard));
            SetDecimalWeekDataForProperty("DP4", dynamicObjects, GetPlanFemaleWeek(femalePlanner, f => f.ViabilityFemale.Real));
            SetDecimalWeekDataForProperty("DP10", dynamicObjects, GetParentAvgFemaleBirdWeight(henbatchId));
            SetDecimalWeekDataForProperty("DP11", dynamicObjects, GetGADFemaleAverage(planners));

            if (henStage == HenStage.Laying)
            {
                // Propiedades específicas de Laying
                SetDecimalWeekDataForProperty("DP1", dynamicObjects, SetPesoOvoPadraoFemaleReport(henbatchId, henStage));
                SetDecimalWeekDataForProperty("DP2", dynamicObjects, SetPesoOvoRealFemaleReport(henbatchId));
                SetDecimalWeekDataForProperty("DP5", dynamicObjects, GetPlanFemaleWeek(femalePlanner, f => f.ProductionEgg.Standard));
                SetDecimalWeekDataForProperty("DP6", dynamicObjects, GetPlanFemaleWeek(femalePlanner, f => f.ProductionEgg.Real));
                SetDecimalWeekDataForProperty("DP7", dynamicObjects, GetBedEgg(henbatchId));
                SetDecimalWeekDataForProperty("DP8", dynamicObjects, GetPlanFemaleWeek(femalePlanner, f => f.IncubationEgg.Standard));
                SetDecimalWeekDataForProperty("DP9", dynamicObjects, GetPlanFemaleWeek(femalePlanner, f => f.IncubationEgg.Real));
            }
        }

        /// <inheritdoc/>
        public async Task SaveProgram(string plannerName, Guid henbatchId, IEnumerable<ReportPlannerProgram> values)
        {
            var reportPlannerValues = await reportPlannerProgramService.GetAll(plannerName).Where(w => w.HenbatchId == henbatchId)
                .ToListAsync();

            var entitiesToUpdate = values.Intersect(reportPlannerValues, new ReportPlannerValueComparer());
            var entitiesToAdd = values.Except(reportPlannerValues, new ReportPlannerValueComparer());

            foreach (ReportPlannerProgram program in entitiesToUpdate)
            {
                ReportPlannerProgram entityDb = reportPlannerValues.FirstOrDefault(f => f.Week == program.Week);

                program.CopyTo(entityDb);

                await reportPlannerProgramService.UpdateAsync(entityDb);
            }

            foreach (ReportPlannerProgram entity in entitiesToAdd)
                await reportPlannerProgramService.CreateAsync(entity);

        }
        #endregion

        #region Private functions
        private List<Guid> GetChildBatches(Guid henbatchId, HenStage henStage, string plannerName, Guid? warehouseId = null)
        {
            IQueryable<HenBatch> henBatches = this.henBatchService.GetAll(true, false)
                .Where(hb => hb.HenStage == henStage && !hb.DateEnd.HasValue && hb.ParentId.HasValue && hb.ParentId.Value == henbatchId).OrderBy(x => x.Code);

            if (plannerName == GADReportPlannerConst.ReportFemale)
                henBatches = henBatches.Where(w => w.HenAmountFemale > 0);

            if (plannerName == GADReportPlannerConst.ReportMale)
                henBatches = henBatches.Where(w => w.HenAmountMale > 0);

            if (warehouseId.HasValue)
            {
                var lineIdsInWarehouse = containerService.GetAll()
                    .Where(c => ((Line)c).WarehouseId == warehouseId.Value)
                    .Select(c => c.Id)
                    .ToList();

                if (lineIdsInWarehouse.Any())
                {
                    henBatches = henBatches.Where(b => b.LineId.HasValue && lineIdsInWarehouse.Contains(b.LineId.Value));
                }
            }

            return henBatches
                    .Select(h => h.Id)
                    .ToList();
        }

        private List<DynamicPropertyDto> GetDynamicPropertiesForMaleReport()
        {
            var properties = new List<DynamicPropertyDto>();

            // Crear primera propiedad
            var displayAttr1 = new DisplayAttribute
            {
                Name = "Peso Médio do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP1", typeof(decimal?), displayAttr1));

            // Crear segunda propiedad
            var displayAttr2 = new DisplayAttribute
            {
                Name = "GAD Médio do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP2", typeof(decimal?), displayAttr2));

            return properties;
        }

        private List<DynamicPropertyDto> GetDynamicPropertiesForFemaleReport()
        {
            var properties = new List<DynamicPropertyDto>();

            var displayAttr1 = new DisplayAttribute
            {
                Name = "Peso Ovo Padrão do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP1", typeof(decimal?), displayAttr1));

            var displayAttr2 = new DisplayAttribute
            {
                Name = "Peso Ovo Real do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP2", typeof(decimal?), displayAttr2));


            var displayAttr3 = new DisplayAttribute
            {
                Name = "viabilidade F Padrão do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP3", typeof(decimal?), displayAttr3));

            var displayAttr4 = new DisplayAttribute
            {
                Name = "Viabilidade F Real do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP4", typeof(decimal?), displayAttr4));

            var displayAttr5 = new DisplayAttribute
            {
                Name = "Produção Ovos Padrão do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP5", typeof(decimal?), displayAttr5));

            var displayAttr6 = new DisplayAttribute
            {
                Name = "Produção Ovos Real do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP6", typeof(decimal?), displayAttr6));

            var displayAttr7 = new DisplayAttribute
            {
                Name = "Ovos Cama do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP7", typeof(decimal?), displayAttr7));

            var displayAttr8 = new DisplayAttribute
            {
                Name = "Ovos incubaveis p/F Cap Padrão do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP8", typeof(decimal?), displayAttr8));

            var displayAttr9 = new DisplayAttribute
            {
                Name = "Ovos incubaveis p/F Cap Real do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP9", typeof(decimal?), displayAttr9));

            var displayAttr10 = new DisplayAttribute
            {
                Name = "Peso Médio do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP10", typeof(decimal?), displayAttr10));

            var displayAttr11 = new DisplayAttribute
            {
                Name = "GAD Medio do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP11", typeof(decimal?), displayAttr11));

            return properties;
        }
        private List<DynamicPropertyDto> GetDynamicPropertiesForFemaleReportBreeding()
        {
            var properties = new List<DynamicPropertyDto>();

            var displayAttr3 = new DisplayAttribute
            {
                Name = "viabilidade F Padrão do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP3", typeof(decimal?), displayAttr3));

            var displayAttr4 = new DisplayAttribute
            {
                Name = "Viabilidade F Real do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP4", typeof(decimal?), displayAttr4));

            var displayAttr10 = new DisplayAttribute
            {
                Name = "Peso Médio do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP10", typeof(decimal?), displayAttr10));

            var displayAttr11 = new DisplayAttribute
            {
                Name = "GAD Medio do Lote"
            };
            properties.Add(new DynamicPropertyDto("DP11", typeof(decimal?), displayAttr11));

            return properties;
        }
        private Func<int, decimal?> GetParentAvgMaleBirdWeight(Guid henbatchId)
        {
            //Parent batch
            var parentBatchAvgBirdWeight = henBatchPerformanceService.GetAll()
                    .Where(hbp => hbp.HenBatchId == henbatchId)
                    .OrderBy(hbp => hbp.WeekNumber)
                    .Select(hbp => new
                    {
                        hbp.WeekNumber,
                        hbp.AvgMaleBirdWeight
                    })
                    .ToList();

            return (int weekNumber) =>
            {
                return parentBatchAvgBirdWeight.Where(hbp => hbp.WeekNumber == weekNumber)
                        .Select(hbp => hbp.AvgMaleBirdWeight).FirstOrDefault();
            };
        }

        private Func<int, decimal?> GetParentAvgFemaleBirdWeight(Guid henbatchId)
        {
            //Parent batch
            var parentBatchAvgBirdWeight = henBatchPerformanceService.GetAll()
                    .Where(hbp => hbp.HenBatchId == henbatchId)
                    .OrderBy(hbp => hbp.WeekNumber)
                    .Select(hbp => new
                    {
                        hbp.WeekNumber,
                        hbp.AvgFemaleBirdWeight
                    })
                    .ToList();

            return (int weekNumber) =>
            {
                return parentBatchAvgBirdWeight.Where(hbp => hbp.WeekNumber == weekNumber)
                        .Select(hbp => hbp.AvgFemaleBirdWeight).FirstOrDefault();
            };
        }

        private Func<int, decimal?> GetGADMaleAverage(List<List<ExportMale>> planners)
        {
            return (int weekNumber) => planners.SelectMany(p => p).Where(p => p.Week == weekNumber && p.GADReal > 0)
                       .Select(p => p.GADReal)
                       .Average(p => p);

        }
        private Func<int, decimal?> GetGADFemaleAverage(List<List<ExportFemale>> planners)
        {
            return (int weekNumber) => planners.SelectMany(p => p).Where(p => p.Week == weekNumber && p.GADReal > 0)
                       .Select(p => p.GADReal)
                       .Average(p => p);

        }
        public Func<int, decimal?> SetPesoOvoPadraoFemaleReport(Guid henbatchId, HenStage henStage)
        {
            Guid parentGeneticId = this.henBatchService.GetAll(true, false)
                     .Where(hb => hb.HenStage == henStage && !hb.DateEnd.HasValue && !hb.ParentId.HasValue && hb.Id == henbatchId)
                     .Select(h => h.GeneticId)
                     .FirstOrDefault();

            var geneticsParametersReferences =
                geneticsParameterService.GetAll(true, false)
                .Where(gp => gp.GeneticsId == parentGeneticId && gp.EggWeight > 0)
                .Select(gp => new
                {
                    Week = gp.TimePeriodValue,
                    gp.EggWeight
                });

            return (int weekNumber) =>
            {
                return geneticsParametersReferences
                    .Where(p => p.Week == weekNumber)
                    .Select(p => p.EggWeight)
                    .FirstOrDefault();
            };
        }

        public Func<int, decimal?> GetBedEgg(Guid henbatchId)
        {
            List<Guid> batchs = henBatchService.GetAll(true, false)
                            .Where(hb => !hb.DateEnd.HasValue && hb.ParentId == henbatchId)
                            .Select(g => g.Id).ToList();
            IQueryable<HenBatchPerformance> henBatchPerformances = henBatchPerformanceService.GetAll(true, false)
                .Include(i => i.HenReports).ThenInclude(th => th.ClassifiedEggs)
                .Where(hbp => batchs.Contains(hbp.HenBatchId));

            const string materialId = "90D811EE-4FC1-435E-45CE-08D8FABC0A06";

            var henReportClassifiedEgg = henBatchPerformances.SelectMany(sm => sm.HenReports).SelectMany(sm => sm.ClassifiedEggs)
                .Where(w => w.MaterialId == Guid.Parse(materialId))
                .GroupBy(gr => new
                {
                    WeekNumber = gr.HenReport.HenBatchPerformance.WeekNumber,
                    HatchableEggs = gr.HenReport.HenBatchPerformance.HatchableEggs,
                    CommercialEggs = gr.HenReport.HenBatchPerformance.CommercialEggs,
                    BrokenEggs = gr.HenReport.HenBatchPerformance.BrokenEggs
                })
    .Select(s => new
    {
        Week = s.Key.WeekNumber, // Obtener WeekNumber del grupo
        Percentage = (s.Key.HatchableEggs + s.Key.CommercialEggs + s.Key.BrokenEggs) > 0 && s.Sum(sg => sg.Quantity) > 0
            ? Math.Round(((decimal)s.Sum(sg => sg.Quantity) / (decimal)(s.Key.HatchableEggs + s.Key.CommercialEggs + s.Key.BrokenEggs)) * 100, 2)
            : (decimal?)null // Cálculo del porcentaje directamente
    }).ToList();

            return (int weekNumber) =>
            {
                return henReportClassifiedEgg
                    .Where(p => p.Week == weekNumber)
                    .Select(p => p.Percentage)
                    .FirstOrDefault();
            };
        }

        public Func<int, decimal?> GetPlanFemaleWeek(List<GADPlannerFemaleDTO> gADPlanners, Func<GADPlannerFemaleDTO, decimal?> selector)
        {
            return (int weeknumber) =>
            {
                return gADPlanners.Where(g => g.Week == weeknumber).Select(selector).FirstOrDefault();
            };
        }
        /// <summary>
        /// DP2 Value for female report
        /// </summary>
        /// <param name="henbatchId"></param>
        /// <returns></returns>
        public Func<int, decimal?> SetPesoOvoRealFemaleReport(Guid henbatchId)
        {
            var pesoOvo = this.henBatchPerformanceService.GetAll(true, false)
                     .Where(hb => hb.Id == henbatchId && hb.AvgWeightEggFromEWR > 0)
                     .Select(h => new
                     {
                         h.WeekNumber,
                         WeightEgg = h.AvgWeightEggFromEWR * 1000
                     });

            return (int weekNumber) =>
            {
                return pesoOvo
                    .Where(p => p.WeekNumber == weekNumber)
                    .Select(p => p.WeightEgg)
                    .FirstOrDefault();
            };
        }

        private void SetDecimalWeekDataForProperty(string propertyName, List<IExportableDTO> dynamicObjects, Func<int, decimal?> calculateWeekValue)
        {
            foreach (IExportableDTO item in dynamicObjects)
            {
                PropertyInfo propertyInfoTarget = item.GetType().GetProperty("Week[0]");
                if (propertyInfoTarget == null)
                    continue;

                int weekNumber = int.Parse(propertyInfoTarget.GetValue(item).ToString());
                decimal? average = calculateWeekValue(weekNumber);
                if (!average.HasValue)
                    average = 0;

                PropertyInfo property = item.GetType().GetProperty(propertyName);
                property.SetValue(item, Math.Round(average.Value, 2));

            }
        }
        #endregion
    }

}
