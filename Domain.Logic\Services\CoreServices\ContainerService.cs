using Binit.Framework;
using Binit.Framework.Constants.DAL;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Binit.Shaper.Entities.Alias;
using Binit.Shaper.Interfaces.Services;
using DAL.Interfaces;
using DAL.Services;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs.StockDTOs;
using Domain.Logic.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.Services.ContainerService;

namespace Domain.Logic.Services
{
    /// <summary>
    /// Cluster specific services.
    /// </summary>
    public class ContainerService<TContainer> : ServiceTenantDependent<TContainer>, IContainerService<TContainer> where TContainer : Container
    {
        internal readonly IClusterService clusterService;
        private readonly IGeneticService geneticService;
        private readonly IFarmService farmService;
        private readonly IServiceProvider provider;
        private readonly IInconsistencyReportService inconsistencyReportService;
        protected readonly IMaterialTypeService materialTypeService;
        private readonly ISectorService sectorService;
        protected readonly IServiceTenantDependent<TenantDependentEntityFile> fileService;
        protected readonly IAliasExtensionService aliasExtensionService;
        protected readonly IService<TenantConfiguration> tenantConfigurationService;

        public ContainerService(
            IExceptionManager exceptionManager, ILogger logger, IOperationContext operationContext,
            IUnitOfWork unitOfWork, IStringLocalizer<SharedResources> localizer, IMediator mediator,
            IFarmService farmService, IClusterService clusterService, IGeneticService geneticService,
            IInconsistencyReportService inconsistencyReportService,
            IServiceProvider provider, IMaterialTypeService materialTypeService,
            IServiceTenantDependent<TenantDependentEntityFile> fileService, ISectorService sectorService,
            IAliasExtensionService aliasExtensionService,
            IService<TenantConfiguration> tenantConfigurationService)
            : base(exceptionManager, logger, operationContext, unitOfWork, localizer, mediator)
        {
            this.clusterService = clusterService;
            this.geneticService = geneticService;
            this.farmService = farmService;
            this.provider = provider;
            this.inconsistencyReportService = inconsistencyReportService;
            this.materialTypeService = materialTypeService;
            this.fileService = fileService;
            this.sectorService = sectorService;
            this.aliasExtensionService = aliasExtensionService;
            this.tenantConfigurationService = tenantConfigurationService;
        }

        /// <summary>
        /// Creates a container. Can generates detailed name.
        /// </summary>
        public override async Task CreateAsync(TContainer entity)
        {
            entity.DetailedName = CreateDetailedName(entity);

            if (!string.IsNullOrEmpty(entity.Code) && !ValidateCodeUniqueness(entity, DALOperations.Create))
                throw new ValidationException($"ContainerProperties.{nameof(entity.Code)}", this.localizer[Lang.CodeIsUnique]);

            if (entity.Files != null)
            {
                List<TenantDependentEntityFile> newFiles = entity.Files;
                entity.Files = null;
                foreach (TenantDependentEntityFile file in newFiles)
                {
                    file.TenantDependentEntityId = entity.Id;
                    await fileService.CreateAsync(file);
                }
            }

            if (entity.SectorId.HasValue)
            {
                Sector sector = this.sectorService.Get(entity.SectorId.Value);
                entity.FarmId = sector.FarmId;
                entity.CompanyId = sector.CompanyId;
            }

            await base.CreateAsync(entity);
        }

        private bool ValidateCodeUniqueness(Container container, DALOperations operation)
        {
            bool codeUniqueness = true;

            if (container is FeedFactory || container is ClassificationWarehouse || container is StorageWarehouse)
            {
                string containerType = container switch
                {
                    FeedFactory ff => ContainerTypes.FeedFactory,
                    ClassificationWarehouse cw => ContainerTypes.ClassificationWarehouse,
                    StorageWarehouse sw => ContainerTypes.StorageWarehouse
                };

                codeUniqueness = operation switch
                {
                    DALOperations.Create => !GetAll().Any(c => c.Code.ToUpper() == container.Code.ToUpper() && c.ContainerType == containerType && c.FarmId == container.FarmId),
                    DALOperations.Update => !GetAll().Any(c => c.Id != container.Id && c.Code.ToUpper() == container.Code.ToUpper() && c.ContainerType == containerType && c.FarmId == container.FarmId)
                };

            }
            return codeUniqueness;
        }

        public async override Task UpdateAsync(TContainer entity)
        {
            if (!entity.ModifiedDetailedName)
                entity.DetailedName = CreateDetailedName(entity);

            if (!string.IsNullOrEmpty(entity.Code) && !ValidateCodeUniqueness(entity, DALOperations.Update))
                throw new ValidationException($"ContainerProperties.{nameof(entity.Code)}", this.localizer[Lang.CodeIsUnique]);

            if (entity.Files != null)
                await this.UpdateFiles(entity.Id, entity.Files);

            await base.UpdateAsync(entity);
        }

        /// <summary>
        /// Update container passed from a job
        /// </summary>
        public async Task UpdateFromJobAsync(TContainer entity, IStringLocalizer customLocalizer)
        {
            if (!entity.ModifiedDetailedName)
                entity.DetailedName = CreateDetailedName(entity, customLocalizer);

            if (!string.IsNullOrEmpty(entity.Code) && !ValidateCodeUniqueness(entity, DALOperations.Update))
                throw new Exception(customLocalizer[Lang.CodeIsUnique]);

            if (entity.Files != null)
                await this.UpdateFiles(entity.Id, entity.Files);

            await base.UpdateAsync(entity);
        }

        public DayOfWeek GetDayOfWeek(Guid id)
        {
            TContainer container = GetAll()
                .Include(c => c.Farm)
                .FirstOrDefault(c => c.Id == id);

            if (container == null || container.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]));

            return container.Farm.DayOfWeek;
        }

        public string CreateDetailedName(Container entity, IStringLocalizer customLocalizer = null)
        {
            IContainerService<Container> containerService = provider.GetService(typeof(IContainerService<Container>)) as IContainerService<Container>;

            bool hasCluster = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");

            (bool multipleFarms, string farmName, bool multipleClusters) farmQuery = new ValueTuple<bool, string, bool>();

            if (entity.FarmId != null)
            {
                Farm farm = farmService.GetAllIgnoringClaims()
                    .Where(f => f.Id == entity.FarmId)
                    .Include(f => f.Clusters)
                    .Include(f => f.Company).ThenInclude(c => c.Farms)
                    .FirstOrDefault();

                farmQuery = new ValueTuple<bool, string, bool>(
                        farm.Company.Farms.Count > 1,
                        farm.Name,
                        farm.Clusters.Count > 1 && hasCluster);
            }

            LinkedList<string> nameContainers = new LinkedList<string>();

            if (!string.IsNullOrEmpty(farmQuery.farmName))
                nameContainers.AddFirst(farmQuery.farmName);

            switch (entity)
            {
                case StorageWarehouse storageWarehouse:
                    nameContainers.AddLast(entity.Name);
                    break;

                case ClassificationWarehouse classificationWarehouse:
                    nameContainers.AddLast(entity.Name);
                    break;

                case PackingWarehouse packingWarehouse:
                    nameContainers.AddLast(entity.Name);
                    break;

                case Silo silo:
                    nameContainers.AddLast(entity.Name);
                    break;

                case FeedFactory feedFactory:
                    nameContainers.AddLast(entity.Name);
                    break;

                case HenWarehouse henWarehouse:
                    if (farmQuery.multipleClusters)
                        nameContainers.AddLast($"{clusterService.Get(henWarehouse.ClusterId).Name}");

                    nameContainers.AddLast(henWarehouse.Name);
                    break;

                case Line line:
                    var lineQuery = containerService.GetAll()
                        .Where(c => c.Id == line.WarehouseId)
                        .Select(c => new
                        {
                            henWarehouseName = c.Name,
                            clusterName = hasCluster ? (c as HenWarehouse).Cluster.Name : ""
                        }).First();

                    if (farmQuery.multipleClusters && hasCluster)
                        nameContainers.AddLast(lineQuery.clusterName);

                    nameContainers.AddLast(lineQuery.henWarehouseName);
                    nameContainers.AddLast(line.Name);

                    break;
                case HenBatch henBatch:
                    if (!string.IsNullOrEmpty(farmQuery.farmName))
                        nameContainers.Remove(farmQuery.farmName);

                    nameContainers.AddFirst(customLocalizer == null ? this.localizer[Lang.HenBatch] : customLocalizer[Lang.HenBatch]);

                    switch (henBatch.HenBatchType)
                    {
                        case HenBatchTypeEnum.Parent:
                            string farmCode = farmService.GetAll().Where(f => f.Id == henBatch.FarmId).Select(f => f.Code).First();
                            nameContainers.AddLast(farmCode);
                            nameContainers.AddLast(henBatch.Code);
                            break;

                        case HenBatchTypeEnum.Child:
                        case HenBatchTypeEnum.Single:
                            Line line = containerService.GetAll()
                                .Include(l => (l as Line).Warehouse).ThenInclude(w => w.Cluster)
                                .FirstOrDefault(l => l.Id == henBatch.LineId) as Line;

                            if (farmQuery.multipleClusters && hasCluster)
                                nameContainers.AddLast(line.Warehouse.Cluster.Name);

                            nameContainers.AddLast(henBatch.Code);
                            nameContainers.AddLast(line.Warehouse.Name);
                            nameContainers.AddLast(line.Name);
                            break;
                    }

                    break;

                case DispositionWarehouse dispositionWarehouse:
                    nameContainers.AddLast(entity.Name);
                    break;

                case ReceptionWarehouse receptionWarehouse:
                    if (!string.IsNullOrEmpty(farmQuery.farmName))
                        nameContainers.Remove(farmQuery.farmName);
                    string farmCodeRW = farmService.GetAll().Where(f => f.Id == receptionWarehouse.FarmId).Select(f => f.Code).First();
                    nameContainers.AddFirst(farmCodeRW);
                    nameContainers.AddLast(farmQuery.farmName);
                    nameContainers.AddLast(customLocalizer == null ? this.localizer[Lang.ReceptionWarehouse] : customLocalizer[Lang.ReceptionWarehouse]);
                    break;

                default:
                    return entity.Name;
            }

            return string.Join(" | ", nameContainers);
        }

        /// <summary>
        ///  Function used to get a container with the necesary relationships to handle a shipping note
        /// </summary>
        public IQueryable<TContainer> GetForHandleShippingNote(bool asNoTracking = false, bool? active = true, params Guid[] ids)
        {
            ids = ids.Where(i => i != default).ToArray();

            return GetAllIgnoringClaims(asNoTracking: asNoTracking)
                .Where(c => !active.HasValue || c.Active && ids.Contains(c.Id))
                .Include(c => c.AcceptedMaterialType).ThenInclude(cmt => cmt.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(cmt => cmt.MaterialType)
                .Include(c => c.OriginContainers);
        }

        /// <summary>
        /// Returns a container by Id with its material containers and accepted material types.
        /// </summary>
        public TContainer GetWithMaterial(Guid id, bool asNoTracking = false, bool? active = true)
        {
            TContainer container = GetAll()
                .Where(c => !active.HasValue || c.Active)
                .Include(c => c.AcceptedMaterialType).ThenInclude(cmt => cmt.MaterialType)
                .Include(c => c.AcceptedMaterialType).ThenInclude(cmt => cmt.CapacityUnit)
                .Include(c => c.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.CapacityUnit)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.MaterialBatches).ThenInclude(m => m.MaterialBatch)
                .Include(c => c.OriginContainers).ThenInclude(o => o.Origin)
                .Include(c => c.DestinationInconsistencyReports)
                .Include(c => c.OriginInconsistencyReports)
                .FirstOrDefault(c => c.Id == id);

            if (container == null || container.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]));

            return container;
        }

        /// <inheritdoc/>
        public StockAdjustmentDTO GetForStockAdjustment(Guid id)
        {
            return GetAll(true).Where(c => c.Id == id && c.Active)
                .Select(c => new StockAdjustmentDTO()
                {
                    Id = c.Id,
                    MaterialAdjustments = c.MaterialContainers
                        .Where(mc => mc.Quantity > 0)
                        .Select(mc => new MaterialAdjustmentDTO()
                        {
                            Id = mc.MaterialId,
                            Name = mc.Material.Name,
                            MeasurementUnit = mc.CapacityUnit.Symbol,
                            MeasurementUnitId = mc.CapacityUnitId,
                            Quantity = mc.Quantity,
                            MaterialBatchAdjustments = mc.MaterialBatches
                                .Where(mb => mb.Quantity > 0)
                                .Select(mb => new MaterialBatchAdjustmentDTO()
                                {
                                    Id = mb.MaterialBatchId,
                                    Name = mb.MaterialBatch.Name,
                                    Quantity = mb.Quantity
                                })
                        })

                }).FirstOrDefault();
        }

        /// <summary>
        /// Returns a container by Id with its material containers and accepted material.
        /// </summary>
        public TContainer GetWithMaterialAndAcceptedMaterial(Guid id, bool asNoTracking = false)
        {
            TContainer container = GetAll(asNoTracking)
                .Include(c => c.AcceptedMaterialType).ThenInclude(cmt => cmt.MaterialType)
                .Include(c => c.AcceptedMaterialType).ThenInclude(cmt => cmt.CapacityUnit)
                .Include(c => c.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.CapacityUnit)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.MaterialBatches).ThenInclude(m => m.MaterialBatch)
                .FirstOrDefault(c => c.Id == id);

            if (container == null || container.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]));

            return container;
        }

        /// <summary>
        /// Returns an IQueryable of containers with its material containers and accepted material types.
        /// </summary>
        public IQueryable<TContainer> GetAllWithMaterial(bool ignoringClaims = false)
        {
            IQueryable<TContainer> containers = ignoringClaims ? GetAllIgnoringClaims() : GetAll();

            return containers
                .Include(c => c.AcceptedMaterialType).ThenInclude(cm => cm.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.CapacityUnit)
                .Include(c => c.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
                .Include(c => c.OriginContainers);
        }

        /// <summary>
        /// Returns an IQueryable of containers with its happening containers and happening types.
        /// </summary>
        public IQueryable<TContainer> GetAllWithHappenings()
        {
            IQueryable<TContainer> containers = GetAll()
                .Include(c => c.HappeningContainers).ThenInclude(hc => hc.Happening).ThenInclude(h => h.HappeningType);

            return containers;
        }

        /// <summary>
        /// Returns an IQueryable of containers all their relations.
        /// </summary>
        public IQueryable<Container> GetAllFull()
        {
            IQueryable<Container> containers = GetAll()
                .Include(c => c.OriginContainers)
                .Include(c => c.AreaContainers)
                .Include(c => c.DestinationShippingNotes)
                .Include(c => c.OriginShippingNotes)
                .Include(c => c.Farm).ThenInclude(f => f.Company)
                .Include(c => c.AcceptedMaterialType).ThenInclude(cm => cm.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.Material).ThenInclude(m => m.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.CapacityUnit);

            return containers;
        }

        /// <summary>
        /// Returns an IQueryable of active containers.
        /// </summary>
        public IQueryable<Container> GetAllActive() => GetAll().Where(c => c.Active);

        /// <summary>
        /// Returns an IQueryable of active containers whith all its relationships .
        /// </summary>
        public IQueryable<Container> GetAllFullActive() => GetAllFull().Where(c => c.Active);

        /// <summary>
        /// Returns a Container with all their relations.
        /// </summary>
        public Container GetFull(Guid id)
        {
            Container container = GetAll()
                .Include(c => c.OriginContainers).ThenInclude(o => o.Origin).ThenInclude(o => o.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType)
                .Include(c => c.OriginContainers).ThenInclude(o => o.Origin).ThenInclude(o => o.MaterialContainers)
                .Include(c => c.DestinationShippingNotes)
                .Include(c => c.OriginShippingNotes)
                .Include(c => c.AcceptedMaterialType).ThenInclude(cm => cm.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.Material).ThenInclude(m => m.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.MaterialBatches).ThenInclude(m => m.MaterialBatch)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.CapacityUnit)

                .Where(x => x.Id == id)
                .FirstOrDefault();

            return container;
        }

        /// <summary>
        /// Returns a Container with all their materials and capacity units.
        /// </summary>
        public Container GetWithMaterialContainers(Guid id)
        {
            Container container = GetAll()
                .Include(c => c.MaterialContainers).ThenInclude(m => m.Material).ThenInclude(m => m.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.CapacityUnit)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.MaterialBatches).ThenInclude(m => m.MaterialBatch)
                .Where(x => x.Id == id)
                .FirstOrDefault();

            return container;
        }

        /// <summary>
        /// Returns a Container with origin containers
        /// </summary>
        public Container GetWithOriginContainers(Guid id)
        {
            Container container = GetAll()
                .Include(c => c.OriginContainers).ThenInclude(o => o.Origin).ThenInclude(o => o.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType)
                .Include(c => c.OriginContainers).ThenInclude(o => o.Origin).ThenInclude(o => o.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
                .Where(x => x.Id == id)
                .FirstOrDefault();

            return container;
        }
        /// <summary>
        /// Returns a Container with origin containers and material batches
        /// </summary>
        public Container GetWithOriginContainersAndBatches(Guid id)
        {
            Container container = GetAll()
                .Include(c => c.OriginContainers).ThenInclude(o => o.Origin).ThenInclude(o => o.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType)
                .Include(c => c.OriginContainers).ThenInclude(o => o.Origin).ThenInclude(o => o.MaterialContainers).ThenInclude(mc => mc.MaterialBatches).ThenInclude(m => m.MaterialBatch)
                .Where(x => x.Id == id)
                .FirstOrDefault();

            return container;
        }
        /// <summary>
        /// Returns a Container with accepted material types
        /// </summary>
        public Container GetWithAcceptedMaterialTypes(Guid id)
        {
            Container container = GetAll()
                .Include(c => c.OriginContainers).ThenInclude(o => o.Origin).ThenInclude(o => o.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType)
                .Include(c => c.OriginContainers).ThenInclude(o => o.Origin).ThenInclude(o => o.MaterialContainers)
                .Include(c => c.AcceptedMaterialType).ThenInclude(cm => cm.MaterialType)
                .Where(x => x.Id == id)
                .FirstOrDefault();

            return container;
        }

        /// <summary>
        /// Returns a Container with accepted material types and material container
        /// </summary>
        public Container GetWithAcceptedMaterialTypesAndMaterialsContainer(Guid id)
        {
            Container container = GetAll()
                .Include(c => c.AcceptedMaterialType).ThenInclude(cm => cm.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.Material).ThenInclude(m => m.MaterialType)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.MaterialBatches).ThenInclude(m => m.MaterialBatch)
                .Where(c => c.Id == id)
                .FirstOrDefault();

            return container;
        }

        /// <summary>
        /// Returns a Container with the area containers.
        /// </summary>
        public Container GetWithAreaContainers(Guid id)
        {
            Container container = GetAll()
                .Include(c => c.AreaContainers)
                .Where(x => x.Id == id)
                .FirstOrDefault();

            return container;
        }

        /// <summary>
        /// Returns an IQueryable of containers with relations used in Stock index.
        /// </summary>
        public IQueryable<Container> GetAllFullForStockIndexWithInconsistencies()
        {
            IQueryable<InconsistencyReport> inconsistencyReports = inconsistencyReportService.GetAll().Where(s => s.Status == InconsistencyReportStatusEnum.PendingReview);

            IQueryable<Container> containers = GetAll()
                .Include(c => c.AreaContainers)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.Material)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.CapacityUnit)
                .Include(c => c.DestinationInconsistencyReports)
                .Include(c => c.OriginInconsistencyReports)
                .Where(cc => inconsistencyReports.Any(ir => ir.DestinationContainerId == cc.Id
                || ir.OriginContainerId == cc.Id))
                .Distinct();

            return containers;
        }

        /// <summary>
        /// Returns an IQueryable of containers with relations used in Stock index.
        /// </summary>
        public IQueryable<Container> GetAllFullForStockIndex()
        {
            IQueryable<Container> containers = GetAll(useDefaultSorting: false)
                .Include(c => c.AreaContainers)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.Material)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.CapacityUnit)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.MaterialBatches).ThenInclude(m => m.MaterialBatch)
                .Include(c => c.AcceptedMaterialType).ThenInclude(m => m.MaterialType)
                .Include(c => c.AcceptedMaterialType).ThenInclude(m => m.CapacityUnit)
                .Include(c => c.DestinationInconsistencyReports)
                .Include(c => c.OriginInconsistencyReports)
                .Include(c => c.AcceptedMaterialType);

            return containers;
        }

        /// <summary>
        /// Returns a List<Container> of containers asynchronously filtered.
        /// </summary>
        public async Task<List<TContainer>> GetAllByTermAsync(string searchTerm)
        {
            string caseInsensitiveSearchTerm = $"%{searchTerm}%";

            return await GetAll()
                .Where(dn => EF.Functions.Like(dn.DetailedName, caseInsensitiveSearchTerm)
                || EF.Functions.Like(dn.Name, caseInsensitiveSearchTerm))
                .ToListAsync();
        }

        ///<summary>
        /// Returns  an IQueryable of containers filtered by area
        /// <summary>
        public IQueryable<Container> GetAllByArea(AreaEnum area)
        {
            IQueryable<Container> containers = GetAll()
                .Include(c => c.HappeningContainers).ThenInclude(hc => hc.Happening).ThenInclude(h => h.HappeningType)
                .Include(c => c.OriginContainers).ThenInclude(hc => hc.Origin)
               .Include(c => c.AreaContainers)
               .Include(c => c.MaterialContainers)
               .Include(c => c.AcceptedMaterialType).ThenInclude(c => c.MaterialType)
               .Where(c => c.AreaContainers.Any(a => a.AreaEnum == area));
            return containers;
        }

        public IQueryable<Container> GetAllByAreaWithMaterials(AreaEnum area, bool addMaterials = true, bool asNoTracking = false)
        {
            if (addMaterials)
                return GetAll(asNoTracking)
                    .Include(c => c.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
                    .Include(c => c.AcceptedMaterialType).ThenInclude(c => c.MaterialType)
                    .Where(c => c.AreaContainers.Any(a => a.AreaEnum == area));

            return GetAll(asNoTracking).Where(c => c.AreaContainers.Any(a => a.AreaEnum == area));
        }

        ///<summary>
        /// Returns  an IQueryable of containers filtered by areas
        /// <summary>
        public IQueryable<Container> GetAllByAreas(List<AreaEnum> areas)
        {
            return GetAll(true)
               .Include(c => c.HappeningContainers).ThenInclude(hc => hc.Happening).ThenInclude(h => h.HappeningType)
               .Include(c => c.HappeningContainers).ThenInclude(hc => hc.Happening).ThenInclude(h => h.NotifiedUsers)
               .Where(c => c.AreaContainers.Any(a => areas.Contains(a.AreaEnum)));
        }

        /// <summary>
        /// Returns a List<SelectListItem> of containers.
        /// </summary>
        public List<SelectListItem> GetAllAsListForArea(AreaEnum? area)
        {
            if (area != null)
                return GetAll().Include(c => c.AreaContainers)
                .Where(c => c.AreaContainers.Any(ac => ac.AreaEnum == area))
                .OrderBy(c => c.DetailedName)
                .Select(c => new SelectListItem(c.DetailedName, c.Id.ToString()))
                .ToList();
            return GetAll()
                .OrderBy(c => c.DetailedName)
                .Select(c => new SelectListItem(c.DetailedName, c.Id.ToString()))
                .ToList();
        }

        /// <summary>
        /// Returns a List<SelectListItem> of materialTypes.
        /// </summary>
        public List<SelectListItem> GetAllMaterialTypesAsListForArea(AreaEnum? area)
        {
            if (area != null)
            {
                IQueryable<Guid> currentContainers = GetAll().Include(c => c.AreaContainers)
                    .Include(m => m.MaterialContainers).ThenInclude(m => m.Material)
                    .Where(c => c.AreaContainers.Any(ac => ac.AreaEnum == area))
                    .SelectMany(x => x.MaterialContainers.Select(m => m.Material.MaterialTypeId))
                    .Distinct();

                IQueryable<MaterialType> materialTypes = this.materialTypeService.GetAll();

                List<SelectListItem> typesFilter = (from mt in materialTypes
                                                    join cc in currentContainers
                                                    on mt.Id equals cc
                                                    where mt.Path.StartsWith(mt.Path)
                                                    select new SelectListItem(mt.DetailedName, mt.Path)).ToList();
                return typesFilter;
            }
            else
                return this.materialTypeService.GetAll().Select(mt => new SelectListItem(mt.Name, mt.Path)).ToList();
        }

        public IQueryable<Guid> GetAllMaterialsForArea(AreaEnum? area)
        {
            IQueryable<Guid> currentContainers = GetAll()
                .Where(c => c.AreaContainers.Any(ac => ac.AreaEnum == area))
                .SelectMany(x => x.MaterialContainers.Select(m => m.Material.Id))
                .Distinct();
            return currentContainers;
        }

        /// <summary>
        /// Returns a List<SelectListItem> of containers.
        /// </summary>
        public List<SelectListItem> GetAllWithMaterialAsList()
        {
            return GetAll().Where(x => x.MaterialContainers.Any()).Select(c => new SelectListItem(c.DetailedName, c.Id.ToString()))
                .ToList();
        }

        public class TupleComparer : IEqualityComparer<Tuple<string, string>>
        {
            public bool Equals(Tuple<string, string> x, Tuple<string, string> y)
            {
                return x.Item1 == y.Item1 && x.Item2 == y.Item2;
            }

            public int GetHashCode(Tuple<string, string> item)
            {
                int hashText = item.Item1 == null ? 0 : item.Item1.GetHashCode();
                int hashValue = item.Item2 == null ? 0 : item.Item2.GetHashCode();
                return hashText ^ hashValue;
            }
        }

        /// <summary>
        /// Returns a List<SelectListItem> of containers.
        /// </summary>
        public IEnumerable<(string containerType, string containerTypeName)> GetContainersTypes(AreaEnum? area = null)
        {
            return GetAll()
                .Where(c => !area.HasValue || c.AreaContainers.Any(ac => ac.AreaEnum == area))
                .Select(c => new { c.ContainerType, c.ContainerTypeName })
                .AsEnumerable()
                .Distinct()
                .Select(c => (containerType: c.ContainerType, containerTypeName: c.ContainerTypeName));
        }

        /// <summary>
        /// Returns a IQueryable<stockIndexDTO> of containers filtered for index.
        /// </summary>
        public IQueryable<StockIndexDTO> GetAllFullFilteredIndex(Dictionary<string, string> filters = null)
        {
            IQueryable<Container> containers = GetAll(useDefaultSorting: false);

            string area = filters["area"];
            if (!string.IsNullOrEmpty(area))
                containers = containers.Where(x => x.AreaContainers.Any(x => x.AreaEnum == (AreaEnum)Enum.Parse(typeof(AreaEnum), area)));

            if (!string.IsNullOrEmpty(filters["status"]))
            {
                containers = filters["status"] == "Active"
                    ? containers.Where(hb => hb.Active)
                    : filters["status"] == "Inactive" ?
                    containers.Where(hb => !hb.Active) :
                    containers;
            }
            else
                containers = containers.Where(hb => hb.Active);

            if (!string.IsNullOrEmpty(filters["material"]))
            {
                Guid materialId = new Guid(filters["material"]);
                containers = containers.Where(c => c.MaterialContainers.Any(mc => mc.MaterialId == materialId && mc.Quantity > 0));
            }

            IQueryable<StockIndexDTO> stockIndexDTO = containers.Select(container => new StockIndexDTO
            {
                Id = container.Id,
                Company = container.Company.BusinessName,
                Farm = container.Farm.Name,
                ContainerName = container.DetailedName,
                ContainerType = container.ContainerType,
                InconsistencyReport = container.OriginInconsistencyReports.Any(ir => ir.Status == InconsistencyReportStatusEnum.PendingReview) || container.DestinationInconsistencyReports.Any(ir => ir.Status == InconsistencyReportStatusEnum.PendingReview),
                IsActiveFlag = container.Active,
                AcceptMaterialsWithActionEnumStoreFlag = container.AcceptedMaterialType.Any(m => m.ActionEnum == ActionsEnum.Store),
                HasMaterialsFlag = container.MaterialContainers.Any(mc => mc.Quantity > 0),
            });

            return stockIndexDTO;
        }


        /// <summary>
        /// Apply filters and returns all the containers with theirs relationships.
        /// </summary>
        public IQueryable<Container> GetAllFullFiltered(string area = null, Dictionary<string, string> filters = null)
        {
            if (!string.IsNullOrEmpty(area) && filters != null)
            {
                IQueryable<Container> containers = GetAllFullForStockIndex();
                containers = containers.Where(x => x.AreaContainers.Any(x => x.AreaEnum == (AreaEnum)Enum.Parse(typeof(AreaEnum), area)));

                if (!string.IsNullOrEmpty(filters["inconsistency"]))
                {
                    if (filters["inconsistency"] == this.localizer[Lang.WithInconsistency])
                    {
                        IQueryable<InconsistencyReport> inconsistencyReports = inconsistencyReportService.GetAll().Where(s => s.Status == InconsistencyReportStatusEnum.PendingReview);
                        containers = containers.Where(cc => inconsistencyReports.Any(ir => ir.DestinationContainerId == cc.Id || ir.OriginContainerId == cc.Id)).Distinct();
                    }
                    else if (filters["inconsistency"] == this.localizer[Lang.WithoutInconsistency])
                    {
                        IQueryable<InconsistencyReport> inconsistencyReports = inconsistencyReportService.GetAll().Where(s => s.Status == InconsistencyReportStatusEnum.PendingReview);
                        IQueryable<Container> containersWithInconsistencies = containers.Where(cc => inconsistencyReports.Any(ir => ir.DestinationContainerId == cc.Id || ir.OriginContainerId == cc.Id)).Distinct();
                        containers = containers.Except(containersWithInconsistencies);
                    }
                }
                if (!string.IsNullOrEmpty(filters["container"]))
                    containers = containers.Where(cc => cc.ContainerType == filters["container"]);

                if (!string.IsNullOrEmpty(filters["material"]))
                    containers = containers.Where(c => c.MaterialContainers.Any(mc => mc.MaterialId.ToString() == filters["material"]));

                if (!string.IsNullOrEmpty(filters["status"]))
                {
                    containers = filters["status"] == "Active"
                        ? containers.Where(hb => hb.Active)
                        : filters["status"] == "Inactive" ?
                        containers.Where(hb => !hb.Active) :
                        containers;
                }
                else
                    containers = containers.Where(hb => hb.Active);

                return containers;
            }
            else if (!string.IsNullOrEmpty(area))
            {
                IQueryable<Container> containers = GetAllFullForStockIndex();
                containers = containers.Where(x => x.AreaContainers.Any(x => x.AreaEnum == (AreaEnum)Enum.Parse(typeof(AreaEnum), area)) && x.Active);

                return containers;
            }
            else if (filters != null)
            {
                IQueryable<Container> containers = GetAllFullForStockIndex();
                if (!string.IsNullOrEmpty(filters["areas"]))
                {
                    containers = containers.Where(cc => cc.AreaContainers.Any(ac => ac.AreaEnum == (AreaEnum)Convert.ToInt32(filters["areas"]))
                    || cc.AreaContainers.Any(ac => ac.AreaEnum == (AreaEnum)Convert.ToInt32(filters["areas"])));
                }

                if (!string.IsNullOrEmpty(filters["inconsistency"]))
                {
                    if (filters["inconsistency"] == this.localizer[Lang.WithInconsistency])
                    {
                        IQueryable<InconsistencyReport> inconsistencyReports = inconsistencyReportService.GetAll().Where(i => i.Status == InconsistencyReportStatusEnum.PendingReview);
                        containers = containers.Where(cc => inconsistencyReports.Any(ir => ir.DestinationContainerId == cc.Id || ir.OriginContainerId == cc.Id)).Distinct();
                    }
                    else if (filters["inconsistency"] == this.localizer[Lang.WithoutInconsistency])
                    {
                        IQueryable<InconsistencyReport> inconsistencyReports = inconsistencyReportService.GetAll().Where(i => i.Status == InconsistencyReportStatusEnum.PendingReview);
                        containers = containers.Where(cc => !inconsistencyReports.Any(ir => ir.DestinationContainerId == cc.Id || ir.OriginContainerId == cc.Id)).Distinct();
                    }
                }
                if (!string.IsNullOrEmpty(filters["container"]))
                    containers = containers.Where(cc => cc.ContainerType == filters["container"]);
                if (!string.IsNullOrEmpty(filters["material"]))
                    containers = containers.Where(c => c.MaterialContainers.Any(mc => mc.MaterialId.ToString() == filters["material"]));
                if (!string.IsNullOrEmpty(filters["materialType"]))
                    containers = containers.Where(c => c.AcceptedMaterialType.Any(mtc => mtc.MaterialTypeId.ToString() == filters["materialType"]));
                if (!string.IsNullOrEmpty(filters["status"]))
                {
                    containers = filters["status"] == "Active"
                        ? containers.Where(hb => hb.Active)
                        : filters["status"] == "Inactive" ?
                        containers.Where(hb => !hb.Active) :
                        containers;
                }
                else
                    containers = containers.Where(hb => hb.Active);

                return containers;
            }
            return null;
        }

        public bool CheckInconsistency(Container container)
        {
            IQueryable<InconsistencyReport> inconsistencyReports = inconsistencyReportService.GetAll();
            if (container.DestinationShippingNotes.Any(sn => inconsistencyReports.Any(ir => ir.OriginShippingNoteId == sn.Id && ir.ResultShippingNoteId == null))
                || container.OriginShippingNotes.Any(sn => inconsistencyReports.Any(ir => ir.OriginShippingNoteId == sn.Id && ir.ResultShippingNoteId == null)))
                return true;
            else
                return false;
        }

        public List<SelectListItem> GetMaterialsOptions(AreaEnum? area)
        {
            IQueryable<Container> containers = GetAllFullForStockIndex();

            if (area.HasValue)
                containers = containers.Where(x => x.AreaContainers.Any(x => x.AreaEnum == area.Value));

            return containers.SelectMany(c => c.MaterialContainers)
                .Select(mc => new SelectListItem(mc.Material.Name, mc.Material.InternalId))
                .Distinct()
                .ToList();
        }

        public List<SelectListItem> GetMaterialsByTypeOptions(AreaEnum? area, Guid? materialTypeId)
        {
            IQueryable<Container> containers = GetAllFullForStockIndex();

            if (area.HasValue)
                containers = containers.Where(x => x.AreaContainers.Any(x => x.AreaEnum == area.Value));

            if (materialTypeId.HasValue)
                containers = containers.Where(x => x.MaterialContainers.Any(x => x.Material.MaterialTypeId == materialTypeId.Value));

            return containers.SelectMany(c => c.MaterialContainers)
                .Select(mc => new SelectListItem(mc.Material.Name, mc.Material.InternalId))
                .Distinct()
                .ToList();
        }

        /// <summary>
        /// Update files throw the Container Service comparing the container files currently on the db and the in memory container files.
        /// </summary>
        public async Task UpdateFiles(Guid containerId, List<TenantDependentEntityFile> newFiles)
        {
            List<TenantDependentEntityFile> dbFiles = this.fileService.GetAll().Where(f => f.TenantDependentEntityId == containerId).AsNoTracking().ToList();

            // Create any features that exist in newFeatures but not in dbFeatures.
            foreach (TenantDependentEntityFile file in newFiles.Except(dbFiles, new EntityComparer<TenantDependentEntityFile>()))
                await fileService.CreateAsync(file);

            // Update any features that exist both in newFeatures and in dbFeatures
            foreach (TenantDependentEntityFile file in newFiles.Intersect(dbFiles, new EntityComparer<TenantDependentEntityFile>()))
                await fileService.UpdateAsync(file);

            // Remove any features that exist in dbFeatures but not in newFeatures.
            foreach (TenantDependentEntityFile file in dbFiles.Except(newFiles, new EntityComparer<TenantDependentEntityFile>()))
                await fileService.DeleteAsync(file);
        }

        public async override Task DeleteAsync(TContainer entity)
        {
            if (entity.Files != null && entity.Files.Any())
            {
                foreach (TenantDependentEntityFile file in entity.Files)
                    await fileService.DeleteAsync(file);
            }

            await base.DeleteAsync(entity);
        }

        /// <summary>
        /// Returns all accepted material types for a particual container.
        /// </summary>
        public IQueryable<MaterialType> GetAcceptedMaterialTypes(Guid id, bool verifyByHenBatch = false)
        {
            IQueryable<Container> container = GetAll(true).Where(c => c.Id == id);

            if (verifyByHenBatch && container.Any(c => ContainerTypes.HenBatch == c.ContainerType))
                return container.SelectMany(c => c.AcceptedMaterialType)
                    .Select(amt => amt.MaterialType)
                    .Where(mt => mt.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAve));

            return container.SelectMany(c => c.AcceptedMaterialType).Select(amt => amt.MaterialType);
        }

        /// <summary>
        /// Returns an IQueryable<TContainer> representing all the container records accessible to this user's definitions for company, facility and sector.
        /// </summary>
        /// <param name="asNoTracking">If set to true, the entities returned by the query will not be tracked by the dbContext</param>
        /// <param name="useDefaultSorting">If set to true, the query will be sorted by Name (desc). Explicitly set it to false if another order is preferred</param>
        public override IQueryable<TContainer> GetAll(bool asNoTracking = false, bool useDefaultSorting = true)
        {
            try
            {
                return base.GetAll(asNoTracking, useDefaultSorting).Where(CanAccessByClaim());
            }
            catch (Exception ex)
            {
                throw exceptionManager.Handle(ex);
            }
        }

        /// <summary>
        /// Returns containers with free space for indicated material types.
        /// </summary>
        public IQueryable<TContainer> GetAllWithAvailableSpace(bool asNoTracking = false, bool useDefaultSorting = true, params string[] materialTypePaths)
        {
            return GetAll(asNoTracking, useDefaultSorting)
                .Where(c => c.AcceptedMaterialType.Any(amt => materialTypePaths.Contains(amt.MaterialType.Path) && amt.ActionEnum == ActionsEnum.Store && amt.CapacityStandarizedValue == 0)
                || (c.MaterialContainers.Where(mc => materialTypePaths.Contains(mc.Material.MaterialType.Path)).Sum(mc => (decimal?)mc.Quantity) ?? 0)
                    < (c.AcceptedMaterialType.Where(amt => materialTypePaths.Contains(amt.MaterialType.Path) && amt.ActionEnum == ActionsEnum.Store).Sum(amt => (decimal?)amt.CapacityStandarizedValue) ?? 0));
        }


        /// <summary>
        /// Returns an expression (for Linq operations) that validates if the current user can access the provided entity according to their companies, sites and sectors.
        /// Retrieves the user's tenants from the current operation context.
        /// Eg: `GetAll().Where(CanAccessByTenant());`
        /// </summary>
        public Expression<Func<TContainer, bool>> CanAccessByClaim()
        {
            List<Guid> userSectorIds = this.operationContext.GetUserSectorIds();
            List<Guid> userSiteIds = this.operationContext.GetUserSiteIds();
            List<Guid> userCompanyIds = this.operationContext.GetUserCompanyIds();

            Guid? userTenantId = this.operationContext.GetUserTenantId();

            return e => (e.TenantId == userTenantId)
            && (!e.CompanyId.HasValue || userCompanyIds == null || !userCompanyIds.Any() || userCompanyIds.Contains(e.CompanyId.Value))
            && (!e.FarmId.HasValue || userSiteIds == null || !userSiteIds.Any() || userSiteIds.Contains(e.FarmId.Value))
            && (!e.SectorId.HasValue || userSectorIds == null || !userSectorIds.Any() || userSectorIds.Contains(e.SectorId.Value));
        }

        /// <summary>
        /// Returns materials that a container has available. They can be filtered by material type.
        /// </summary>
        public IQueryable<MaterialContainer> GetAvailableMaterials(Guid id, string materialPath)
        {
            return GetAll(false).Where(c => c.Id == id)
                .SelectMany(c => c.MaterialContainers)
                .Include(mc => mc.Container)
                .Include(mc => mc.Material)
                .Where(mc => mc.Quantity > 0 && (string.IsNullOrEmpty(materialPath) || mc.Material.MaterialType.Path.StartsWith(materialPath)));
        }

        /// <summary>
        /// Returns all material batches in a container. Can be filtered by material.
        /// </summary>
        public IQueryable<MaterialBatchContainer> GetAvailableMaterialBatches(Guid id, Guid? materialId = null)
        {
            return GetAllIgnoringClaims(false).Where(c => c.Id == id)
                .SelectMany(c => c.MaterialContainers)
                .Include(c => c.MaterialBatches).ThenInclude(mb => mb.MaterialBatch)
                .Include(c => c.MaterialBatches).ThenInclude(mb => mb.MaterialContainer)
                .SelectMany(mb => mb.MaterialBatches.Where(mb => mb.Quantity > 0 && (!materialId.HasValue || mb.MaterialBatch.MaterialId == materialId)));
        }

        /// <summary>
        /// Returns containers that contain the alias for the indicated namer
        /// </summary>
        public IQueryable<TContainer> GetByAlias(Guid namerId, string alias)
        {
            Alias aliasEntity = aliasExtensionService.GetAll(true)
               .Where(a => a.NamerEntityId == namerId && a.Name.ToUpper() == alias.ToUpper())
               .FirstOrDefault();

            string name = aliasEntity.NamedEntityType;
            Guid id = aliasEntity.NamedEntityId;


            if (id == default || name != "Silo")
                throw new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]);

            return GetAll(true).Where(c => c.Id == id);
        }

        /// <summary>
        /// Asynchronously retrieves a container by code.
        /// </summary>
        public virtual async Task<TContainer> GetByCodeAsync(string code)
        {
            TContainer entity = await GetAll().FirstOrDefaultAsync(c => c.Code.ToUpper() == code.ToUpper());

            if (entity == null)
                throw new NotFoundException(localizer[Lang.GetFullNotFoundEx]);

            return entity;
        }

        /// <summary>
        /// Returns all containers with their acepted material types, filter by farm
        /// </summary>
        public IQueryable<Container> GetAllFilterByFarm(Guid farmId)
        {
            return base.GetAll()
                .Where(c => c.FarmId == farmId)
                .Include(c => c.AcceptedMaterialType).ThenInclude(c => c.CapacityUnit);
        }

        /// <summary>
        /// Returns all containers with their relationships, filter by farm and add its origins
        /// </summary>
        public IQueryable<Container> GetAllForFarmStructure(Guid farmId)
        {
            return base.GetAllIgnoringClaims()
                .Where(c => c.Active && (c.FarmId == farmId || c.OriginContainers.Any(oc => oc.Origin.FarmId == farmId)))
                .Include(c => c.AcceptedMaterialType).ThenInclude(c => c.CapacityUnit)
                .Include(c => c.AcceptedMaterialType).ThenInclude(c => c.MaterialType)
                .Include(c => c.OriginContainers).ThenInclude(oc => oc.Origin)
                .Include(c => c.MaterialContainers).ThenInclude(m => m.Material).ThenInclude(m => m.MaterialType)
                .Include(c => (c as HenWarehouse).Cluster)
                .Include(c => (c as Line).HenBatches)
                .Include(c => c.AreaContainers);
        }

        public EntityTypeEnum? ParseEntityTypeEnum(string containerType)
        {
            switch (containerType)
            {
                case ContainerTypes.ClassificationWarehouse:
                    return EntityTypeEnum.ClassificationWarehouse;
                case ContainerTypes.Farm:
                    return EntityTypeEnum.Farm;
                case ContainerTypes.DispositionWarehouse:
                    return EntityTypeEnum.DispositionWarehouse;
                case ContainerTypes.FeedFactory:
                    return EntityTypeEnum.FeedFactory;
                case ContainerTypes.HenBatch:
                    return EntityTypeEnum.HenBatch;
                case ContainerTypes.HenWarehouse:
                    return EntityTypeEnum.HenWarehouse;
                case ContainerTypes.Line:
                    return EntityTypeEnum.Line;
                case ContainerTypes.PackingWarehouse:
                    return EntityTypeEnum.PackingWarehouse;
                case ContainerTypes.PreparationWarehouse:
                    return EntityTypeEnum.PreparationWarehouse;
                case ContainerTypes.ReceptionWarehouse:
                    return EntityTypeEnum.ReceptionWarehouse;
                case ContainerTypes.Silo:
                    return EntityTypeEnum.Silo;
                case ContainerTypes.Slaughterhouse:
                    return EntityTypeEnum.Slaughterhouse;
                case ContainerTypes.StorageWarehouse:
                    return EntityTypeEnum.StorageWarehouse;
                case ContainerTypes.Vehicle:
                    return EntityTypeEnum.Vehicle;
                default:
                    return null;
            }
        }
    }
}