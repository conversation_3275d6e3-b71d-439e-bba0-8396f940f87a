﻿let boxes = [];

localStorage.removeItem("items-selected"); // clean local storage 

const tableContainer = $(`#planner-tables-container`);

disableButton('#export');

$(() => {
    // window load

    $(`#btn-save-planner`).on('click', (self) => {
        saveGADTableChanges();
    });

    $(`#btn-filter-planner`).on('click', async (self) => {
        disableButton('#btn-filter-planner');

        const warehouseId = $(`#warehouse`).val();
        const parentHenBatchId = $(`#parent-henbatch-selector`).val();
        const henbatchText = $(`#parent-henbatch-selector option:selected`).text();
        let distribuitionId = $(`#parent-henbatch-selector`).val()
        const henstage = $(`#hen-stage`).val();

        let lines = await getLinesByWarehouse(henstage, warehouseId, parentHenBatchId);

        if(lines?.length) {
            loadLinesResults(lines);
        }
        else {
            lines = [{ value: distribuitionId, text: henbatchText }]
        }

        $(`#planner-tables-container`).empty(); // Limpa o container antes de recriar as tabelas

        let tableIdx = 0;
        disableButton('#export'); // Desabilita o botão de exportação

        await generateLinesTables(
            {
                type: 'male',
                amountName: 'amountMale',
                lines,
                plannerModel,
                henstage,
                tableConfigurations
            },
            tableIdx
        );

        enableButton('#btn-filter-planner');
    });


    $(`#parent-henbatch-selector`).on('change', (self) => {
        changeParentHenBatch(self.target.value, 'male');
    });

    $(`#warehouse`).on('change', (self) => {
        changeWarehouse(self.target, 'male');
    });

    $('#export').click(function () {
        exportEvent();
    });

    $(`#planner-tables-container`).css({
        display: 'flex', // Usar flexbox para alinhar as tabelas lado a lado
    });
});


const layingColumns = [
    // { data: 'id', readOnly: true },
    // { data: 'week', readOnly: true },
    { data: 'amountMale', readOnly: true },
    { data: 'averageWeight.standard', readOnly: true },
    { data: 'averageWeight.real', readOnly: true },
    { data: 'averageWeight.weight', readOnly: true },
    { data: 'gad.standard', readOnly: true },
    { data: 'gad.program', editor: 'numeric' },
    { data: 'gad.real', readOnly: true },
    { data: 'cv', readOnly: true },
    { data: 'uniformityPercentage', readOnly: true },
    { data: 'henRelationship.real', readOnly: true },
    { data: 'henRelationship.standard', readOnly: true }, // [ ] TODO. verificar se é necessário
];

const breedingColumns = [
        // { data: 'id', readOnly: true },
        // { data: 'week', readOnly: true },
        { data: 'amountMale', readOnly: true },
        { data: 'averageWeight.standard', readOnly: true },
        { data: 'averageWeight.real', readOnly: true },
        { data: 'averageWeight.weight', readOnly: true },
        { data: 'gad.standard', readOnly: true },
        { data: 'gad.program', editor: 'numeric' },
        { data: 'gad.real', readOnly: true },
        { data: 'cv', readOnly: true },
        { data: 'uniformityPercentage', readOnly: true },
        { data: 'henRelationship.real', readOnly: true },
        { data: 'henRelationship.standard', readOnly: true }, // [ ] TODO. verificar se é necessário
];

const tableConfigurations = getTableConfigurations({ breedingColumns, layingColumns });

let handtableinstance = getInitialTable();