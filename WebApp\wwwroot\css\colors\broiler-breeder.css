/*
Template Name: Admin Pro Admin
Author: Wrappixel
Email: <EMAIL>
File: scss
*/
/*
Template Name: Admin Pro Admin
Author: Wrappixel
Email: <EMAIL>
File: scss
*/
/*Theme Colors*/
/*bootstrap Color*/
/*Light colors*/
/*Normal Color*/
/*Extra Variable*/
/*******************
/*Top bar
*******************/
.topbar {
  background: #29313e;
}
.topbar .top-navbar .navbar-header .navbar-brand .dark-logo {
  display: none;
}
.topbar .top-navbar .navbar-header .navbar-brand .light-logo {
  display: inline-block;
  color: rgba(255, 255, 255, 0.8);
}
.topbar .navbar-nav .nav-item > a.nav-link {
  color: rgba(41, 49, 62, 0.8);
  font-weight: bold;
}
.topbar .navbar-nav .nav-item > a.nav-link:hover,
.topbar .navbar-nav .nav-item > a.nav-link:focus {
  color: rgba(41, 49, 62, 1) !important;
}
.topbar .top-navbar .navbar-nav > .nav-item > span {
  color: black;
}
.topbar .navbar-header {
  background: #272c33;
}

.logo-center .topbar .navbar-header {
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.logo-center .topbar .top-navbar .navbar-header .navbar-brand .dark-logo {
  display: none;
}

.logo-center .topbar .top-navbar .navbar-header .navbar-brand .light-logo {
  display: inline-block;
  color: rgba(255, 255, 255, 0.8);
}

.hdr-nav-bar .navbar .navbar-nav > li.active > a {
  border-color: #29313e;
}

/*******************
/*General Elements
*******************/
.lstick {
  background: #29313e;
}

a {
  color: #787f91;
}

a.link:hover,
a.link:focus {
  color: #29313e !important;
}

.bg-theme {
  background-color: #29313e !important;
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  background-color: #29313e;
  border-color: #29313e;
}

.right-sidebar .rpanel-title {
  background: #29313e;
}

.stylish-table tbody tr:hover,
.stylish-table tbody tr.active {
  border-left: 4px solid #29313e;
}

.text-themecolor {
  color: #29313e !important;
}

.profile-tab li a.nav-link.active,
.customtab li a.nav-link.active {
  border-bottom: 2px solid #29313e;
  color: #29313e;
}

.profile-tab li a.nav-link:hover,
.customtab li a.nav-link:hover {
  color: #29313e;
}

/*******************
/*Buttons
*******************/
.btn-themecolor,
.btn-themecolor.disabled {
  background: #29313e;
  color: #ffffff;
  border: 1px solid #29313e;
}
.btn-themecolor:hover,
.btn-themecolor.disabled:hover {
  background: #29313e;
  opacity: 0.7;
  border: 1px solid #29313e;
}
.btn-themecolor.active,
.btn-themecolor:focus,
.btn-themecolor.disabled.active,
.btn-themecolor.disabled:focus {
  background: #5fc557;
}

/*******************
/*sidebar navigation
*******************/
.card-no-border .left-sidebar,
.card-no-border .sidebar-nav {
  background: #242a33;
}

.mini-sidebar .sidebar-nav {
  background: transparent;
}

@media (min-width: 768px) {
  .mini-sidebar .sidebar-nav .sidebarnav > li > ul {
    background: #1c2128;
  }
}

.sidebar-nav {
  background: #242a33;
}

.user-profile .profile-text a {
  color: #687384 !important;
}

.card-no-border .sidebar-footer {
  background: #1c2128;
}

.label-themecolor {
  background: #29313e;
}

.sidebar-nav > ul > li.active > a {
  color: #ffffff;
  border-color: #29313e;
}
.sidebar-nav > ul > li.active > a i {
  color: #ffffff;
}

.sidebar-nav ul li a.active,
.sidebar-nav ul li a:hover {
  color: #ffffff;
}
.sidebar-nav ul li a.active i,
.sidebar-nav ul li a:hover i {
  color: #ffffff;
}

.sidebar-nav ul li.nav-small-cap {
  color: #687384;
}

@media (min-width: 768px) {
  .mini-sidebar .sidebar-nav .sidebarnav > li:hover > a {
    background: #1c2128;
  }
}
