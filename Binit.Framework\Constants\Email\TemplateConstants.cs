﻿namespace Binit.Framework.Constants.Email
{
    public static class TemplateConstants
    {
        public enum TemplateEnum
        {
            Welcome,
            ForgotPassword,
            PasswordRecovery,
            Notification,
            TaskNotification,
            AlertForLaying,
            AlertForMortality,
            HappeningNotification,
            InconsistencyReport,
            AlertForHenReport
        }

        /// <summary>
        /// Retrieves the Assembly path to the Email Template.
        /// </summary>
        public static string GetTemplateAssemblyPath(this TemplateEnum templateEnum)
        {
            string response = "Binit.Framework.Helpers.Email.Views.";
            switch (templateEnum)
            {
                case TemplateEnum.Welcome:
                    response += "Welcome.cshtml";
                    break;
                case TemplateEnum.ForgotPassword:
                    response += "ForgotPassword.cshtml";
                    break;
                case TemplateEnum.PasswordRecovery:
                    response += "PasswordRecovery.cshtml";
                    break;
                case TemplateEnum.Notification:
                    response += "Notification.cshtml";
                    break;
                case TemplateEnum.TaskNotification:
                    response += "TaskNotification.cshtml";
                    break;
                case TemplateEnum.InconsistencyReport:
                    response += "InconsistencyReport.cshtml";
                    break;
                case TemplateEnum.AlertForLaying:
                    response += "AlertForLaying.cshtml";
                    break;
                case TemplateEnum.AlertForMortality:
                    response += "AlertForMortality.cshtml";
                    break;
                case TemplateEnum.HappeningNotification:
                    response += "HappeningNotification.cshtml";
                    break;
                case TemplateEnum.AlertForHenReport:
                    response += "AlertForHenReport.cshtml";
                    break;
                default:
                    response = null;
                    break;
            }
            return response;
        }
    }
}
