﻿using Binit.Framework.Helpers.Excel;
using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs.GAD.Planner;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Domain.Logic.Interfaces.PlannerBusinessLogic
{
    /// <summary>
    /// Planner GAD Logic
    /// </summary>
    public interface IGADPlannerBusinessLogic
    {
        /// <summary>
        /// Get GAD by female
        /// </summary>
        /// <param name="henbatchId"></param>
        /// <param name="henStage"></param>
        /// <returns></returns>
        List<GADPlannerFemaleDTO> GetGADFemale(Guid henbatchId, Range range);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="henbatchId"></param>
        /// <param name="henStage"></param>
        /// <returns></returns>
        public Task<ExportResult> GetGADMaleReportAsync(Guid henbatchId, Guid warehouseId, HenStage henStage);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="henbatchId"></param>
        /// <param name="henStage"></param>
        /// <returns></returns>
        public Task<ExportResult> GetGADFemaleReportAsync(Guid henbatchId, Guid warehouseId, HenStage henStage);

        /// <summary>
        /// Get GAD by male
        /// </summary>
        /// <param name="henbatchId"></param>
        /// <param name="henStage"></param>
        /// <returns></returns>
        List<GADPlannerMaleDTO> GetGADMale(Guid henbatchId, Range range);

        /// <summary>
        /// Save programs selected for henbatch
        /// </summary>
        /// <param name="values"></param>
        /// <returns></returns>
        Task SaveProgram(string plannerName, Guid henbatchId, IEnumerable<ReportPlannerProgram> values);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="henbatchId"></param>
        /// <param name="henStage"></param>
        /// <param name="plannerName"></param>
        /// <returns></returns>
        Task<(decimal, decimal)> GetAverageGAD(Guid henbatchId, HenStage henStage, string plannerName);
    }
}
