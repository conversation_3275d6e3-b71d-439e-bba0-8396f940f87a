using Binit.Framework;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Interfaces.DAL;
using DAL.Interfaces;
using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.Interfaces;
using Domain.Logic.Validations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.EggWeightReportBusinessLogic;

namespace Domain.Logic.BusinessLogic
{
    public class EggWeightReportBusinessLogic : IEggWeightReportBusinessLogic
    {
        private readonly IHenBatchService henBatchService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IEggWeightReportService eggWeightReportService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IServiceTenantDependent<EggWeightMeasurement> eggWeightMeasurementService;
        private readonly IServiceTenantDependent<SampleCage> sampleCageService;
        private readonly IUnitOfWork unitOfWork;


        public EggWeightReportBusinessLogic(
            IHenBatchService henBatchService,
            IStringLocalizer<SharedResources> localizer,
            IEggWeightReportService eggWeightReportService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IServiceTenantDependent<EggWeightMeasurement> eggWeightMeasurementService,
            IServiceTenantDependent<SampleCage> sampleCageService,
            IUnitOfWork unitOfWork)
        {
            this.henBatchService = henBatchService;
            this.localizer = localizer;
            this.eggWeightReportService = eggWeightReportService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.eggWeightMeasurementService = eggWeightMeasurementService;
            this.sampleCageService = sampleCageService;
            this.unitOfWork = unitOfWork;
        }
        /// <summary>
        /// Returns all egg weight report includes some  relationships.
        /// </summary>
        public IQueryable<EggWeightReportDTO> GetAll()
        {
            return eggWeightReportService.GetAll()
                .Include(e => e.Farm)
                .Include(e => e.HenBatch).ThenInclude(hb => hb.Line).ThenInclude(l => l.Warehouse).ThenInclude(w => w.Cluster).ThenInclude(c => c.Farm)
                .Select(e => new EggWeightReportDTO()
                {
                    Id = e.Id,
                    FarmCode = e.HenBatch.Line.Warehouse.Cluster.Farm.Code,
                    FarmName = e.Farm.Name,
                    HenBatch = e.HenBatch.Code,
                    HenWarehouse = e.HenBatch.Line.Warehouse.Code,
                    Line = e.HenBatch.Line.Code,
                    Date = e.Date,
                    AverageEggWeight = e.AvgEggWeight,
                });
        }



        /// <summary>
        /// creates warehouse reports and add them to the hen batch performance
        /// </summary>
        public async Task CreateReports(List<EggWeightReport> reports)
        {
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                foreach (EggWeightReport eggWeightReport in reports)
                    await Create(eggWeightReport);
            });
        }

        /// <summary>
        /// Creates an egg weight report from a sample cage report
        /// </summary>
        public async Task CreateEggWeightReportFromSampleCageReport(Guid henBatchId, DateTime date, decimal eggWeight, Guid? sampleCageId = null)
        {
            if (eggWeight <= 0)
                return;

            // Validate that the SampleCageId exists in the database
            if (sampleCageId.HasValue && sampleCageId.Value != Guid.Empty)
            {
                // Check if the SampleCage exists
                var sampleCageExists = sampleCageService.GetAll()
                    .Any(sc => sc.Id == sampleCageId.Value);

                if (!sampleCageExists)
                {
                    // Log the error and return without creating the report
                    //Console.WriteLine($"Error: SampleCage with ID {sampleCageId} does not exist in the database.");
                    return;
                }
            }

            var report = new EggWeightReport
            {
                Id = Guid.NewGuid(),
                HenBatchId = henBatchId,
                Date = date,
                AvgEggWeight = eggWeight / 1000, // Convert from g to kg
                EggWeightMeasurements = new List<EggWeightMeasurement>()
            };

            report.EggWeightMeasurements.Add(new EggWeightMeasurement
            {
                Id = Guid.NewGuid(),
                SampleCageId = sampleCageId ?? Guid.Empty,
                EggWeight = eggWeight / 1000,
                EggWeightCapacityUnitId = Guid.Parse("E6F01CB9-E6B4-4163-8972-000CCA13108F") // Grams
            });

            try
            {
                await Create(report);
            }
            catch (Exception ex)
            {
                //Console.WriteLine($"Error creating egg weight report: {ex.Message}");
                throw;
            }
        }


        /// <summary>
        /// actually creates the report and ad the values to the corresponding hen batch performance
        /// </summary>
        private async Task Create(EggWeightReport report)
        {
            DateTime lastReportDate = eggWeightReportService
                   .GetAll().Where(r => r.HenBatchId == report.HenBatchId)
                   .OrderByDescending(r => r.Date)
                   .Select(hr => hr.Date.Date)
                   .FirstOrDefault();

            if (lastReportDate != default && lastReportDate > report.Date)
                throw new ValidationException(null, new BusinessValidationResult<SampleCageReport>(localizer[Lang.LastReportEx]).UnsuccessfulValidations);

            int week = henBatchService.GetCurrentWeekNumberForDate(report.HenBatchId, report.Date);
            HenBatchPerformance performance = await henBatchPerformanceService.GetAll().FirstOrDefaultAsync(hbp => hbp.HenBatchId == report.HenBatchId && hbp.WeekNumber == week);
            if (performance is null)
                throw new ValidationException(null, new BusinessValidationResult<SampleCageReport>(localizer[Lang.HenBatchPerformanceEx]).UnsuccessfulValidations);

            report.HenBatchPerformanceId = performance.Id;
            List<EggWeightMeasurement> newMeasurement = report.EggWeightMeasurements;
            report.EggWeightMeasurements = null; // To prevent EF from creating them automatically.
            // set sector, farm and company id for filtering
            HenBatch henBatch = henBatchService.Get(report.HenBatchId);
            report.CompanyId = henBatch.CompanyId;
            report.FarmId = henBatch.FarmId;
            report.SectorId = henBatch.SectorId;
            // Create sample cage report.
            await eggWeightReportService.CreateAsync(report);

            // Set sample cage measurements
            if (newMeasurement != null && newMeasurement.Count > 0)
            {
                foreach (EggWeightMeasurement measurement in newMeasurement)
                {
                    measurement.EggWeightReportId = report.Id;
                    measurement.Id = Guid.NewGuid();
                    await eggWeightMeasurementService.CreateAsync(measurement);
                }
            }
            //Add avg egg weight to hen batch report.
            List<EggWeightReport> allReports = eggWeightReportService.GetAll().Where(scr => scr.HenBatchPerformanceId == performance.Id).ToList();
            performance.AvgWeightEggFromEWR = allReports.Any() ? allReports.Average(scr => scr.AvgEggWeight) : performance.AvgWeightEgg;

            // If avgEggsWeight has changed -> we go to the Database
            if (allReports.Any())
                await henBatchPerformanceService.UpdateAsync(performance);
        }

    }
}