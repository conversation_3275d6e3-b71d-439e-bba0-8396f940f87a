const GetWarehousesByHenBatch = (parentId, value) => {
    $(`#warehouse`).prop('disabled', true)
    $(`#warehouse`).empty() // Clean "henbatch-child" selector

    const henbatchText = $(`#parent-henbatch-selector option:selected`).text();
    if(henbatchText.split('|').length == 4) {
        return;
    }

    $.getJSON(`/planner/GetWarehousesByHenBatch?selectedHenBatch=${parentId}`, (res) => {


        if (res.length == 0) {
            $(`#henbatch-selected`).val(value)
            ChangeBtnSaveLabel("save")
            return;
        }

        $.each(res, function (i, l) {
            $(`#warehouse`).prop('disabled', false)
            $(`#warehouse`).append(new Option(l.text, l.value, false, false));

        });

        $(`#henbatch-selected`).val($(`#warehouse option:selected`).val())
        ChangeBtnSaveLabel("save-next")
    });
}

const getLinesByWarehouse = async (henstage, warehouseId, henBatchId) => {
    try {
        if (warehouseId) {
            const response = await fetch(
                `/planner/gad/GetLinesByWarehouse?henStage=${henstage}&warehouseId=${warehouseId}&henBatchId=${henBatchId}`
            );

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const text = await response.text(); // Get raw response text
            if (!text) {
                console.error("Empty response received from server.");
                return null; // Handle empty response
            }

            return JSON.parse(text); // Parse JSON if response is not empty
        }
    } catch (error) {
        console.error("Error fetching lines by warehouse:", error.message);
        return null; // Return null to indicate failure
    }
};

const GetTableDataByLine = async ({plannerModel, henbatchId, henstage}) => {
    // Fazer a requisição para obter os dados da tabela
    const res = await $.ajax({
        method: 'post',
        url: '/planner/GetTableDataByLine',
        contentType: 'application/json',
        data: JSON.stringify({
            model: plannerModel,
            henbatchId: henbatchId,
            henStage: henstage
        })
    });

    return res;
}

const postPlannerSaveChanges = async ({plannerModel, henbatchId, changes}) => {
    try {
        const response = await fetch('/planner/save-changes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                model: plannerModel,
                henbatchId: henbatchId,
                'elements-changed': changes
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Error saving changes');
        }

        return true;
    } catch (error) {
        console.error(error.message);
        return false;
    }
}

function GADTableAfterChange(changes, source) {
    let itemChange = changes[0];
    let items = localStorage.getItem("items-selected");
    let row = handtableinstance.getDataAtRow(itemChange[0]);
    let match = false;

    // const rowWeek = row[1];
    const rowWeek = row[0];

    const columnsEachBox = (row.length - 1) / boxes.length;
    const boxIdx = itemChange[1].split('.')[0];
    const selectedBox = boxes[boxIdx];

    const changedItem = { week: rowWeek, value: itemChange[3], selectedBox }

    if (items === null) {
        const newBoxChanged = {
            ...selectedBox,
            values: [{ week: rowWeek, value: itemChange[3] }]
        };

        localStorage.setItem('items-selected', JSON.stringify([newBoxChanged])); 
        return;
    }

    items = JSON.parse(localStorage.getItem("items-selected"));

    const newItems = updateResultFromChangedTableItem(items, changedItem);

    localStorage.setItem("items-selected", JSON.stringify(newItems)); 
}

function updateResultFromChangedTableItem(result, changedItem) {
    const { id, boxName } = changedItem.selectedBox;
    const { week, value } = changedItem;
  
    const existingBox = result.find(box => box.id === id);
  
    if (!existingBox) {
      result.push({
        id,
        boxName,
        values: [{ week, value }]
      });
    }
    else {
      const existingWeek = existingBox.values.findIndex(v => v.week === week);
      
      if (existingWeek !== -1) {
        existingBox.values[existingWeek] = { week, value };
      } else {
        existingBox.values.push({ week, value });
      }
    }
  
    return result;
  }

function changeParentHenBatch(value, type='') {
    if (!handtableinstance) {
        console.error("handtableinstance is not initialized yet.");
        return;
    }

    let parentId = value

    localStorage.removeItem("items-selected");
    clearTableData();
    ClearInfoViewModel(type)

    // Limpiar el selector de henbatch-child
    $(`#warehouse`).empty()

    GetWarehousesByHenBatch(parentId, value);
}

function changeWarehouse(target, type='') {
    localStorage.removeItem("items-selected");
    clearTableData();
    ClearInfoViewModel(type)

    $(`#henbatch-selected`).val(target.value)

    let optionIdx = target.selectedIndex;

    if (optionIdx >= (target.options.length - 1)) {
        ChangeBtnSaveLabel("save")
        return;
    }

    ChangeBtnSaveLabel("save-next")
}

function loadLinesResults(lines) {
    // Ordenar os resultados com base no texto (Aviário e Box)
    lines.sort((a, b) => {
        const partsA = a.text.split(" | ");
        const partsB = b.text.split(" | ");

        // Extrair os números do Aviário e Box
        const aviaryA = parseInt(partsA[2].match(/\d+/)[0]); // Extrai o número de "Aviário X"
        const aviaryB = parseInt(partsB[2].match(/\d+/)[0]);
        const boxA = parseInt(partsA[3].match(/\d+/)[0]); // Extrai o número de "Box Y"
        const boxB = parseInt(partsB[3].match(/\d+/)[0]);

        // Ordenar primeiro pelo Aviário, depois pelo Box
        if (aviaryA === aviaryB) {
            return boxA - boxB;
        }
        return aviaryA - aviaryB;
    });
}

function LoadAdditionalInfoViewModel(data, type='') {
    let obj = JSON.parse(data)

    $(`#farm-name`).val(obj.FarmName)
    $(`#henbatch-code`).val(obj.HenbatchCode)
    $(`#production-date`).val(obj.ProductionDate)
    $(`#line-number`).val(obj.LineNumber)
    if(type === 'female') $(`#line-code`).val(obj.LineCode)
    $(`#genetic-name`).val(obj.Genetic)
    $(`#AvgBirdWeight`).val(obj.AvgBirdWeight)
    $(`#AvgGAD`).val(obj.AvgGAD)
}

function ClearInfoViewModel(type='') {
    $(`#farm-name`).val("")
    $(`#henbatch-code`).val("")
    $(`#production-date`).val("")
    $(`#line-number`).val("")
    if(type === 'female') $(`#line-code`).val("")
    $(`#genetic-name`).val("")
    $(`#AvgBirdWeight`).val("")
    $(`#AvgGAD`).val("")
}

async function generateLinesTables(
    { 
        type,
        amountName,
        lines,
        plannerModel,
        henstage,
        tableConfigurations
    },
    tableIdx
) {

    localStorage.removeItem("items-selected");

    const auxnNestedHeaders = [
        [""], // row 1
        [""], // row 2
        ["Semana"]  // row 3
    ];

    let auxColumns = [{ data: 'week', readOnly: true }]; // Start with the "Semana" column
    const consolidatedData = [];

    const { nestedHeaders, columns } = tableConfigurations;
    let hasTable = false;
    boxes = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const distributionId = line.value;

        // Extrair "Aviário X - Box Y" de line.text
        const parts = line.text.split(" | "); // Divide a string pelo delimitador " | "
        const aviaryAndBox = `${parts[2]} - ${parts[3]}`; // Combina "Aviário X" e "Box Y"
    
        try {
            const res = await GetTableDataByLine({ plannerModel, henbatchId: distributionId, henstage });

            if (res?.data?.length > 0) {
                // Verificar se todos os itens têm "amountFemale" ou "amountMale" igual a 0
                const allAmountZero = res.data.every(item => item[amountName] === 0);
    
                if (allAmountZero) {
                    console.log(`Box table ${i + 1} will not be created because all values ​​of "amount" are 0.`);
                    continue; // Jump to the next Box
                }

                auxnNestedHeaders[0].push({ label: aviaryAndBox, colspan: nestedHeaders[0][0].colspan }); // Adiciona o rótulo para a nova coluna
                auxnNestedHeaders[1].push(...nestedHeaders[1]); // cabeçalhos nível 2
                auxnNestedHeaders[2].push(...nestedHeaders[2]); // Adiciona os cabeçalhos da tabela

                const prefix = tableIdx; // line.value;

                // Add columns for this Box
                auxColumns = [
                    ...auxColumns,
                    ...columns.map((column) => {
                        return { ...column, data: `${prefix}.${column.data}`, }
                    })
                ];

                // Populate data for each week
                res.data.forEach((row, rowIndex) => {
                    if (!row) {
                        console.warn(`Row data is undefined for tableIdx: ${tableIdx}`, row);
                        return;
                    }

                    // // Initialize the row if it doesn't exist
                    if (!consolidatedData[rowIndex]) {
                        // consolidatedData[rowIndex] = { }; // Add "Semana" column only once
                        consolidatedData[rowIndex] = { week: row.week }; // Add "Semana" column only once
                    }
                   
                    // Access properties dynamically and add data for this Box
                    columns.forEach((column) => {
                        const columnName = column.data;

                        if (columnName.includes('.')) {
                            const keys = columnName.split('.');
                            let value = row;
                            keys.forEach(key => {
                                value = value ? value[key] : undefined;
                            });
                            consolidatedData[rowIndex][`${prefix}.${columnName}`] = value;
                        } else {
                            consolidatedData[rowIndex][`${prefix}.${columnName}`] = row[columnName];
                        }
                    });
                });

                const { henBatch } = res.data[0];
                boxes[prefix] = { boxName: henBatch.split(' | ')[3], id: distributionId };

                hasTable = true;

            } else {
                console.log(`Box table ${i + 1} will not be created because there is no data.`);
                continue; // Jump to the next Box
            }

            if (tableIdx === 0) {
                LoadAdditionalInfoViewModel(res["additional-info"], type);
            }

        } catch (error) {
            console.error(`Error loading data for Line ${i + 1}:`, error);
        }

        tableIdx++;
    }

    // [ ] TODO. GAD Female Breeding: Corrigir para o lote 0724CA (tem vários Boxes, a requisição está dando sucesso, mas todos com soma zero, e não aparece nada no lugar da tabela)
    // mostrar mensagem de erro se não houver dados para a tabela

    if (hasTable) {
        enableButton('#export'); // Habilita o botão de exportação
    }
    else {
        // // Criar o HTML do cabeçalho e da tabela apenas se houver dados válidos
        const tableInfo = `
            <div id="table-message" style="text-align: center; color: red; font-size: 14px;">
                ${localizedStrings.noDataMessage}
            </div>`;

        $(`#planner-tables-container`).append(tableInfo); // Adiciona o container ao DOM
        return;
    }

    renderTable({consolidatedData, auxnNestedHeaders, auxColumns})

    if (tableIdx > 0) {
        enableButton('#export'); // Habilita o botão de exportação
    }
}

function getInitialTable() {
    return new Handsontable(tableContainer[0], {
        rowHeaders: false,
        colHeaders: true,
        nestedHeaders: tableConfigurations.nestedHeaders, // Use os novos nestedHeaders
        // nestedHeaders: nestedHeaders, // Use os novos nestedHeaders
        // hiddenColumns: {
        //     columns: [0], // Ocultar a coluna "id" (se necessário)
        //     indicators: false
        // },
        filters: false,
        dropdownMenu: false,
        autoColumnSize: true,
        manualColumnResize: true,
        width: '100%',
        height: 'auto',
        autoWrapRow: true,
        autoWrapCol: true,
        allowEmpty: true,
        data: [], // Dados serão carregados dinamicamente
        columns: tableConfigurations.columns,
        licenseKey: 'non-commercial-and-evaluation'
    });
}

function renderTable({consolidatedData, auxnNestedHeaders, auxColumns}) {
    // Render the table
    handtableinstance = new Handsontable(tableContainer[0], {
        data: consolidatedData,
        rowHeaders: false,
        colHeaders: true,
        nestedHeaders: auxnNestedHeaders,
        // nestedHeaders: plannerModel.columns, // Cabeçalhos originais
        // hiddenColumns: {
        //     columns: [0],
        //     // columns: (tableIdx === 0) ? [0] : [0, 1], // Ocultar apenas a coluna "Id" na primeira tabela; "Id" e "week" nas demais
        //     indicators: false
        // },
        columns: auxColumns,
        stretchH: 'none',
        autoColumnSize: true,
        manualColumnResize: true,
        width: '100%',
        height: 'auto',

        filters: false,
        dropdownMenu: false,
        autoWrapRow: true,
        autoWrapCol: true,
        allowEmpty: true,

        afterChange: (changes, source) => {
            if (changes !== null && source !== 'loadData') {
                GADTableAfterChange(changes, source)
            }
        },
        cells: function (row, col) {
            const cellProperties = {};

            if (col === 0) { // Índice da coluna "Semana"
                cellProperties.renderer = function (instance, td, row, col, prop, value, cellProperties) {
                    Handsontable.renderers.TextRenderer.apply(this, arguments);
                    td.style.backgroundColor = '#f0f0f0'; // Cor de fundo cinza
                    td.style.color = '#000'; // Cor do texto preta
                    td.style.textAlign = 'center'; // Centralizar o texto (opcional)
                };
            }
            return cellProperties;
        },
        afterGetColHeader: function (col, TH) {
            // Aplica cores alternadas apenas à primeira linha do cabeçalho
            const headerRow = TH.parentNode.rowIndex; // Índice da linha do cabeçalho
            if (headerRow === 0 && col > 0 && TH?.colSpan == auxnNestedHeaders[0][1].colspan && !TH?.hiddenHeader) { // Apenas para a primeira linha e ignorando a primeira coluna
                const colors = ['#ffff00', '#00b0f0', '#92d050']; // Amarelo, Azul, Verde

                const colorIndex = (col - 1) % colors.length; // Alterna entre as cores
                const color = colors[colorIndex];
        
                TH.style.backgroundColor = color; // Aplica a cor alternada
                TH.style.fontWeight = 'bold'; // Texto em negrito
                TH.style.color = '#000'; // Texto preto
                TH.style.textAlign = 'center'; // Centraliza o texto
            }
        },
        licenseKey: 'non-commercial-and-evaluation',
    });
}

function getTableConfigurations({ breedingColumns, layingColumns }) {
    const henstage = $(`#hen-stage`).val();

    let columns = (henstage === 'Breeding') ? breedingColumns : layingColumns;

    const nestedHeaders = getNestedColumns({
        columns: JSON.parse(JSON.stringify(plannerModel.columns)), // Clone to avoid modifying the original object
        colspan: columns.length,
    })

    return { columns, nestedHeaders };
}

function getNestedColumns({columns, colspan}) {
    const nestedHeaders = [
        [{ label: "", colspan }], // Aviário - Box
        ...columns
    ];

    const nColumnsRemoveBeginning = 2;

    // Remove os dois primeiros elementos
    nestedHeaders[1].splice(0, nColumnsRemoveBeginning)
    nestedHeaders[2].splice(0, nColumnsRemoveBeginning)
    return nestedHeaders;
}

const clearTableData = () => {
    if (handtableinstance) {
        // handtableinstance.loadData([]);

        handtableinstance.destroy(); // Destroys the Handsontable instance
        handtableinstance = null; // Clear the reference
    }

    handtableinstance = getInitialTable();

    removeEmptyTableMessage();
}

function removeEmptyTableMessage() {
    const div = document.getElementById('table-message');
    if(div) div.remove();
}

function disableButton(selector) {
    const element = $(selector);
    if (element.length) {
        const tagName = element.prop('tagName').toLowerCase();
        if (tagName === 'a') {
            element.addClass('disabled'); // Adiciona uma classe para estilizar links desativados
            element.attr('aria-disabled', 'true'); // Acessibilidade
        } else if (tagName === 'button') {
            element.prop('disabled', true); // Desativa o botão
        }
    }
}

function enableButton(selector) {
    const element = $(selector);
    if (element.length) {
        const tagName = element.prop('tagName').toLowerCase();
        if (tagName === 'a') {
            element.removeClass('disabled'); // Remove a classe de desabilitado para links
            element.removeAttr('aria-disabled'); // Remove o atributo de acessibilidade
        } else if (tagName === 'button') {
            element.prop('disabled', false); // Habilita o botão
        }
    }
}

function exportEvent() {
    disableButton('#export');

    const henbatchId = $('#parent-henbatch-selector').val();
    const warehouseId = $(`#warehouse`).val();
    const henstage = $('#hen-stage').val();

    setTimeout(() => {
        enableButton('#export')
    }, 2000); // Habilitar o botão após 2 segundos

    const downloadUrl = `${location.pathname}/ExcelExport?henbatchId=${henbatchId}&warehouseId=${warehouseId}&henstage=${henstage}`;

    window.location.href = downloadUrl;
}

function ChangeBtnSaveLabel(state) {
    const idBtnSave = `#btn-save-planner`;

    switch (state) {
        case "save-next":
            $(idBtnSave).text($(idBtnSave).data('nextLabel'))
            break;
        case "save":
            $(idBtnSave).text($(idBtnSave).data('saveLabel'))
            break;
    }
}

async function saveGADTableChanges() {
    const changesStr = localStorage.getItem('items-selected');
    const boxesChanges = JSON.parse(changesStr);

    const savePromises = boxesChanges.map(boxChanges => {
        const henbatchId = boxChanges.id;

        const values = boxChanges.values.map(item => ({
            ...item,
            value: (item.value === "") ? "0" : (typeof item.value === 'string') ? item.value.replace('.', ',') : item.value.toString().replace('.', ',')
        }));

        const changes = JSON.stringify(values);
        return postPlannerSaveChanges({plannerModel, henbatchId, changes});
    });

    // Aguarda todas as requisições serem concluídas
    const results = await Promise.allSettled(savePromises);

    // Verifica se todas as requisições foram bem-sucedidas
    const allSuccessful = results.every(result => result.status === 'fulfilled' && result.value === true);

    if (allSuccessful) {
        UpdateChildSelector();
        $(`#btn-filter-planner`).trigger('click');
    } else {
        console.error('Not all changes were saved successfully.');
    }
}

// salva as alterações do box, e seleciona a próxima distriuição (carrega a próxima tabela)
function UpdateChildSelector() {
    let selector = $(`#warehouse`)[0]
    let idxCurrentOption = selector.selectedIndex

    if (idxCurrentOption >= (selector.options.length - 1)) {
        location.reload(); // Recargar la página si es el último elemento
        return;
    }

    selector.options[idxCurrentOption + 1].selected = true;
    $(`#warehouse`).trigger('change')
}