@model WebApp.Models.AssignBestPracticeViewModel;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.BestPractice.CreateOrEdit
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer

@{
    int i = (int)ViewData["Index"];
    var relevances = ViewData["Relevances"] as List<SelectListItem>;
    var frequencyCheckList = ViewData["FrequencyCheckList"] as List<SelectListItem>;
    var notificationUsersList = ViewData["NotificationUsersList"] as List<SelectListItem>;
    var timePeriodTypes = ViewData["TimePeriodTypes"] as List<SelectListItem>;
    if (timePeriodTypes == null)
        timePeriodTypes = new List<SelectListItem>();
    var weekDays = ViewData["WeekDays"] as List<SelectListItem>;
    var taskTypeClass = "other-task";
    var imageURL = "/images/views/bestPractice/other.png";
    if (Model.BestPracticeTasks[i].TaskType == "HealthCare")
    {
        taskTypeClass = "health-care-task";
        imageURL = "/images/views/bestPractice/healthCare.png";
    }
    else if (Model.BestPracticeTasks[i].TaskType == "WellBeing")
    {
        taskTypeClass = "well-being-task";
        imageURL = "/images/views/bestPractice/wellBeing.png";
    }
    else if (Model.BestPracticeTasks[i].TaskType == "Maintenance")
    {
        taskTypeClass = "maintenance-task";
        imageURL = "/images/views/bestPractice/maintenance.png";
    }
}

    <div id="best-practice-task-@i" style="width:100%; display:none" class="best-practice-task m-b-15 m-r-30 m-l-30 m-t-10">
        <input type="hidden" asp-for="BestPracticeTasks[i].Id" />
        <input type="hidden" asp-for="BestPracticeTasks[i].Relevance" />
        <input type="hidden" asp-for="BestPracticeTasks[i].TaskType" />
        @*The following hidden inputs are not necessary information for task generation but are necessary if page needs to reload after validation*@
        <input type="hidden" asp-for="BestPracticeTasks[i].Name" />
        <input type="hidden" asp-for="BestPracticeTasks[i].Description" />
        <input type="hidden" asp-for="BestPracticeTasks[i].SummarizedTask" />
        <input type="hidden" asp-for="BestPracticeTasks[i].AlarmDate" />
        <input type="hidden" asp-for="BestPracticeTasks[i].ExpirationDate" />
        <div class="floating-labels" style="display:inline-flex; width: 100%">
            <div class="col-md-8 row">
                <div class="col-md-2 taskIcon @taskTypeClass" style="text-align:center; margin-bottom: auto; margin-top: auto;">
                    <img src="@imageURL" style="max-width:100%" />
                </div>
                <div class="col-md-10 p-l-20" style="margin-bottom: auto; margin-top: auto;">
                    <h3>@Model.BestPracticeTasks[i].Name</h3>
                    <h6>@Model.BestPracticeTasks[i].Description</h6>
                </div>
            </div>
            <div class="col-md-4" style="margin-top:15px">
                <ignite-input for-property="BestPracticeTasks[i].TaskTypeSymbol" type="text" disabled></ignite-input>
                <ignite-input for-property="BestPracticeTasks[i].RelevanceSymbol" type="text" disabled></ignite-input>
            </div>
        </div>
        <div class="p-l-20 p-r-20">
            @if (Model.EntityType == "Genetic" || Model.EntityType == "HenBatch")
            {
                <div class="row col-md-4" style="padding-left: 30px">
                    <h6 id="spanLabel-@i" class="floating-labels m-b-15">@localizer[Lang.LabelSpanHenBatch]</h6>
                </div>
            }
            else
            {
                <div class="row col-md-4" style="padding-left: 30px">
                    <h6 id="spanLabel-@i" class="floating-labels m-b-15">@localizer[Lang.LabelSpan]</h6>
                </div>
            }
            <div id="spanContainer-@i" class="col-md-12 floating-labels align-content-center" style="display:inline-flex;padding-left:0px">
                <div class="col-md-4">
                    <ignite-input for-property="BestPracticeTasks[i].SpanValue" type="number"></ignite-input>
                </div>
                <div class="col-md-4">
                    <ignite-dropdown for-property="BestPracticeTasks[i].SpanTimePeriodType"
                                     items="@timePeriodTypes">
                    </ignite-dropdown>
                </div>
            </div>
            <div id="spanContainer-@i" class="col-md-12 floating-labels align-content-center" style="display:inline-flex;padding-left:0px">
                <div id="weekContainer-@i" class="m-b-10" style="display:@(Model.BestPracticeTasks[i].SpanTimePeriodType == "Month" ? "" : "none")">
                    @for (int wd = 0; wd < weekDays.Count; wd++)
                    {
                        <a value="@weekDays[wd].Value" class="btn weekDay-@i btn-outline-inverse @(Model.BestPracticeTasks[i].WeekDay.ToString() == weekDays[wd].Value ? "btn-themecolor" : "")">
                            @weekDays[wd].Text
                        </a>
                    }
                </div>
                <input type="hidden" asp-for="BestPracticeTasks[i].WeekDay" />
            </div>
        </div>
        <div class="floating-labels" style="display:inline-flex; width: 100%;padding-left:15px;padding-right:25px">
            <div class="col-md-4">
                <h6 id="spanLabel-@i" class="floating-labels m-b-15">@localizer[Lang.DurationLabel]</h6>
                <ignite-input for-property="BestPracticeTasks[i].Duration" type="number"></ignite-input>
            </div>
            <div class="col-md-4">
                <h6 id="spanLabel-@i" class="floating-labels m-b-15">@localizer[Lang.AlertSpanValueLabel]</h6>
                <ignite-input for-property="BestPracticeTasks[i].AlertSpanValue" type="number"></ignite-input>
            </div>
        </div>
        <div style="width:96%; margin:auto">
            <div id="frequenciesContainer-@i" class="frequenciesContainer">
                @{
                    int iF = 0;
                    foreach (var f in Model.BestPracticeTasks[i].Frequencies)
                    {
                        @await Component.InvokeAsync("AssignFrequencyRow", new { indexTask = i, index = iF, model = Model, timePeriodTypes = timePeriodTypes, weekDays = weekDays });
                        iF++;
                    }

                }
            </div>
        </div>

        <div class="notifyByEmail" style="margin-left:1rem;">
            <ignite-checkbox for-property="BestPracticeTasks[i].NotifyByEmail" onchange="handleNotifyByEmailChange(@i)"></ignite-checkbox>
        </div>
        <div class="row pl-3" id="infoEmailNotification">
            <div class="col-5 email-input-@i p-0" hidden="@(!Model.BestPracticeTasks[i].NotifyByEmail)">
                <div class="btn-group col-md-5 pr-0 btn-alarmDate" aria-label="date" role="group" style="margin-top:17px">
                    <button type="button" id="btn-alarmDate-@i" class="btn @(Model.BestPracticeTasks[i].AlarmDate ? "btn-themecolor" : "btn-outline-themecolor") btn-date" style="@(!Model.BestPracticeTasks[i].AlarmDate ? "border-color:#29313ea1":"")" onclick="changeColorAlarm(@i);">@localizer[Lang.BtnAlarmLabel]</button>
                </div>
                <div class="btn-group col-md-5 pl-0 btn-expirationDate" aria-label="date" role="group" style="margin-top:17px">
                    <button type="button" id="btn-expirationDate-@i" class="btn @(Model.BestPracticeTasks[i].ExpirationDate ? "btn-themecolor" : "btn-outline-themecolor") btn-date" style="@(!Model.BestPracticeTasks[i].ExpirationDate ? "border-color:#29313ea1":"")" onclick="changeColorExpiration(@i);">@localizer[Lang.BtnExpirationLabel]</button>
                </div>
            </div>

            <div class="col-4 email-input-@i" hidden="@(!Model.BestPracticeTasks[i].NotifyByEmail)">
                <ignite-dropdown for-property="BestPracticeTasks[i].FrequencyCheck"
                                 items="@frequencyCheckList"
                                 placeholder="@localizer[Lang.PlaceholderEmailNotificationFrequency]">
                </ignite-dropdown>
            </div>

            <div class="floating-labels" style="display:inline-flex; width: 100%;padding-left:15px;padding-right:25px">
                <div class="col-md-4 email-input-@i" hidden="@(!Model.BestPracticeTasks[i].NotifyByEmail)">
                    <h6 id="spanLabel-@i" class="floating-labels m-b-15">@localizer[Lang.NotificationAmountLabel]</h6>
                    <ignite-input for-property="BestPracticeTasks[i].NotificationAmount" type="number"></ignite-input>
                </div>

                @if (notificationUsersList != null)
                {
                    <div class="col-md-4 email-input-@i" hidden="@(!Model.BestPracticeTasks[i].NotifyByEmail)">
                        <h6 id="spanLabel-@i" class="floating-labels m-b-15">@localizer[Lang.NotificationUsersLabel]</h6>
                        <ignite-dropdown for-property="BestPracticeTasks[i].NotificationUsers" items="@notificationUsersList" multiple="true"></ignite-dropdown>
                    </div>
                }
            </div>
        </div>

        <div class="m-b-10" style="width:100%; text-align:center; align-items:center">
            <a name="@i" class="btn btn-green reduceTask m-r-10"
               style="vertical-align:middle; color:white">
                <i class="fas fa-chevron-up" style="font-size:20px"></i>
                @localizer[Lang.ReduceTaskLabel]
            </a>
            <button type="button" onclick="OpenCurrentModal(@i)" class="btn btn-themecolor" style="justify-content:right"><i class='fa fa-file'></i>@localizer[Lang.FileButton]</button>
        </div>
    </div>
<div id="best-practice-task-@i-reduced" name="@i" style="width:100%; " class="m-b-15 m-t-10 m-r-30 m-l-30 best-practice-task-reduced">
    <div class="p-10 row showTask">
        <div class="col-md-1 taskIcon @taskTypeClass">
            <img src="@imageURL" style="max-width:100%" />
        </div>
        <div id="taskName-@i" class="col-md-8" style="margin:auto">
            @Model.BestPracticeTasks[i].SummarizedTask
        </div>
        <div class="d-flex justify-content-end col-md-2" style="margin:auto">
            <a style="vertical-align:middle">
                <i class="fas fa-chevron-down" style="font-size:30px"></i>
            </a>
        </div>
    </div>
</div>
