﻿using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Domain.Logic.Interfaces
{
    public interface IHenWarehouseService : IContainerService<HenWarehouse>
    {
        /// <summary>
        /// Creates a new henwarehouse with auto-generated name and number
        /// </summary>
        Task CreateHenWarehouseAsync(HenWarehouse entity);

        /// <summary>
        /// Returns all warehouses with its relationships.
        /// </summary>
        IQueryable<HenWarehouse> GetAllFull(Guid? clusterId = null, HenStage? henStage = null);

        /// <summary>
        /// Returns a warehouse by Id with its relationships.
        /// </summary>
        new HenWarehouse GetFull(Guid id);

        /// <summary>
        /// Returns asynchronously a warehouse by Id with its relationships.
        /// </summary>
        Task<HenWarehouse> GetFullAsync(Guid id, bool asNoTracking = false);

        /// <summary>
        /// Returns all hen Warehouses flitered by farm and cluster.
        /// </summary>
        IQueryable<HenWarehouse> GetAllFullFiltered(Dictionary<string, string> filters);

        /// <summary>
        /// Returns all warehouses that have lines with no active hen batches.
        /// </summary>
        IQueryable<HenWarehouse> GetWithAvailableLines(HenStage? henStage = null);

        /// <summary>
        /// Work around for updating HenWarehouses from the Edit View.
        /// </summary>
        Task UpdateFromViewModelAsync(HenWarehouse entity);

        /// <summary>
        /// Returns unused space by lines
        /// </summary>
        Task<decimal> GetHenWarehouseAvailableSpace(Guid id);

        /// <summary>
        /// Return an Enum DTO with the data for the capacityAlert
        /// </summary>
        IEnumerable<HenWarehouseDTO> GetAll(HenStage henStage);


        /// <summary>
        /// Return henWarehouse with AcceptedMaterialType relashionship
        /// </summary>
        IQueryable<HenWarehouse> GetAllWithAcceptedMaterialType();

        IQueryable<HenWarehouse> GetAllByHenBatch(Guid henBatchId);

        HenWarehouse GetByHenBatchId(Guid henBatchId);


    }
}