﻿@model WebApp.Models.HenReportViewModel;
@using Binit.Framework;
@using Microsoft.Extensions.Localization;
@using Binit.Framework.Constants.SeedEntities;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.HenReport.CreateOrEdit;
@inject IStringLocalizer<SharedResources> localizer
@{
    bool editable = (bool)ViewData["Editable"];
}
<div class="form-group m-b-20" binit-validation-for="EggsValidationErrors" binit-onerror-class="has-danger"
    binit-onsuccess-class="has-success">
    <span asp-validation-for="EggsValidationErrors" class="form-control-feedback"></span>
</div>

@if (Model.Eggs.Any(e =>
e.MaterialTypePath.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubablePreclasificado)))
{
    <div>
        <h5 class="card-title">@localizer[Lang.TitleHatchableEggs]</h5>
        <br />
        <div style="margin:2rem">
            <table id="hatchableEggs-details" class="table w-100 details">
                <thead>
                    <tr>
                        <th width="350px">@(localizer[Lang.TableMaterialName])</th>
                        <th width="50px">@(localizer[Lang.TableQuantity])</th>
                    </tr>
                </thead>
                <tbody>
                    @{
                        foreach (var material in Model.Eggs.Where(e =>
                        e.MaterialTypePath.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubablePreclasificado)))
                        {
                            @await Component.InvokeAsync("HenReportEggsTable", new { index = Model.Eggs.IndexOf(material), model =
                            Model, eggType = "hatchableEggs", editable = editable })
                                ;
                    }
                }
            </tbody>
        </table>
    </div>

    <div class="row" style="margin:2rem">
        <div class="col-3" style="margin-left:43rem;">
            <ignite-input for-property="TotalHatchableEggs" disabled></ignite-input>
        </div>
    </div>

</div>
}

@if (Model.Eggs.Any(e =>
e.MaterialTypePath.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercialPreclasificado)))
{
    <div>
        <h5 class="card-title">@localizer[Lang.TitleCommercialEggs]</h5>
        <br />

        <div style="margin:2rem">
            <table id="commercialEggs-details" class="table w-100 details">
                <thead>
                    <tr>
                        <th width="350px">@(localizer[Lang.TableMaterialName])</th>
                        <th width="50px">@(localizer[Lang.TableQuantity])</th>
                    </tr>
                </thead>
                <tbody>
                    @{
                        foreach (var material in Model.Eggs.Where(e =>
                        e.MaterialTypePath.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercialPreclasificado)
                        && e.EggId != "7B1BC3D9-B32B-4A42-45D4-08D8FABC0A06"
                        && !e.Egg.Contains("Eliminado/Quebrado na GM") && !e.Egg.Contains("huevos rotos")))
                        {
                            @await Component.InvokeAsync("HenReportEggsTable", new { index = Model.Eggs.IndexOf(material), model =
                            Model, eggType = "commercialEggs", editable = editable })
                                ;

                    }
                }
            </tbody>
        </table>
    </div>

    <div class="row" style="margin:2rem">
        <div class="col-3" style="margin-left:43rem;">
            <ignite-input for-property="TotalCommercialEggs" disabled></ignite-input>
        </div>
    </div>
</div>
}

@if (Model.Eggs.Any())
{
    <div>
        <h5 class="card-title">@localizer[Lang.TitleBrokenEggs]</h5>
        <br />
        <div style="margin:2rem">
            <table id="brokenEggs-details" class="table w-100 details">
                <thead>
                    <tr>
                        <th width="350px">@(localizer[Lang.TableMaterialName])</th>
                        <th width="50px">@(localizer[Lang.TableQuantity])</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="vertical-align:middle">
                            @localizer[Lang.BrokenEggsLabel]
                        </td>

                        <td style="vertical-align:middle">
                            @if (editable)
                            {
                                <ignite-input for-property="BrokenEggs"></ignite-input>
                            }
                            else
                            {
                                <ignite-input for-property="BrokenEggs" disabled></ignite-input>
                            }
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
}
