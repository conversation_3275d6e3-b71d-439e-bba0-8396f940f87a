$(document).ready(function () {
  // Check if a farm is already selected or if there are exactly 2 options (default + 1 farm)
  if ($("#FarmId").val() || $("#FarmId").get(0).options.length == 2) {
    // If there are exactly 2 options (default + 1 farm), select the farm
    if ($("#FarmId").get(0).options.length == 2 && !$("#FarmId").val()) {
      const farmOption = $("#FarmId").get(0).options[1];
      $("#FarmId").val(farmOption.value);
    }

    // Now load the active hen batches for the selected farm
    loadActiveHenBatches();
  }

  // set date to today
  $("#Date").val(
    new Date().toISOString().split("T")[0].split("-").reverse().join("/")
  );
  setTimeout(() => {
    $("#Date").focus();
    setTimeout(() => {
      $("#Date").blur();
    }, 250);
  }, 250);

  $("#FarmId").on("change", async function () {
    loadActiveHenBatches();
  });

  // Add event handlers for sort buttons if they exist
  $(".sort-button").on("click", function () {
    const sortBy = $(this).data("sort");
    updateSort(sortBy);
  });

  $("#HenBatchId").on("change", async function () {
    await initTable();
  });

  $("#Date").on("blur", async function () {
    await initTable();
  });

  $("#createBreedingWeightReportButton").on("click", async () => {
    await createReport();
  });

  // Add event handler for the apply averages button
  $(document).on("click", "#applyAveragesButton", async function () {
    await applyAveragesAndShowBoxes(currentWarehouseData);
  });
});

let table = null;
let rows = [];
let currentSort = null;
let allWarehousesData = []; // Store all warehouses data
let warehousesMap = new Map(); // Map to store data grouped by warehouse
let processedWarehouses = new Set(); // Track which warehouses have been processed
let warehouseQueue = []; // Queue of warehouses to process
let currentWarehouseId = null; // Current warehouse being processed
let currentWarehouseData = null; // Data for the current warehouse
let lastEditedWarehouseId = null; // Track the last warehouse being edited before showing all warehouses
let batchAverages = {
  weightF: 0,
  cvF: 0,
  unifF: 0,
  weightM: 0,
  cvM: 0,
  unifM: 0,
}; // Store batch-level averages
let batchAveragesEntered = false; // Flag to track if batch averages have been entered

//=======  HELPERS

function resetSaveButton() {
  $("#createBreedingWeightReportButton").prop("disabled", false).html("Salvar");
}

function showWarning(msg) {
  return swal.fire({ type: "warning", title: "Atenção", html: msg });
}

async function handleResponse(resp) {
  if (resp.ok) {
    if (resp.status === 204) return;
    const text = await resp.text();
    const isJson = resp.headers
      .get("content-type")
      ?.includes("application/json");
    const data = isJson ? JSON.parse(text) : text;
    if (data?.error) await showWarning(data.error);
    else if (data?.Message) await showWarning(data.Message);
    else if (data?.message) await showWarning(data.message);
    return data;
  }

  const text = await resp.text();
  let msg;
  try {
    const parsed = JSON.parse(text);
    if (parsed.errors) msg = Object.values(parsed.errors).join("<br/>");
    else if (parsed.Message) msg = parsed.Message;
    else if (parsed.message) msg = parsed.message;
    else msg = text;
  } catch {
    msg = text;
  }
  throw new Error(msg);
}

function clearReportTable() {
  destroyTable();
  $("#report-table").empty();
  rows = [];
}

async function loadActiveHenBatches() {
  clearReportTable();

  const farmId = $("#FarmId").val();

  if (!farmId) {
    return;
  }

  const activeHenBatches = await getActiveHenBatchesByFarm(farmId);

  if (activeHenBatches.length === 0) {
    await showWarning("Nenhum lote com performance para essa fazenda.");
    return;
  }

  const $henBatchSelect = $("#HenBatchId");
  $henBatchSelect.prop("disabled", false).empty();
  $henBatchSelect.append($("<option>").val("").text("Selecione um lote"));
  activeHenBatches.forEach((hb) =>
    $henBatchSelect.append($("<option>").val(hb.id).text(hb.code))
  );

  if (activeHenBatches.length === 1) {
    // Use setTimeout to ensure the option is added to the select before trying to select it
    setTimeout(() => {
      $henBatchSelect.val(activeHenBatches[0].id);
      $henBatchSelect.trigger("change");
      $henBatchSelect.prop("disabled", true);
    }, 100);
  }
}

async function initTable() {
  // Clear all UI elements and event handlers
  clearAllUI();

  // Reset data structures
  allWarehousesData = [];
  warehousesMap.clear();
  processedWarehouses.clear();
  currentWarehouseId = null;
  currentWarehouseData = null;
  batchAveragesEntered = false;
  batchAverages = {
    weightF: 0,
    cvF: 0,
    unifF: 0,
    weightM: 0,
    cvM: 0,
    unifM: 0,
  };

  const henBatchId = $("#HenBatchId").val();
  const dateVal = $("#Date").val();
  if (!henBatchId || !dateVal) {
    $("#table-header").empty();
    return;
  }

  // Apply the current sort if it exists
  if (currentSort) {
    $(".sort-button").removeClass("active");
    $(`.sort-button[data-sort="${currentSort}"]`).addClass("active");
  }

  try {
    await handleResponse(
      await fetch(
        `/SampleCageReport/RevalidateDate?henBatchId=${henBatchId}&date=${encodeURIComponent(
          dateVal
        )}`
      )
    );
  } catch (e) {
    await showWarning(e.message);
    return;
  }

  let resp;
  try {
    resp = await fetchJSON(
      `/SampleCageReport/GetWarehousesWithLinesByBatch?henBatchId=${henBatchId}&date=${encodeURIComponent(
        dateVal
      )}`
    );
  } catch (e) {
    await showWarning(e.message);
    return;
  }

  const warehouses = resp.warehouses || [];

  if (warehouses.length === 0) {
    await showWarning("Não foram encontrados aviários para este lote.");
    return;
  }

  // Group data by warehouse
  warehouses.forEach((hw) => {
    const warehouseData = {
      id: hw.warehouseId,
      name: hw.name,
      boxes: [],
    };

    hw.lines.forEach((line) => {
      const boxData = {
        aviary: hw.name,
        warehouseId: hw.warehouseId,
        box: line.name,
        sampleCageId: line.sampleCageId,
        henBatchId: line.henBatchId,
        weightF: null,
        cvF: null,
        unifF: null,
        weightM: null,
        cvM: null,
        unifM: null,
      };

      warehouseData.boxes.push(boxData);
      allWarehousesData.push(boxData);
    });

    warehousesMap.set(hw.warehouseId, warehouseData);
  });

  const farmName = $("#FarmId option:selected").text();
  const batchName = $("#HenBatchId option:selected").text();
  $("#table-header").html(`
    <strong>Produtor:</strong> ${farmName}
    &nbsp;&nbsp;<strong>Lote:</strong> ${batchName}
    &nbsp;&nbsp;<strong>Data:</strong> ${dateVal}
  `);

  // Create a queue of warehouses to process
  warehouseQueue = Array.from(warehousesMap.values());

  // Sort the warehouse queue if needed (e.g., by name)
  warehouseQueue.sort((a, b) => a.name.localeCompare(b.name));

  // Check if there are existing reports for this date and batch
  try {
    const existingData = await fetchJSON(
      `/SampleCageReport/GetExistingSampleCageReports?henBatchId=${henBatchId}&date=${encodeURIComponent(
        dateVal
      )}`
    );

    if (existingData && existingData.batchAverages) {
      // Load batch averages
      batchAverages = {
        weightF: existingData.batchAverages.weightF || 0,
        cvF: existingData.batchAverages.cvF || 0,
        unifF: existingData.batchAverages.unifF || 0,
        eggWeight: existingData.batchAverages.eggWeight || 0,
        weightM: existingData.batchAverages.weightM || 0,
        cvM: existingData.batchAverages.cvM || 0,
        unifM: existingData.batchAverages.unifM || 0,
      };

      batchAveragesEntered = true;

      // Load processed warehouses from the response
      if (
        existingData.processedWarehouseIds &&
        existingData.processedWarehouseIds.length > 0
      ) {
        existingData.processedWarehouseIds.forEach((warehouseId) => {
          if (warehouseId && warehousesMap.has(warehouseId)) {
            processedWarehouses.add(warehouseId);
          }
        });
      }

      if (
        existingData.warehouseReports &&
        existingData.warehouseReports.length > 0
      ) {
        // Create a map of existing boxes by warehouseId and box name for more reliable matching
        const existingBoxesMap = new Map();

        existingData.warehouseReports.forEach((warehouseReport) => {
          if (warehouseReport && warehouseReport.boxes) {
            // Store warehouse averages by warehouseId
            if (warehouseReport.warehouseId) {
              // Store boxes by a composite key of warehouseId + box name for more reliable matching
              warehouseReport.boxes.forEach((box) => {
                if (box) {
                  const key = `${warehouseReport.warehouseId}_${box.box}`;
                  existingBoxesMap.set(key, {
                    ...box,
                    warehouseId: warehouseReport.warehouseId,
                    averages: warehouseReport.averages,
                  });

                  // Also store by sampleCageId as a fallback
                  if (box.sampleCageId) {
                    existingBoxesMap.set(box.sampleCageId, {
                      ...box,
                      warehouseId: warehouseReport.warehouseId,
                      averages: warehouseReport.averages,
                    });
                  }
                }
              });
            }
          }
        });

        // Apply existing data to warehouses
        warehousesMap.forEach((warehouseData) => {
          let hasExistingData = false;

          // Apply data to each box
          warehouseData.boxes.forEach((box) => {
            // Try to find existing box by composite key first
            const compositeKey = `${warehouseData.id}_${box.box}`;
            let existingBox = existingBoxesMap.get(compositeKey);

            // Fall back to sampleCageId if composite key doesn't match
            if (!existingBox && box.sampleCageId) {
              existingBox = existingBoxesMap.get(box.sampleCageId);
            }

            if (existingBox) {
              // This box has existing data
              hasExistingData = true;

              // Apply existing values
              box.weightF = existingBox.weightF || null;
              box.cvF = existingBox.cvF || null;
              box.unifF = existingBox.unifF || null;
              box.eggWeight = existingBox.eggWeight || null;
              box.weightM = existingBox.weightM || null;
              box.cvM = existingBox.cvM || null;
              box.unifM = existingBox.unifM || null;

              // Store the report ID for later updates
              box.reportId = existingBox.reportId || null;

              // Initialize as not modified - will only be set to true if values change
              box.isModified = false;
              box.hasExistingData = true;
            } else {
              // No existing data for this box
              box.reportId = null;
              box.isModified = false;
              box.hasExistingData = false;
            }
          });

          // Mark warehouse as processed if it has existing data
          if (hasExistingData) {
            processedWarehouses.add(warehouseData.id);
          }
        });

        // Check if there are unprocessed warehouses
        const remainingWarehouses = warehouseQueue.filter(
          (w) => !processedWarehouses.has(w.id)
        );

        if (remainingWarehouses.length > 0) {
          // Show a notification about existing data
          swal.fire({
            type: "info",
            title: "Dados Existentes Carregados",
            html: `Foram encontrados dados para ${processedWarehouses.size} de ${warehousesMap.size} aviários.<br>Você pode continuar inserindo dados para os aviários restantes.`,
            timer: 3000,
            showConfirmButton: false,
          });

          // Continue with the next warehouse
          processNextWarehouse();
        } else {
          // If all warehouses are processed, show all warehouses
          showAllWarehouses();
        }
        return;
      }
    }
  } catch (error) {}

  // Start with batch average input
  showBatchAverageInput();
}

// Function to show the batch average input form
function showBatchAverageInput() {
  // Clear any existing UI
  $("#batch-average-container").remove();
  $("#warehouse-progress-container").remove();
  $("#average-input-container").remove();
  $("#warehouse-boxes-container").remove();

  // Create a container for the batch average input
  $("#report-table").before(`
    <div id="batch-average-container" class="mb-4">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Médias do Lote</h5>
        </div>
        <div class="card-body">
          <p class="font-bold">Insira as médias para o lote completo:</p>
          <div id="batch-average-table"></div>
          <div class="d-flex justify-content-end mt-3">
            <button id="applyBatchAveragesButton" class="btn btn-primary">Continuar para Aviários</button>
          </div>
        </div>
      </div>
    </div>
  `);

  // Create a single row for batch average input
  rows = [
    {
      batch: $("#HenBatchId option:selected").text(),
      weightF: batchAverages.weightF || null,
      cvF: batchAverages.cvF || null,
      unifF: batchAverages.unifF || null,
      weightM: batchAverages.weightM || null,
      cvM: batchAverages.cvM || null,
      unifM: batchAverages.unifM || null,
    },
  ];

  // Build the table for batch average input
  table = new Handsontable(document.getElementById("batch-average-table"), {
    data: rows,
    rowHeaders: false,
    stretchH: "all",
    licenseKey: "non-commercial-and-evaluation",
    nestedHeaders: [
      [
        { label: "", colspan: 1 },
        { label: "FÊMEAS", colspan: 3 },
        { label: "MACHOS", colspan: 3 },
      ],
      ["Lote", "Peso", "CV", "Uniformidade", "Peso", "CV", "Uniformidade"],
    ],
    columns: [
      { data: "batch", readOnly: true },
      { data: "weightF", type: "numeric" },
      { data: "cvF", type: "numeric" },
      { data: "unifF", type: "numeric" },
      { data: "weightM", type: "numeric" },
      { data: "cvM", type: "numeric" },
      { data: "unifM", type: "numeric" },
    ],
    minRows: 0,
    minSpareRows: 0,
    height: "auto",
    afterChange: (_changes, source) => {
      if (source === "edit") {
        table.render();
      }
    },
  });

  $(document).off("click", "#applyBatchAveragesButton");
  $(document).on("click", "#applyBatchAveragesButton", async function () {
    await applyBatchAverages();
  });
}

// Function to apply batch averages and proceed to warehouse averages
async function applyBatchAverages() {
  // Get the average values from the input table
  const data = table.getSourceData();
  if (!data || !data.length) {
    await showWarning("Não foi possível obter os dados da tabela.");
    return;
  }

  // Get the batch average values
  batchAverages = {
    weightF: parseFloat(data[0].weightF) || 0,
    cvF: parseFloat(data[0].cvF) || 0,
    unifF: parseFloat(data[0].unifF) || 0,
    weightM: parseFloat(data[0].weightM) || 0,
    cvM: parseFloat(data[0].cvM) || 0,
    unifM: parseFloat(data[0].unifM) || 0,
  };

  // Validate that at least some values were entered
  if (
    batchAverages.weightF === 0 &&
    batchAverages.cvF === 0 &&
    batchAverages.unifF === 0 &&
    batchAverages.weightM === 0 &&
    batchAverages.cvM === 0 &&
    batchAverages.unifM === 0
  ) {
    await showWarning(
      "Por favor, insira pelo menos um valor médio para o lote."
    );
    return;
  }

  // Mark batch averages as entered
  batchAveragesEntered = true;

  $("#batch-average-container").remove();

  // Show a brief confirmation
  await swal.fire({
    type: "success",
    title: "Médias do Lote Registradas",
    text: "Continuando para as médias por aviário",
    timer: 1500,
    showConfirmButton: false,
  });

  // Proceed to warehouse averages
  processNextWarehouse();
}

// Function to process the next warehouse in the queue
function processNextWarehouse() {
  $("#warehouse-progress-container").remove();
  $("#average-input-container").remove();
  $("#warehouse-boxes-container").remove();
  $("#all-warehouses-container").remove();

  // Reset the last edited warehouse ID when moving to the next warehouse
  lastEditedWarehouseId = null;

  // Check if batch averages have been entered
  if (!batchAveragesEntered) {
    showBatchAverageInput();
    return;
  }

  // Filter out already processed warehouses
  const remainingWarehouses = warehouseQueue.filter(
    (w) => !processedWarehouses.has(w.id)
  );

  // If all warehouses are processed, show the summary
  if (remainingWarehouses.length === 0) {
    showAllWarehouses();
    return;
  }

  // Get the next warehouse to process
  currentWarehouseData = remainingWarehouses[0];
  currentWarehouseId = currentWarehouseData.id;

  // Create a progress indicator
  $("#report-table").before(`
    <div id="warehouse-progress-container" class="mb-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h5>Aviários: ${processedWarehouses.size} de ${
    warehousesMap.size
  } processados</h5>
        ${
          processedWarehouses.size > 0
            ? `<button id="showAllWarehousesButton" class="btn btn-info btn-sm">Ver Todos os Aviários</button>`
            : ""
        }
      </div>
      <div class="progress mb-3">
        <div class="progress-bar" role="progressbar" style="width: ${
          (processedWarehouses.size / warehousesMap.size) * 100
        }%;"
          aria-valuenow="${
            processedWarehouses.size
          }" aria-valuemin="0" aria-valuemax="${warehousesMap.size}">
          ${processedWarehouses.size} / ${warehousesMap.size}
        </div>
      </div>
    </div>
  `);

  $(document).off("click", "#showAllWarehousesButton");
  $(document).on("click", "#showAllWarehousesButton", function () {
    showAllWarehouses();
  });

  // Show the average input for this warehouse
  showAverageInputRow(currentWarehouseData);
}

// Function to show the average input row for a specific warehouse
function showAverageInputRow(warehouseData) {
  // Remove existing average input container
  $("#average-input-container").remove();

  // Create a container for the average input
  $("#warehouse-progress-container").after(`
    <div id="average-input-container" class="mb-4">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Aviário atual: ${warehouseData.name}</h5>
        </div>
        <div class="card-body">
          <p class="font-bold">Insira as médias para este aviário:</p>
          <div id="average-input-table"></div>
          <div class="d-flex justify-content-end mt-3">
            <button id="applyAveragesButton" class="btn btn-primary">Continuar</button>
          </div>
        </div>
      </div>
    </div>
  `);

  let warehouseAverages = {
    weightF: null,
    cvF: null,
    unifF: null,
    weightM: null,
    cvM: null,
    unifM: null,
  };

  // Check if this warehouse has existing data
  const hasExistingData = warehouseData.boxes.some(
    (box) => box.hasExistingData
  );

  if (warehouseData.boxes && warehouseData.boxes.length > 0) {
    // If we have existing data, use it to calculate averages
    const boxesWithWeightF = warehouseData.boxes.filter((b) => b.weightF);
    const boxesWithCvF = warehouseData.boxes.filter((b) => b.cvF);
    const boxesWithUnifF = warehouseData.boxes.filter((b) => b.unifF);
    const boxesWithWeightM = warehouseData.boxes.filter((b) => b.weightM);
    const boxesWithCvM = warehouseData.boxes.filter((b) => b.cvM);
    const boxesWithUnifM = warehouseData.boxes.filter((b) => b.unifM);

    if (boxesWithWeightF.length > 0) {
      warehouseAverages.weightF =
        boxesWithWeightF.reduce((sum, box) => sum + box.weightF, 0) /
        boxesWithWeightF.length;
    }
    if (boxesWithCvF.length > 0) {
      warehouseAverages.cvF =
        boxesWithCvF.reduce((sum, box) => sum + box.cvF, 0) /
        boxesWithCvF.length;
    }
    if (boxesWithUnifF.length > 0) {
      warehouseAverages.unifF =
        boxesWithUnifF.reduce((sum, box) => sum + box.unifF, 0) /
        boxesWithUnifF.length;
    }
    if (boxesWithWeightM.length > 0) {
      warehouseAverages.weightM =
        boxesWithWeightM.reduce((sum, box) => sum + box.weightM, 0) /
        boxesWithWeightM.length;
    }
    if (boxesWithCvM.length > 0) {
      warehouseAverages.cvM =
        boxesWithCvM.reduce((sum, box) => sum + box.cvM, 0) /
        boxesWithCvM.length;
    }
    if (boxesWithUnifM.length > 0) {
      warehouseAverages.unifM =
        boxesWithUnifM.reduce((sum, box) => sum + box.unifM, 0) /
        boxesWithUnifM.length;
    }
  }

  // Create a single row for average input
  rows = [
    {
      aviary: warehouseData.name,
      weightF: warehouseAverages.weightF,
      cvF: warehouseAverages.cvF,
      unifF: warehouseAverages.unifF,
      weightM: warehouseAverages.weightM,
      cvM: warehouseAverages.cvM,
      unifM: warehouseAverages.unifM,
    },
  ];

  // Build the table for average input
  table = new Handsontable(document.getElementById("average-input-table"), {
    data: rows,
    rowHeaders: false,
    stretchH: "all",
    licenseKey: "non-commercial-and-evaluation",
    nestedHeaders: [
      [
        { label: "", colspan: 1 },
        { label: "FÊMEAS", colspan: 3 },
        { label: "MACHOS", colspan: 3 },
      ],
      ["Aviário", "Peso", "CV", "Uniformidade", "Peso", "CV", "Uniformidade"],
    ],
    columns: [
      { data: "aviary", readOnly: true },

      { data: "weightF", type: "numeric" },
      { data: "cvF", type: "numeric" },
      { data: "unifF", type: "numeric" },

      { data: "weightM", type: "numeric" },
      { data: "cvM", type: "numeric" },
      { data: "unifM", type: "numeric" },
    ],

    minRows: 0,
    minSpareRows: 0,
    height: "auto",

    afterChange: (_changes, source) => {
      if (source === "edit") {
        table.render();
      }
    },
  });

  $(document).off("click", "#applyAveragesButton");
  $(document).on("click", "#applyAveragesButton", async function () {
    await applyAveragesAndShowBoxes(warehouseData);
  });
}

// Function to apply averages to boxes in the current warehouse
async function applyAveragesAndShowBoxes(warehouseData) {
  // Get the average values from the input table
  const data = table.getSourceData();
  if (!data || !data.length) {
    await showWarning("Não foi possível obter os dados da tabela.");
    return;
  }

  // Get the average values
  const averageValues = {
    weightF: parseFloat(data[0].weightF) || 0,
    cvF: parseFloat(data[0].cvF) || 0,
    unifF: parseFloat(data[0].unifF) || 0,
    weightM: parseFloat(data[0].weightM) || 0,
    cvM: parseFloat(data[0].cvM) || 0,
    unifM: parseFloat(data[0].unifM) || 0,
  };

  // Validate that at least some values were entered
  if (
    averageValues.weightF === 0 &&
    averageValues.cvF === 0 &&
    averageValues.unifF === 0 &&
    averageValues.weightM === 0 &&
    averageValues.cvM === 0 &&
    averageValues.unifM === 0
  ) {
    await showWarning("Por favor, insira pelo menos um valor médio.");
    return;
  }

  // Store the warehouse average values but don't apply them to individual boxes
  // Instead, we'll just store the warehouse averages for reference
  warehouseData.averages = {
    weightF: averageValues.weightF,
    cvF: averageValues.cvF,
    unifF: averageValues.unifF,
    weightM: averageValues.weightM,
    cvM: averageValues.cvM,
    unifM: averageValues.unifM,
  };

  // Ensure all boxes have their box name set
  warehouseData.boxes.forEach((box) => {
    box.box = box.box || "";

    // If the box already has values, keep them
    // Otherwise, leave the values as null for manual entry
    if (!box.hasExistingData) {
      // Reset any previously set values to null
      box.weightF = null;
      box.cvF = null;
      box.unifF = null;
      box.weightM = null;
      box.cvM = null;
      box.unifM = null;
    }
  });

  // Mark this warehouse as processed
  processedWarehouses.add(warehouseData.id);

  // Show a brief confirmation
  await swal.fire({
    type: "success",
    title: "Aviário Processado",
    text: `Médias aplicadas ao ${warehouseData.name}`,
    timer: 1200,
    showConfirmButton: false,
  });

  // Move to the next warehouse in the queue
  processNextWarehouse();
}

function showAllWarehouses() {
  // Store the current warehouse ID before showing all warehouses
  if (currentWarehouseId) {
    lastEditedWarehouseId = currentWarehouseId;
  }

  // Hide any existing UI
  $("#batch-average-container").remove();
  $("#warehouse-progress-container").remove();
  $("#average-input-container").remove();
  $("#warehouse-boxes-container").remove();
  $("#all-warehouses-container").remove();

  // Check if there are unprocessed warehouses
  const remainingWarehouses = warehouseQueue.filter(
    (w) => !processedWarehouses.has(w.id)
  );

  const allProcessed = remainingWarehouses.length === 0;
  const headerClass = allProcessed ? "bg-success" : "bg-info";
  const headerText = allProcessed
    ? `Todos os Aviários Processados (${processedWarehouses.size} de ${warehousesMap.size})`
    : `Aviários Processados (${processedWarehouses.size} de ${warehousesMap.size})`;

  // Create a container for all warehouses
  $("#report-table").before(`
    <div id="all-warehouses-container" class="mb-4">
      <div class="card">
        <div class="card-header ${headerClass} text-white d-flex justify-content-between align-items-center">
          <h5 class="mb-0">${headerText}</h5>
          <div>
            <button id="editBatchAveragesButton" class="btn btn-info btn-sm mr-2">Editar Médias do Lote</button>
            ${
              !allProcessed
                ? `<button id="continueDataEntryButton" class="btn btn-primary btn-sm mr-2">Continuar Lançamento</button>`
                : ""
            }
            ${
              lastEditedWarehouseId
                ? `<button id="backToEditingButton" class="btn btn-warning btn-sm">Voltar para Edição</button>`
                : ""
            }
          </div>
        </div>
        <div class="card-body">
          ${
            !allProcessed
              ? `<div class="alert alert-info mb-3">
                  <i class="fa fa-info-circle mr-2"></i>
                  Existem ${remainingWarehouses.length} aviários pendentes de lançamento.
                  Clique em "Continuar Lançamento" para prosseguir com a entrada de dados.
                </div>`
              : ""
          }
          <div id="all-warehouses-table"></div>
        </div>
      </div>
    </div>
  `);

  $(document).off("click", "#backToEditingButton");
  $(document).on("click", "#backToEditingButton", function () {
    backToEditing();
  });

  $(document).off("click", "#editBatchAveragesButton");
  $(document).on("click", "#editBatchAveragesButton", function () {
    editBatchAverages();
  });

  $(document).off("click", "#continueDataEntryButton");
  $(document).on("click", "#continueDataEntryButton", function () {
    processNextWarehouse();
  });

  rows = [];

  // Include all warehouses that have existing data
  warehousesMap.forEach((warehouseData) => {
    // Check if any box in this warehouse has existing data
    const hasExistingData = warehouseData.boxes.some(
      (box) => box.hasExistingData
    );

    // Include this warehouse if it's been processed or has existing data
    if (processedWarehouses.has(warehouseData.id) || hasExistingData) {
      // If it has existing data but wasn't marked as processed, mark it now
      if (hasExistingData && !processedWarehouses.has(warehouseData.id)) {
        processedWarehouses.add(warehouseData.id);
      }

      // Add all boxes from this warehouse to the rows
      rows.push(...warehouseData.boxes);
    }
  });

  if (currentSort) {
    switch (currentSort) {
      case "aviary":
        rows.sort((a, b) => a.aviary.localeCompare(b.aviary));
        break;
      case "box":
        rows.sort((a, b) => a.box.localeCompare(b.box));
        break;
      case "weightF":
        rows.sort((a, b) => (b.weightF || 0) - (a.weightF || 0));
        break;
      case "weightM":
        rows.sort((a, b) => (b.weightM || 0) - (a.weightM || 0));
        break;
    }
  }

  table = new Handsontable(document.getElementById("all-warehouses-table"), {
    data: rows,
    rowHeaders: false,
    stretchH: "all",
    licenseKey: "non-commercial-and-evaluation",
    nestedHeaders: [
      [
        { label: "DISTRIBUIÇÃO", colspan: 2 },
        { label: "PESOS POR AVIÁRIO E BOX", colspan: 6 },
      ],
      [
        { label: "", colspan: 2 },
        { label: "FÊMEAS", colspan: 3 },
        { label: "MACHOS", colspan: 3 },
      ],
      [
        "Aviário",
        "Box",
        "Peso",
        "CV",
        "Uniformidade",
        "Peso",
        "CV",
        "Uniformidade",
      ],
    ],
    columns: [
      { data: "aviary", readOnly: true },
      { data: "box", readOnly: true },

      { data: "weightF", type: "numeric" },
      { data: "cvF", type: "numeric" },
      { data: "unifF", type: "numeric" },

      { data: "weightM", type: "numeric" },
      { data: "cvM", type: "numeric" },
      { data: "unifM", type: "numeric" },
    ],

    minRows: 0,
    minSpareRows: 0,
    height: "auto",

    afterChange: (changes, source) => {
      if (source === "edit" && changes) {
        changes.forEach(([rowIndex, prop, oldValue, newValue]) => {
          const row = rows[rowIndex];
          const warehouseData = warehousesMap.get(row.warehouseId);
          if (warehouseData) {
            const boxIndex = warehouseData.boxes.findIndex(
              (b) => b.sampleCageId === row.sampleCageId && b.box === row.box
            );
            if (boxIndex !== -1) {
              // Check if the value actually changed
              if (oldValue !== newValue) {
                // Update the value in both the row and the warehouse data
                warehouseData.boxes[boxIndex][prop] = newValue;
                row[prop] = newValue;

                // Mark as modified
                warehouseData.boxes[boxIndex].isModified = true;
                row.isModified = true;
              }
            }
          }
        });
      }
    },
  });
}

//=======  POST
async function createReport() {
  const henBatchId = $("#HenBatchId").val();
  const date = $("#Date").val();

  if (processedWarehouses.size === 0) {
    await showWarning(
      "Por favor, insira as médias para pelo menos um aviário antes de salvar."
    );
    return;
  }

  try {
    await handleResponse(
      await fetch(
        `/SampleCageReport/RevalidateDate?henBatchId=${henBatchId}&date=${encodeURIComponent(
          date
        )}`
      )
    );
  } catch (e) {
    await showWarning(e.message);
    return;
  }

  $("#createBreedingWeightReportButton")
    .prop("disabled", true)
    .html('<i class="fa fa-spinner fa-spin"></i>');

  const processedBoxes = [];
  processedWarehouses.forEach((warehouseId) => {
    const warehouseData = warehousesMap.get(warehouseId);
    if (warehouseData) {
      processedBoxes.push(...warehouseData.boxes);
    }
  });

  // Filter out unmodified existing records
  const recordsToSave = processedBoxes.filter((r) => {
    // Include records that are new (no reportId) or have been modified
    return !r.reportId || r.isModified;
  });

  if (recordsToSave.length === 0) {
    await swal.fire({
      type: "info",
      title: "Nenhuma alteração detectada",
      text: "Não foram detectadas alterações nos dados existentes. Nenhum registro será atualizado.",
    });
    resetSaveButton();
    return;
  }

  const payload = recordsToSave.map((r) => {
    // Get the warehouse data to access the averages
    const warehouseData = warehousesMap.get(r.warehouseId);
    const warehouseAverages = warehouseData ? warehouseData.averages : null;

    return {
      SampleCageId: r.sampleCageId,
      HenBatchId: r.henBatchId,
      WarehouseId: r.warehouseId,
      WarehouseName: r.aviary,
      LineName: r.box,
      // Use the box values if they exist, otherwise null
      WeightFemale: r.weightF,
      CvFemale: r.cvF,
      UniformityFemale: r.unifF,
      WeightMale: r.weightM,
      CvMale: r.cvM,
      UniformityMale: r.unifM,
      // Include warehouse averages in a separate property
      WarehouseAverages: warehouseAverages,
      ReportId: r.reportId,
      IsModified: r.isModified || false,
    };
  });

  try {
    await postJSON("/SampleCageReport/CreateSampleCageFromTable", {
      reportDate: $("#Date").val(),
      reports: payload,
      batchAverages: batchAverages,
      processedWarehouses: Array.from(processedWarehouses),
    });
    await swal.fire({
      type: "success",
      title: "Sucesso",
      text: "Lançamentos criados!",
    });
    location.href = "/SampleCageReport?henStage=Breeding";
  } catch (e) {
    await swal.fire({
      type: "error",
      title: "Não foi possível salvar",
      html: e.message,
    });
  } finally {
    resetSaveButton();
  }
}

//======= API Calls

const getActiveHenBatchesByFarm = async (farmId) => {
  const response = await fetch(
    `${location.origin}/SampleCageReport/GetActiveHenBatchesByFarm?farmId=${farmId}&henStage=Breeding`
  );
  return response.ok ? response.json() : [];
};

//=======  AJAX helpers
async function fetchJSON(url) {
  return handleResponse(await fetch(url));
}

async function postJSON(url, obj) {
  const resp = await fetch(url, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(obj),
  });
  return handleResponse(resp);
}

// Function to clear all UI elements and their event handlers
function clearAllUI() {
  // Destroy the table if it exists
  if (table && !table.isDestroyed) {
    table.destroy();
    table = null;
  }

  // Remove all UI containers
  $("#batch-average-container").remove();
  $("#warehouse-progress-container").remove();
  $("#average-input-container").remove();
  $("#warehouse-boxes-container").remove();
  $("#all-warehouses-container").remove();
  $("#report-table").empty();
  $("#table-header").empty();

  // Remove any event handlers that might be attached to buttons
  $(document).off("click", "#applyBatchAveragesButton");
  $(document).off("click", "#applyAveragesButton");
  $(document).off("click", "#showAllWarehousesButton");
  $(document).off("click", "#backToEditingButton");
  $(document).off("click", "#editBatchAveragesButton");
  $(document).off("click", "#cancelBatchEditButton");
  $(document).off("click", "#continueDataEntryButton");

  // Reset variables
  rows = [];
  lastEditedWarehouseId = null;
  batchAveragesEntered = false;
}

function destroyTable() {
  if (table && !table.isDestroyed) {
    table.destroy();
    table = null;
  }
  $("#report-table").empty();
}

// Function to update sorting
function updateSort(sortBy) {
  // Remove the active class from all sort buttons
  $(".sort-button").removeClass("active");

  // Add the active class to the clicked button
  $(`.sort-button[data-sort="${sortBy}"]`).addClass("active");

  // Store the current sort
  currentSort = sortBy;

  // Reload the table with the new sorting
  if (table) {
    initTable();
  }
}

// Function to edit batch averages
function editBatchAverages() {
  // Clear the all warehouses view
  $("#all-warehouses-container").remove();
  $("#batch-average-container").remove();

  // Show the batch average input form with current values
  showBatchAverageInput();

  $("#batch-average-container .card-body .d-flex").prepend(`
    <button id="cancelBatchEditButton" class="btn btn-secondary mr-2">Cancelar</button>
  `);

  $(document).off("click", "#cancelBatchEditButton");
  $(document).on("click", "#cancelBatchEditButton", function () {
    showAllWarehouses();
  });

  $(document).off("click", "#applyBatchAveragesButton");
  $(document).on("click", "#applyBatchAveragesButton", async function () {
    await applyBatchAveragesAndShowAll();
  });
}

async function applyBatchAveragesAndShowAll() {
  // Get the average values from the input table
  const data = table.getSourceData();
  if (!data || !data.length) {
    await showWarning("Não foi possível obter os dados da tabela.");
    return;
  }

  // Get the batch average values
  batchAverages = {
    weightF: parseFloat(data[0].weightF) || 0,
    cvF: parseFloat(data[0].cvF) || 0,
    unifF: parseFloat(data[0].unifF) || 0,
    weightM: parseFloat(data[0].weightM) || 0,
    cvM: parseFloat(data[0].cvM) || 0,
    unifM: parseFloat(data[0].unifM) || 0,
  };

  // Validate that at least some values were entered
  if (
    batchAverages.weightF === 0 &&
    batchAverages.cvF === 0 &&
    batchAverages.unifF === 0 &&
    batchAverages.weightM === 0 &&
    batchAverages.cvM === 0 &&
    batchAverages.unifM === 0
  ) {
    await showWarning(
      "Por favor, insira pelo menos um valor médio para o lote."
    );
    return;
  }

  // Mark batch averages as entered
  batchAveragesEntered = true;
  $("#batch-average-container").remove();

  // Show a brief confirmation
  await swal.fire({
    type: "success",
    title: "Médias do Lote Atualizadas",
    text: "As médias do lote foram atualizadas com sucesso",
    timer: 1500,
    showConfirmButton: false,
  });

  // Return to all warehouses view
  showAllWarehouses();
}

// Function to go back to editing the last warehouse
function backToEditing() {
  if (!lastEditedWarehouseId) {
    return;
  }

  // Find the warehouse data for the last edited warehouse
  const warehouseData = warehousesMap.get(lastEditedWarehouseId);
  if (!warehouseData) {
    return;
  }

  $("#all-warehouses-container").remove();
  $("#batch-average-container").remove();

  // Set the current warehouse data and ID
  currentWarehouseData = warehouseData;
  currentWarehouseId = lastEditedWarehouseId;

  // Show the warehouse progress and input form
  $("#report-table").before(`
    <div id="warehouse-progress-container" class="mb-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h5>Aviários: ${processedWarehouses.size} de ${
    warehousesMap.size
  } processados</h5>
        <button id="showAllWarehousesButton" class="btn btn-info btn-sm">Ver Todos os Aviários</button>
      </div>
      <div class="progress mb-3">
        <div class="progress-bar" role="progressbar" style="width: ${
          (processedWarehouses.size / warehousesMap.size) * 100
        }%;"
          aria-valuenow="${
            processedWarehouses.size
          }" aria-valuemin="0" aria-valuemax="${warehousesMap.size}">
          ${processedWarehouses.size} / ${warehousesMap.size}
        </div>
      </div>
    </div>
  `);

  $(document).off("click", "#showAllWarehousesButton");
  $(document).on("click", "#showAllWarehousesButton", function () {
    showAllWarehouses();
  });

  // Show the average input for this warehouse
  showAverageInputRow(warehouseData);
}
