@model WebApp.Models.WarehouseSampleCageReportViewModel
@using Binit.Framework
@using Microsoft.Extensions.Localization;
@inject IStringLocalizer<SharedResources> localizer
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.SampleCageReport.CreateOrEdit;

@{
    var farms = ViewData["Farms"] as List<SelectListItem>;
}

<link href="~/css/handsontable.css" rel="stylesheet" />
<style>
    #hot-display-license-info {
        display: none
    }

    .ht_clone_top {
        z-index: 1
    }

    #report-table {
        width: 100%
    }
</style>

<div class="row">
    <div class="col-md-6" id="FarmDiv" style="@(farms.Count() > 1 ? "" : "display:none")">
        <label class="select">@localizer["Fazenda"]</label>
        <div class="form-group m-b-40">
            <select class="select2" id="FarmId">
                <option value="">@localizer["Selecione uma fazenda"]</option>
                @foreach (var item in farms)
                {
                    <option value="@item.Value" selected="@item.Selected">@item.Text</option>
                }
            </select>
        </div>
    </div>
    <div class="col-md-6" id="HenBatchDiv">
        <label class="select">@localizer["Lote"]</label>
        <div class="form-group m-b-40">
            <select class="select2" id="HenBatchId">
                <option value="">Selecione um lote</option>
            </select>
        </div>
    </div>
</div>

<div class="row floating-labels">
    <div class="col-md-6">
        <ignite-datetime-picker id="Date"></ignite-datetime-picker>
    </div>
</div>

<div id="table-header" class="report-header mb-3"></div>

<div id="report-table"></div>

<div class="d-flex justify-content-end mt-3">
    <button id="createLayingWeightReportButton" class="btn btn-themecolor">Salvar</button>
</div>

@section scripts {
    <script src="~/js/handsontable.js"></script>
    <script src="~/js/views/sampleCageReport/createLayingWeightReport.js"></script>
}
<ignite-load plugins="select2,date-time-picker"></ignite-load>
