$(document).ready(function () {
  // initialize report table if there is only one farm
  if ($("#FarmId").get(0).options.length == 2) {
    loadActiveHenBatches();
  }

  // set date to today
  $("#Date").val(
    new Date().toISOString().split("T")[0].split("-").reverse().join("/")
  );
  setTimeout(() => {
    $("#Date").focus();
    setTimeout(() => {
      $("#Date").blur();
    }, 250);
  }, 250);

  $("#FarmId").on("change", async function () {
    loadActiveHenBatches();
  });

  $("#HenBatchId").on("change", async function () {
    const henBatchId = $(this).val();
    const date = $("#Date").val();

    // Validate date if both henBatchId and date are available
    if (henBatchId && date) {
      const isDateValid = await validateDateForNewReport(henBatchId, date);
      if (!isDateValid) {
        clearReportTable();
        $("#report-table").hide();
        return;
      }
    }

    await debouncedInitializeReportTable();
    await loadGad();
  });

  $("#Date").on("blur", async function () {
    const date = $(this).val();
    const henBatchId = $("#HenBatchId").val();

    // Validate date before proceeding
    if (date && henBatchId) {
      const isDateValid = await validateDateForNewReport(henBatchId, date);
      if (!isDateValid) {
        clearReportTable();
        $("#report-table").hide();
        return;
      }
    }

    await debouncedInitializeReportTable();
    await loadGad();
  });

  $("#createButton").on("click", async function () {
    await createReport();
  });
});

let currentTable = null;
let reportData = [];
const deviationMap = new Map();

// Filters ---------------------------------------------------------------
async function loadActiveHenBatches() {
  clearReportTable();

  const farmId = $("#FarmId").val();
  const activeHenBatches = await getActiveHenBatchesByFarm(farmId);

  const $henBatchSelect = $("#HenBatchId");
  $henBatchSelect.prop("disabled", false);
  $henBatchSelect.empty();

  // Add default option
  $henBatchSelect.append(
    $("<option></option>").val("").text("Selecione um lote")
  );

  // Add active hen batches
  activeHenBatches.forEach((henBatch) => {
    $henBatchSelect.append(
      $("<option></option>").val(henBatch.id).text(henBatch.code)
    );
  });

  // If there's only one hen batch, select it automatically
  if (activeHenBatches.length === 1) {
    $henBatchSelect.val(activeHenBatches[0].id).trigger("change");
    $henBatchSelect.prop("disabled", true);
  }
}

// Report table ------------------------------------------------------------
const columns = [
  { data: "warehouse", type: "text", editor: false, readOnly: true },
  { data: "line", type: "text", editor: false, readOnly: true },
  // { data: "henBatch", type: "text", editor: false, readOnly: true },
  {
    data: "waterConsumption",
    type: "numeric",
    allowInvalid: false,
    validator: (value, callback) => callback(value >= 0),
  },
  {
    data: "waterPh",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 14),
    allowInvalid: false,
  },
  {
    data: "waterChlorineConcentration",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "waterPillQuantity",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 100),
    allowInvalid: false,
  },
  {
    data: "minTemp",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 60),
    allowInvalid: false,
  },
  {
    data: "maxTemp",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 60),
    allowInvalid: false,
  },
  {
    data: "humidity",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 100),
    allowInvalid: false,
  },
  {
    data: "feedIntakeFemale",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "feedIntakeOriginFemale",
    type: "dropdown",
    source: async function (query, process) {
      const rowData = this.instance.getSourceDataAtRow(this.row);
      process(rowData.feedIntakeOrigins.map((origin) => origin.name));
    },
    allowInvalid: false,
  },
  {
    data: "feedIntakeMale",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "feedIntakeOriginMale",
    type: "dropdown",
    source: async function (query, process) {
      const rowData = this.instance.getSourceDataAtRow(this.row);
      process(rowData.feedIntakeOrigins.map((origin) => origin.name));
    },
    allowInvalid: false,
  },
  {
    data: "deadFemale",
    type: "numeric",
    allowInvalid: false,
  },
  {
    data: "deadMale",
    type: "numeric",
    allowInvalid: false,
  },
];

const columnsMap = new Map(
  columns.map((column, index) => [column.data, { ...column, index }])
);

const deadFemaleColumnIndex = columnsMap.get("deadFemale").index;
const deadMaleColumnIndex = columnsMap.get("deadMale").index;
const feedIntakeFemaleColumnIndex = columnsMap.get("feedIntakeFemale").index;
const feedIntakeMaleColumnIndex = columnsMap.get("feedIntakeMale").index;
const feedIntakeOriginFemaleColumnIndex = columnsMap.get(
  "feedIntakeOriginFemale"
).index;
const feedIntakeOriginMaleColumnIndex = columnsMap.get(
  "feedIntakeOriginMale"
).index;

const debouncedInitializeReportTable = debounce(() => initializeReportTable());

async function initializeReportTable() {
  clearReportTable();
  $("#createButton").prop("disabled", true);

  const henBatchId = $("#HenBatchId").val();
  const date = $("#Date").val();

  // First validate the date for new report creation
  if (henBatchId && date) {
    const isDateValidForNewReport = await validateDateForNewReport(
      henBatchId,
      date
    );
    if (!isDateValidForNewReport) {
      $("#report-table").hide();
      return;
    }
  }

  try {
    const isDateValid = await validateDate();
    if (!isDateValid) {
      return;
    }
  } catch (error) {
    console.error(error);
    return;
  }

  const warehouses = await getWarehousesWithLinesByBatch(henBatchId);
  if (warehouses.length === 0) {
    return;
  }

  reportData = [];

  warehouses.forEach((warehouse) => {
    warehouse.lines.forEach((line) => {
      if (line.henBatches.length == 1) {
        const henBatch = line.henBatches[0];
        const feedIntakeOrigins = henBatch.feedIntakeOrigins;

        reportData.push({
          warehouse: warehouse.code,
          line: line.code,
          henBatch: henBatch.code,
          henBatchId: henBatch.id,
          henAmountFemale: henBatch.henAmountFemale,
          henAmountMale: henBatch.henAmountMale,
          feedIntakeOrigins: feedIntakeOrigins,
          feedIntakeOriginFemale:
            feedIntakeOrigins.length === 1
              ? feedIntakeOrigins[0].name
              : feedIntakeOrigins.find((origin) => origin.code === "F")?.name,
          feedIntakeOriginMale:
            feedIntakeOrigins.length === 1
              ? feedIntakeOrigins[0].name
              : feedIntakeOrigins.find((origin) => origin.code === "M")?.name,
        });
      }
    });
  });

  const tableElement = $("#report-table").get(0);
  currentTable = new Handsontable(tableElement, {
    data: reportData,
    columns: columns,
    rowHeaders: false,
    nestedHeaders: [
      [
        { label: "<strong>DISTRIBUIÇÃO</strong>", colspan: 2 },
        { label: "<strong>CONDIÇÕES AMBIENTAIS</strong>", colspan: 7 },
        { label: "<strong>CONSUMO ALIMENTO</strong>", colspan: 4 },
        { label: "<strong>MORTALIDADE</strong>", colspan: 2 },
      ],
      [
        "Aviário",
        "Box",
        // "Lote",
        "Água",
        "ph/PH",
        "PPM <br>Cloro",
        "Rep. <br>Pastilha",
        "Temp. <br>Mínima",
        "Temp. <br>Máxima",
        "Umidade %",
        "Alimento <br>Fêmeas",
        "Origem <br>Fêmeas",
        "Alimento <br>Machos",
        "Origem <br>Machos",
        "Fêmeas <br>Mortas",
        "Machos <br>Mortos",
      ],
    ],
    filters: false,
    fillHandle: false, // disable drag copy
    dropdownMenu: false,
    autoColumnSize: true,
    manualColumnResize: true,
    manualRowResize: true,
    formulas: true,
    width: "100%",
    height: "auto",
    stretchH: "all",
    licenseKey: "non-commercial-and-evaluation",
    // make feed intake and dead hens read only if hen amount is 0
    cells: function (row, col) {
      const rowData = reportData[row];
      if (rowData.henAmountFemale === 0) {
        return {
          readOnly:
            col === feedIntakeFemaleColumnIndex ||
            col === feedIntakeOriginFemaleColumnIndex ||
            col === deadFemaleColumnIndex,
        };
      }
      if (rowData.henAmountMale === 0) {
        return {
          readOnly:
            col === feedIntakeMaleColumnIndex ||
            col === feedIntakeOriginMaleColumnIndex ||
            col === deadMaleColumnIndex,
        };
      }
      return {};
    },
    afterChange: (changes, source) => {
      if (source === "loadData") {
        return;
      }

      const propertiesToCopy = [
        "waterPh",
        "waterChlorineConcentration",
        "waterPillQuantity",
        "minTemp",
        "maxTemp",
        "humidity",
      ];

      const propertiesToDistribute = [
        "waterConsumption",
        // "feedIntakeFemale",
        // "feedIntakeMale",
      ];

      changes?.forEach(([row, prop, oldValue, newValue]) => {
        const rowData = reportData[row];

        // Copy and distribute values
        let valueToApply = null;

        if (propertiesToCopy.includes(prop)) {
          // copy environment conditions to all lines of the warehouse
          valueToApply = newValue;
        } else if (propertiesToDistribute.includes(prop)) {
          // distribute consumption and feed intake to all lines of the warehouse
          const warehouseLinesCount = reportData.filter(
            (item) => item.warehouse === rowData.warehouse
          ).length;
          valueToApply =
            Math.round((newValue * 100) / warehouseLinesCount) / 100; // round to 2 decimal places
        }

        if (valueToApply !== null) {
          reportData.forEach((item) => {
            if (item.warehouse === rowData.warehouse) {
              item[prop] = valueToApply;
            }
          });
        }

        // Validate deviation
        isDeviationValid(rowData.henBatchId, valueToApply ?? newValue, prop);
      });

      currentTable.loadData(reportData);
    },
  });

  $("#createButton").prop("disabled", false);
  await loadDeviationParameters();
}

function clearReportTable() {
  if (currentTable && !currentTable.isDestroyed) {
    currentTable.destroy();
    currentTable = null;
  }
}

// GAD -----------------------------------------------------------------
let currentBatchId = null;
let currentDate = null;

const loadGad = async () => {
  const henBatchId = $("#HenBatchId").val();
  const date = $("#Date").val();

  if (!henBatchId || !date || !currentTable) {
    return;
  }

  // prevent reloading if henBatchId and date have not changed
  if (henBatchId === currentBatchId && date === currentDate) {
    return;
  }
  currentBatchId = henBatchId;
  currentDate = date;

  const gad = await getPlannedGADByBatch(henBatchId, date);
  const gadMap = new Map();
  gad.forEach((item) => {
    gadMap.set(item.henbatchId, item);
  });

  reportData.forEach((item) => {
    const gadItem = gadMap.get(item.henBatchId);
    if (gadItem) {
      item.feedIntakeFemale = gadItem.programFemale;
      item.feedIntakeMale = gadItem.programMale;
    }
  });

  currentTable.loadData(reportData);
};

// Deviation parameters -------------------------------------------------
const loadDeviationParameters = async () => {
  const deviationParameters = await getDeviationParameters(
    reportData.map((item) => item.henBatchId)
  );

  deviationMap.clear();
  deviationParameters.forEach((item) => {
    deviationMap.set(item.henBatchId, item);
  });
};

const isDeviationValid = (
  henBatchId,
  value,
  property,
  displayMessage = true
) => {
  const deviationParameters = deviationMap.get(henBatchId);

  if (deviationParameters) {
    const maxValue = deviationParameters[`${property}Max`] ?? 0;
    const minValue = deviationParameters[`${property}Min`] ?? 0;
    const valueToCompare = value ?? 0;

    if (valueToCompare > maxValue) {
      if (displayMessage) {
        swal.fire({
          title: "Atenção",
          text: `Valor está acima dos carregados nos relatórios anteriores (${maxValue})`,
          type: "warning",
          toast: true,
          position: "bottom-end",
          showConfirmButton: false,
          timer: 3000,
        });
      }
      return false;
    } else if (valueToCompare < minValue) {
      if (displayMessage) {
        swal.fire({
          title: "Atenção",
          text: `Valor está abaixo dos carregados nos relatórios anteriores (${minValue})`,
          type: "warning",
          toast: true,
          position: "bottom-end",
          showConfirmButton: false,
          timer: 3000,
        });
      }
      return false;
    }
  }
  return true;
};

const validateAllDeviations = () => {
  let isAllValid = true;
  reportData.forEach((item) => {
    Object.keys(item).forEach((property) => {
      if (!isDeviationValid(item.henBatchId, item[property], property, false)) {
        isAllValid = false;
      }
    });
  });
  return isAllValid;
};

// Date validation -------------------------------------------------
async function validateDateForNewReport(henBatchId, selectedDate) {
  if (!henBatchId || !selectedDate) {
    return false;
  }

  try {
    // Get the most recent hen report date
    const response = await fetch(
      `${location.origin}/HenReport/GetRealLastReportDate?henBatchId=${henBatchId}`
    );

    if (!response.ok) {
      console.error("Failed to get last report date");
      return true; // Allow proceeding if we can't get the date
    }

    const lastReportDateStr = await response.text();

    // Parse dates for comparison
    const selectedDateParts = selectedDate.split("/");
    const selectedDateObj = new Date(
      selectedDateParts[2],
      selectedDateParts[1] - 1,
      selectedDateParts[0]
    );

    const lastReportDateParts = lastReportDateStr.split("/");
    const lastReportDateObj = new Date(
      lastReportDateParts[2],
      lastReportDateParts[1] - 1,
      lastReportDateParts[0]
    );

    // Check if selected date is on or before the last report date
    if (selectedDateObj <= lastReportDateObj) {
      const nextAllowedDate = new Date(lastReportDateObj);
      nextAllowedDate.setDate(nextAllowedDate.getDate() + 1);
      const nextAllowedDateStr = nextAllowedDate.toLocaleDateString("pt-BR");

      await Swal.fire({
        title: "Data Inválida",
        html: `
          <p>Não é possível criar relatórios para datas anteriores ou iguais ao último relatório submetido.</p>
          <p><strong>Último relatório:</strong> ${lastReportDateStr}</p>
          <p><strong>Data mínima permitida:</strong> ${nextAllowedDateStr}</p>
          <p>Por favor, selecione uma data posterior ao último relatório.</p>
        `,
        type: "warning",
        confirmButtonText: "OK",
        allowOutsideClick: false,
        allowEscapeKey: false,
      });

      return false;
    }

    return true;
  } catch (error) {
    console.error("Error validating date:", error);
    return true; // Allow proceeding if validation fails
  }
}

async function validateDate() {
  const henBatchId = $("#HenBatchId").val();
  const date = $("#Date").val();

  if (!henBatchId || !date) {
    return false;
  }

  try {
    const isDateValid = await validateDateSkipHenAmount(henBatchId, date);
    if (!isDateValid) {
      return false;
    }

    const realLastReportDate = await getRealLastReportDate(henBatchId);
    Swal.fire({
      title: "Informação",
      html: `Data do último relatório: <strong>${realLastReportDate}</strong>`,
      type: "info",
      timer: 5000,
      showConfirmButton: true,
    });

    return true;
  } catch (error) {
    Swal.fire({
      title: "Erro",
      html: error.message,
      type: "error",
      allowOutsideClick: false,
      allowEscapeKey: false,
    });
    return false;
  }
}

// Create report ---------------------------------------------------------
const createReport = async () => {
  if (!validateAllDeviations()) {
    const isConfirmed = await Swal({
      html: "Alguns dos valores digitados têm um desvio maior que 20% em relação aos valores digitados nos relatórios anteriores; recomenda-se revisar os dados.",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "green",
      confirmButtonText: "Criar",
      cancelButtonText: "Revisar",
    });

    if (!isConfirmed.value) {
      return;
    }
  }

  // Show loading state on button
  const createButton = $("#createButton");
  const originalText = createButton.text();
  createButton
    .prop("disabled", true)
    .html('<i class="fa fa-spinner fa-spin"></i> Enviando...');

  try {
    const reports = reportData.map((item) => {
      const breedingReport = {
        ...item,
        FeedIntakeFemaleOriginId: item.feedIntakeOrigins.find(
          (origin) => origin.name === item.feedIntakeOriginFemale
        )?.id,
        FeedIntakeMaleOriginId: item.feedIntakeOrigins.find(
          (origin) => origin.name === item.feedIntakeOriginMale
        )?.id,
      };

      return breedingReport;
    });

    // ensure that all numeric fields are sent as numbers
    reports.forEach((item) => {
      Object.keys(item).forEach((key) => {
        if (columnsMap.get(key)?.type === "numeric" && !item[key]) {
          item[key] = 0;
        }
      });
    });

    await createHenReportsFromTable($("#Date").val(), reports);
  } catch (error) {
    console.error(error);
  } finally {
    // Restore button state
    createButton.prop("disabled", false).text(originalText);
  }
};

// API calls ------------------------------------------------------------
const getActiveHenBatchesByFarm = async (farmId) => {
  const response = await fetch(
    `${location.origin}/HenReport/GetActiveHenBatchesByFarm?farmId=${farmId}&henStage=2`
  );

  return response.json();
};

const getWarehousesWithLinesByBatch = async (henBatchId) => {
  if (!henBatchId) {
    return [];
  }

  const response = await fetch(
    `${location.origin}/HenReport/GetWarehousesWithLinesByBatch?henBatchId=${henBatchId}`
  );

  return response.json();
};

const getPlannedGADByBatch = async (henBatchId, date) => {
  const response = await fetch(
    `${location.origin}/HenReport/GetPlannedGADByBatch?henBatchId=${henBatchId}&date=${date}`
  );

  return response.json();
};

const getDeviationParameters = async (henBatchIds) => {
  const response = await fetch(
    `${location.origin}/HenReport/CreateDeviationDTOs?ids=${henBatchIds.join(
      ","
    )}`
  );

  return response.json();
};

const createHenReportsFromTable = async (reportDate, reports) => {
  try {
    const response = await fetch(
      `${location.origin}/HenReport/CreateBreedingReportsFromTable`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reportDate: reportDate,
          reports: reports,
        }),
      }
    );

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Erro ao criar lançamentos");
    }

    await Swal({
      title: "Sucesso",
      text: "Lançamentos criados com sucesso",
      type: "success",
      confirmButtonColor: "green",
      confirmButtonText: "OK",
    });

    redirectAfterCreation(result.henReports);
  } catch (error) {
    swal.fire({
      title: "Erro",
      text: error.message,
      type: "error",
    });
    console.error(error);
  }
};

const getRealLastReportDate = async (henBatchId) => {
  const response = await fetch(
    `${location.origin}/HenReport/GetRealLastReportDate?henBatchId=${henBatchId}`
  );

  if (!response.ok) {
    throw new Error("Falha em buscar a data do último relatório");
  }

  return response.text();
};

const validateDateSkipHenAmount = async (henBatchId, date) => {
  const response = await fetch(
    `${location.origin}/HenReport/ValidateDateSkipHenAmount?date=${date}&henBatchId=${henBatchId}`
  );

  if (!response.ok) {
    const result = await response.text();
    throw new Error(result || "Falha ao validar a data do relatório");
  }

  return true;
};

// Utilities ------------------------------------------------------------
function debounce(func, timeout = 300) {
  let timer;
  return (...args) => {
    if (!timer) {
      func.apply(this, args);
    }
    clearTimeout(timer);
    timer = setTimeout(() => {
      timer = undefined;
    }, timeout);
  };
}

const redirectAfterCreation = (reports) => {
  const reportsWithDeadHen = reports
    .filter((report) => report.totalDeaths > 0 || report.totalDepopulations > 0)
    .map((report) => report.id);

  if (reportsWithDeadHen.length > 0) {
    const henReportsId = reportsWithDeadHen.join(",");
    window.location.href = `${location.origin}/HenReport/EstablishDeathAndDepopulationQuantitiesReasons?henStage=Breeding&henReportsId=${henReportsId}`;
  } else {
    window.location.href = `${location.origin}/HenReport?henStage=Breeding`;
  }
};
