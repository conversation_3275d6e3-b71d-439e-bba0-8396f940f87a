﻿@model Domain.Logic.BusinessLogic.DTOs.TaskDTO
@using Binit.Framework;
@using Microsoft.Extensions.Localization;
@using WebApp.WebTools
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Task.CalendarPartials;
@inject IStringLocalizer<SharedResources> localizer

@{
    Layout = null;
    var actionCreate = (bool)ViewData["ActionCreate"];
    var lenguage = ViewData["UserLanguage"] as string;
}
<form id='task-form'>

    <div class='input-group'>
        <input type="hidden" asp-for="Id" />
        <input type="hidden" asp-for="TaskStatus" />
        <input type="hidden" asp-for="ClosingTask" />
        @if (actionCreate)
        {
            <div class='col-md-12'>
                <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.TitleLabel]</label>
                <ignite-input type="text" for-property="Title"></ignite-input>
            </div>
        }
        <div class='col-md-6' id="TaskTypeDiv">
            <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.TaskTypeLabel]</label>
            <ignite-dropdown for-property="TaskType"
                             items="@Model.TaskTypes"
                             placeholder="@localizer[Lang.PlaceholderSelectType]">
            </ignite-dropdown>
        </div>
        <div class='col-md-6 '>
            <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.RelevanceLabel]</label>
            <ignite-dropdown for-property="Relevance"
                             items="@Model.RelevanceOptions"
                             placeholder="@localizer[Lang.PlaceholderSelectRelevance]">
            </ignite-dropdown>
        </div>
        @if (actionCreate && Model.IsTask)
        {
            <div class='col-md-12' id="containerDIV" style="display:@(string.IsNullOrEmpty(Model.TaskType)? "none":" ")">
                <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.ContainerLabel]</label>
                <ignite-dropdown for-property="ContainerId"
                                 items="@Model.Containers"
                                 placeholder="@localizer[Lang.PlaceholderSelectContainer]">
                </ignite-dropdown>
            </div>
        }
        else if (actionCreate && !Model.IsTask)
        {
            <div class='col-md-12' id="containerDIV" style="display:@(string.IsNullOrEmpty(Model.TaskType)? "none":" ")">
                <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.ContainerLabel]</label>
                <ignite-dropdown for-property="ContainersIds"
                                 items="@Model.Containers"
                                 placeholder="@localizer[Lang.PlaceholderSelectContainer]"
                                 multiple>
                </ignite-dropdown>
            </div>
        }

        @if (Model.IsTask)
        {
    <div class='col-md-12'>
        <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.AlertDateLabel]</label>
        <ignite-input for-property="AlertDate" css-classes="my-dateTime-class"></ignite-input>
    </div>
        }
    <div class='col-md-6'>
        <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.StartDateLabel]</label>
        <ignite-input for-property="Start" css-classes="my-dateTime-class"></ignite-input>
    </div>
    <div class='col-md-6'>
        <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.EndDateLabel]</label>
        <ignite-input for-property="DateEnd" css-classes="my-dateTime-class"></ignite-input>
    </div>
        <div class='col-md-12'>
            <label style='padding-left:0px'>@localizer[Lang.DescriptionLabel]</label>
            <textarea style='margin-bottom:25px' id="description" name='description' class='form-control' placeholder=''> @Model.Description </textarea>
        </div>
        @if (!Model.IsTask)
        {
            <div class='col-md-12'>
                <label style='padding-left:0px'>@localizer[Lang.ActionsTakenLabel]</label>
                <textarea style='margin-bottom:25px' id="actionsTaken" name='actionsTaken' class='form-control' placeholder=''> @Model.ActionsTaken </textarea>
            </div>

            <div class="col-md-4">
                <ignite-checkbox for-property="EmailNotification" onchange="Switch()"></ignite-checkbox>
            </div>
            <div class="row col-md-12" id="emailNotificationGroup" style="display:none; margin-left:0px">
                <div class='col-md-12' style="margin-bottom:40px">
                    <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.UsersLabel]</label>
                    <select class="form-control select2" multiple="multiple" id="UsersIds" name="UsersIds">
                        @foreach (var item in @Model.Users)
                        {
                            if (item.Selected)
                            {
                                <option Model.Users value="@item.Value" selected>@item.Text</option>
                            }
                            else
                            {
                                <option Model.Users value="@item.Value">@item.Text</option>
                            }

                        }
                    </select>
                </div>
            </div>
        }
        else
        {
            <div class="col-md-4">
                <ignite-checkbox for-property="EmailNotification" onchange="Switch()"></ignite-checkbox>
            </div>
            <div class="row col-md-12" id="emailNotificationGroup" style="display:none; margin-left:0px">
                <div class="btn-group col-md-12" aria-label="date" role="group" style="margin-bottom:15px">
                    <button type="button" id="btn-warningdate" class="btn @(Model.WarningDate ? "btn-themecolor" : "btn-outline-themecolor") btn-date" style="@(!Model.WarningDate ? "border-color:#29313ea1":"")" onclick="this.blur();">@localizer[Lang.BtnWarningLabel]</button>
                    <button type="button" id="btn-expirationdate" class="btn @(Model.ExpirationDate ? "btn-themecolor" : "btn-outline-themecolor") btn-date" style="@(!Model.ExpirationDate ? "border-color:#29313ea1":"")" onclick="this.blur();">@localizer[Lang.BtnExpirationLabel]</button>
                </div>

                <div class='col-md-6'>
                    <ignite-dropdown for-property="Frequency"
                                     items="@Model.FrequencyOptions"
                                     placeholder="@localizer[Lang.PlaceholderEmailNotificationFrequency]">
                    </ignite-dropdown>
                </div>
                <div class='col-md-6'>
                    <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.WarningsLabel]</label>
                    <ignite-input for-property="Warnings" type="number"></ignite-input>
                </div>

                <div class='col-md-12' style="margin-bottom:40px">
                    <label class='col-md-12' style='padding-left:0px'>@localizer[Lang.UsersLabel]</label>
                    <select class="form-control select2" multiple="multiple" id="UsersIds" name="UsersIds">
                        @foreach (var item in @Model.Users)
                        {
                            if (item.Selected)
                            {
                                <option Model.Users value="@item.Value" selected>@item.Text</option>
                            }
                            else
                            {
                                <option Model.Users value="@item.Value">@item.Text</option>
                            }

                        }
                    </select>
                </div>
            </div>
        }
    </div>
</form>

@await Html.PartialAsync("_FileManagerWithDTO", Model.Files,
        new FileManagerOptions(ViewData, localizer)
        {
            UploadEnabled = true,
            MaxFiles = 3,
            ParentFormId = "task-form",
            PropertyName = "FilesIds"
        }
    );