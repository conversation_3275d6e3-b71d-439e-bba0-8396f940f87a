@model WebApp.Models.MaterialBatchAdjustmentViewModel
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.ShippingNote.CreateOrEdit
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer
@{
    int i = (int)ViewData["ParentIndex"];
    int j = (int)ViewData["Index"];
}

<input type="hidden" name="MaterialsAdjusted[@i].MaterialBatchesAdjusted[@j].HasAdjustment"
    class="MaterialsBatchShipped_@(i)_HasAdjustment" value="@Model.HasAdjustment.ToString()">

<input type="hidden" name="MaterialsAdjusted[@i].MaterialBatchesAdjusted[@j].MaterialBatchId"
    value="@Model.MaterialBatchId" />

<tr class="material-batch-row-@i" hidden="@(!Model.Adjustment.HasValue)">
    <td style="vertical-align:middle; width:40%">
        <div class="form-group" binit-validation-for="Name" binit-onerror-class="has-danger"
            binit-onsuccess-class="has-success">
            <input name="MaterialsAdjusted[@i].MaterialBatchesAdjusted[@j].Name" class="form-control"
                value="@Model.Name" readonly />
            <span class="bar"></span>
            <label asp-for="Name"></label>
            <span asp-validation-for="Name" class="form-control-feedback"></span>
        </div>
    </td>

    <td style="vertical-align:middle; width:10%">
        <div class="form-group" binit-validation-for="Quantity" binit-onerror-class="has-danger"
            binit-onsuccess-class="has-success">
            <input name="MaterialsAdjusted[@i].MaterialBatchesAdjusted[@j].Quantity" class="form-control"
                value="@Model.Quantity" readonly />
            <span class="bar"></span>
            <label asp-for="Quantity"></label>
            <span asp-validation-for="Quantity" class="form-control-feedback"></span>
        </div>
    </td>

    <td style="vertical-align:middle; width:10%">
        <div class="form-group" binit-validation-for="Adjustment" binit-onerror-class="has-danger"
            binit-onsuccess-class="has-success">
            <input name="MaterialsAdjusted[@i].MaterialBatchesAdjusted[@j].Adjustment"
                class="form-control material-batch-adjustment-@i" value="@Model.Adjustment"
                onchange="quantityAdjustment(@i)" placeholder="@localizer[Lang.AdjustmentPlaceholder]" min="0" />
            <span class="bar"></span>
            <label asp-for="Adjustment"></label>
            <span asp-validation-for="Adjustment" class="form-control-feedback"></span>
        </div>
    </td>
</tr>