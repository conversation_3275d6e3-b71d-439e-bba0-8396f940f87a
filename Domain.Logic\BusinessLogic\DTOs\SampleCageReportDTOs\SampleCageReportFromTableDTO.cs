using System;
using System.Collections.Generic;

namespace Domain.Logic.BusinessLogic.DTOs.SampleCageReportDTOs
{
    /// <summary>
    /// Individual item/row.
    /// </summary>
    public class SampleCageReportFromTableDTO
    {
        public Guid SampleCageId { get; set; }
        public Guid HenBatchId { get; set; }
        public Guid WarehouseId { get; set; }
        public string WarehouseName { get; set; }
        public string LineName { get; set; }
        public decimal? WeightFemale { get; set; }   // g
        public decimal? WeightMale { get; set; }   // g
        public decimal? CvFemale { get; set; }
        public decimal? CvMale { get; set; }
        public decimal? UniformityFemale { get; set; }
        public decimal? UniformityMale { get; set; }
        public decimal? EggWeight { get; set; }   // g
        public Guid? ReportId { get; set; }
        public bool IsModified { get; set; }
        public WarehouseAveragesDTO WarehouseAverages { get; set; }
    }

    /// <summary>
    /// Warehouse average values.
    /// </summary>
    public class WarehouseAveragesDTO
    {
        public decimal? WeightF { get; set; }
        public decimal? CvF { get; set; }
        public decimal? UnifF { get; set; }
        public decimal? WeightM { get; set; }
        public decimal? CvM { get; set; }
        public decimal? UnifM { get; set; }
        public decimal? EggWeight { get; set; }
    }

    /// <summary>
    /// POST Wrapper with date + items list.
    /// </summary>
    public class CreateSampleCageReportFromTableDTO
    {
        public string ReportDate { get; set; }
        public List<SampleCageReportFromTableDTO> Reports { get; set; }
        public List<Guid> ProcessedWarehouses { get; set; } = new List<Guid>();
    }

    public class SampleCageReportHenBatchDTO
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public int HenAmountFemale { get; set; }
        public int HenAmountMale { get; set; }
    }

    /// <summary>
    /// Minimum controller response after save.
    /// </summary>
    public class SampleCageReportResultDTO
    {
        public Guid Id { get; set; }
        public decimal AverageWeight { get; set; }
    }
}
