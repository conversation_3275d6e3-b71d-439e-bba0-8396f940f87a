﻿using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.FoodInventoryDTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Domain.Logic.Interfaces
{
    public interface IInventoryBusinessLogic
    {
        #region Get food inventory functions

        /// <summary>
        /// Get consumptions from the last week by filters passed.
        /// </summary>
        /// <param name="filters">Filters to apply to the query</param>
        /// <param name="henStage">Optional hen stage to filter by</param>
        /// <returns>List of hen batch IDs</returns>
        List<Guid> GetHenBatchIds(FilterStockCardsDTO filters, HenStage? henStage = null);

        /// <summary>
        /// Handle common filters for inventory queries
        /// </summary>
        /// <param name="query">Initial query to apply filters</param>
        /// <param name="filters">Filters to apply</param>
        /// <returns>Filtered query</returns>
        IQueryable<Container> HandleFilters(IQueryable<Container> query, FilterStockCardsDTO filters);

        /// <summary>
        /// Get the last closed week consumptions for a list of hen batches
        /// </summary>
        /// <param name="henBatchIds">List of hen batch IDs to get consumptions for</param>
        /// <param name="filters">Optional filters to apply to the query</param>
        /// <returns>List of consumptions for the last closed week for each hen batch</returns>
        List<HenBatchConsumptionDTO> GetLastClosedWeekConsumptions(List<Guid> henBatchIds, FilterStockCardsDTO filters = null, HenStage? henStage = null);

        /// <summary>
        /// Get feed stock indicators with availability dates
        /// </summary>
        /// <param name="filters">Filters to apply to the query</param>
        /// <returns>List of feed stock indicators with availability dates</returns>
        Task<List<FeedStockIndicatorDTO>> GetFeedStockIndicators(FilterStockCardsDTO filters);

        /// <summary>
        /// Get material stock consumption with optional sorting
        /// </summary>
        /// <param name="containers">Query for containers</param>
        /// <param name="consumptions">List of consumptions</param>
        /// <param name="sortBy">Optional sorting parameter</param>
        /// <returns>List of material stock consumption data</returns>
        List<MaterialStockConsumptionDTO> GetMaterialStockConsumption(IQueryable<Container> containers, List<HenBatchConsumptionDTO> consumptions, string sortBy = null);

        #endregion
    }
}
