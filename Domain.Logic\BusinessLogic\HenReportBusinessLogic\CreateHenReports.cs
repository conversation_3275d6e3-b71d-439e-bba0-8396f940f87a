using Binit.Framework;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Interfaces.DAL;
using DAL.Interfaces;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs.HenReportDTOs;
using Domain.Logic.BusinessLogic.HenReportBusinessLogic.ShippingNoteSteps;
using Domain.Logic.Interfaces;
using Domain.Logic.Validations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.HenReportBusinessLogic;

namespace Domain.Logic.BusinessLogic.HenReportBusinessLogic
{
    public class CreateHenReports : ICreateHenReports
    {
        private readonly IUnitOfWork unitOfWork;
        private readonly IHenBatchService henBatchService;
        private readonly IOperationContext operationContext;
        private readonly IMaterialService materialService;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IShippingNoteConciliationService shippingNoteConciliationService;
        private readonly IContainerService<Container> containerService;
        private readonly IHenBatchPerformanceBusinessLogic henBatchPerformanceBusinessLogic;
        private readonly IHenReportService henReportService;
        private readonly IServiceProvider serviceProvider;
        private readonly IStringLocalizer<SharedResources> localizer;
        private List<HenBatch> henBatches;
        private List<Guid> henBatchesIds;
        private List<Container> selectedFeedIntakeOrigins;
        private List<ContainerContainer> availableFeedIntakeOrigins;
        private List<MaterialBatchContainer> materialBatchContainers;
        private List<ShippingNoteConciliation> conciliations;
        private BatchManager manager;

        public CreateHenReports(
            IUnitOfWork unitOfWork,
            IHenBatchService henBatchService,
            IOperationContext operationContext,
            IMaterialService materialService,
            IService<TenantConfiguration> tenantConfigurationService,
            IShippingNoteConciliationService shippingNoteConciliationService,
            IContainerService<Container> containerService,
            IHenBatchPerformanceBusinessLogic henBatchPerformanceBusinessLogic,
            IHenReportService henReportService,
            IServiceProvider serviceProvider,
            IStringLocalizer<SharedResources> localizer)
        {
            this.unitOfWork = unitOfWork;
            this.henBatchService = henBatchService;
            this.operationContext = operationContext;
            this.materialService = materialService;
            this.tenantConfigurationService = tenantConfigurationService;
            this.shippingNoteConciliationService = shippingNoteConciliationService;
            this.containerService = containerService;
            this.henBatchPerformanceBusinessLogic = henBatchPerformanceBusinessLogic;
            this.henReportService = henReportService;
            this.serviceProvider = serviceProvider;
            this.localizer = localizer;
        }

        public class BatchManager
        {
            public List<HenReportBatch> Batches { get; set; }
            public IEnumerable<HenReport> HenReports => Batches.Select(b => b.HenReport);
            public IEnumerable<Guid> Ids => Batches.Select(b => b.HenBatch.Id);
            public IEnumerable<HenBatch> HenBatches => Batches.Select(b => b.HenBatch);
            public IEnumerable<HenReportFeedIntakeDTO> FeedIntakes => Batches.Select(b => b.FeedIntake).Where(b => b != null);
            public IEnumerable<Guid> FeedIntakeOrigins => Batches
                .SelectMany(b => new Guid?[] { b.FeedIntake.FeedIntakeOriginId, b.FeedIntake.FeedIntakeFemaleOriginId, b.FeedIntake.FeedIntakeMaleOriginId })
                .Where(b => b.HasValue)
                .Select(b => b.Value)
                .Distinct();
            public IEnumerable<(Guid henBatch, Guid material)> ClassifiedEggs => Batches.SelectMany(b => b.ClassifiedEggs.Select(ce => (b.HenBatch.Id, ce)));
        }

        public class HenReportBatch
        {
            public HenReportBatch()
            {
                Fixture = new List<HenReportBaseStep>();
            }

            public HenBatch HenBatch { get; set; }
            public HenReport HenReport { get; set; }
            public HenReportFeedIntakeDTO FeedIntake { get; set; }
            public bool HasClassifiedEggs { get; set; }
            public IEnumerable<Guid> ClassifiedEggs => HenReport.ClassifiedEggs.Select(b => b.MaterialId);
            public List<HenReportBaseStep> Fixture { get; set; }
        }

        private void Setup(IEnumerable<HenReport> henReports)
        {
            henBatchesIds = henReports.Select(d => d.HenBatchId).ToList();

            henBatches = henBatchService.GetAll()
                .Where(hb => henBatchesIds.Contains(hb.Id))
                .ToList();
        }

        public async Task<IEnumerable<Guid>> CreateWarehouseHenReports(List<HenBatchHenReportDTO> henReportDTOs, HenReport generalReport, List<HenReportEggsDTO> eggsDTO)
        {
            List<HenReport> reports = new List<HenReport>();
            Guid? henBatchId = default;
            DateTime? henReportDate = default;

            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await AsyncLogic();
            else await unitOfWork.ExecuteAsTransactionAsync(AsyncLogic);

            return reports
                .Where(hr => hr.DeadFemale + hr.DeadMale + hr.DepopulateFemale + hr.DepopulateMale > 0)
                .Select(hr => hr.Id);

            async Task AsyncLogic()
            {
                // build hen reports
                int totalFemaleHens = henReportDTOs.Sum(dto => dto.HenAmountFemale);

                Material[] eggMaterials = new Material[eggsDTO.Count];
                if (eggsDTO.Any())
                {
                    IEnumerable<Guid> eggMaterialsIds = eggsDTO.Select(e => Guid.Parse(e.EggId));
                    eggMaterials = await materialService.GetAll().Include(m => m.MaterialType).Where(m => eggMaterialsIds.Contains(m.Id)).ToArrayAsync();
                }

                foreach (HenBatchHenReportDTO dto in henReportDTOs)
                {
                    HenReport currentReport = new HenReport()
                    {
                        Date = generalReport.Date,
                        DeadFemale = dto.DeadFemale ?? 0,
                        DeadMale = dto.DeadMale ?? 0,
                        DepopulateFemale = dto.DepopulateFemale ?? 0,
                        DepopulateMale = dto.DepopulateMale ?? 0,
                        FeedIntakeFemale = dto.FeedIntakeDTO != null ? (dto.FeedIntakeDTO.FeedIntakeFemale != null && dto.FeedIntakeDTO.FeedIntakeFemale != 0 ?
                                 dto.FeedIntakeDTO.FeedIntakeFemale.Value : (dto.FeedIntakeDTO.FeedIntake.HasValue
                                ? dto.FeedIntakeDTO.FeedIntake.Value * dto.HenAmountFemale / (dto.HenAmountFemale + dto.HenAmountMale)
                                : 0)) : 0,
                        FeedIntakeMale = dto.FeedIntakeDTO != null ? (dto.FeedIntakeDTO.FeedIntakeMale != null && dto.FeedIntakeDTO.FeedIntakeMale != 0 ?
                                 dto.FeedIntakeDTO.FeedIntakeMale.Value : (dto.FeedIntakeDTO.FeedIntake.HasValue
                                ? dto.FeedIntakeDTO.FeedIntake.Value * dto.HenAmountMale / (dto.HenAmountFemale + dto.HenAmountMale)
                                : 0)) : 0,
                        HenBatchId = dto.HenBatchId,
                        Humidity = generalReport.Humidity,
                        MaxTemp = generalReport.MaxTemp,
                        MinTemp = generalReport.MinTemp,
                        ToCageFemale = dto.ToCageFemale ?? 0,
                        ToCageMale = dto.ToCageMale ?? 0,
                        ToFloorFemale = dto.ToFloorFemale ?? 0,
                        ToFloorMale = dto.ToFloorMale ?? 0,
                        WaterChlorineConcentration = generalReport.WaterChlorineConcentration,
                        WaterConsumption = generalReport.WaterConsumption,
                        WaterPh = generalReport.WaterPh,
                        WaterPillQuantity = generalReport.WaterPillQuantity,
                        HenAmountFemale = dto.HenAmountFemale,
                        HenAmountMale = dto.HenAmountMale,
                        ClassifiedEggs = new List<HenReportClassifiedEgg>(),
                        BrokenEggs = totalFemaleHens != 0 ? (uint)(generalReport.BrokenEggs * dto.HenAmountFemale / totalFemaleHens) : 0,
                        UploadOrigin = generalReport.UploadOrigin
                    };
                    // add egg quantity according to female hen amount
                    if (currentReport.HenAmountFemale > 0 && eggsDTO.Any())
                        currentReport.ClassifiedEggs.AddRange(eggsDTO.Select(e =>
                            new HenReportClassifiedEgg()
                            {
                                MaterialId = Guid.Parse(e.EggId),
                                Material = eggMaterials.First(m => m.Id == Guid.Parse(e.EggId)),
                                Quantity = e.Quantity * currentReport.HenAmountFemale / totalFemaleHens
                            }
                        ));
                    if (dto.CasualitiesAndDepopulationsDTO != null)
                        currentReport = dto.CasualitiesAndDepopulationsDTO.FillCasualtiesAndDepopulations(currentReport);

                    reports.Add(currentReport);
                }

                Setup(reports);

                if (totalFemaleHens > 0)
                {
                    // if there are missing eggs add them to a random report
                    int numberOfDistributions = reports.Where(r => r.HenAmountFemale != 0).Count();
                    int randomDistribution = new Random().Next(0, numberOfDistributions - 1);
                    foreach (HenReportEggsDTO egg in eggsDTO)
                    {
                        randomDistribution = new Random().Next(0, numberOfDistributions - 1);
                        reports.Where(r => r.HenAmountFemale != 0).ToList()[randomDistribution].ClassifiedEggs
                                .FirstOrDefault(ce => ce.MaterialId == Guid.Parse(egg.EggId)).Quantity
                        += GetEggsRest(egg.Quantity,
                            reports.SelectMany(r => r.ClassifiedEggs)
                                .Where(ce => ce.MaterialId == Guid.Parse(egg.EggId)).Sum(r => r.Quantity));
                    }
                    // add missing broken eggs
                    reports.Where(r => r.HenAmountFemale != 0).ToList()[randomDistribution].BrokenEggs
                        += (uint)GetEggsRest((int)generalReport.BrokenEggs, (int)reports.Sum(r => r.BrokenEggs));
                }

                foreach (HenReport hr in reports)
                {
                    henBatchId = hr.HenBatchId;
                    henReportDate = hr.Date;

                    uint totalEggs = hr.BrokenEggs + hr.CommercialEggs + hr.HatchableEggs + (uint)hr.ClassifiedEggs.Sum(ce => ce.Quantity);
                    if (totalEggs > 0)
                    {
                        HenBatch henBatch = henBatches.FirstOrDefault(hb => hb.Id == henBatchId.Value);
                        DateTime? firstProductionDate = henBatch.FirstProductionDate;
                        if (!firstProductionDate.HasValue)
                        {
                            henBatch.FirstProductionDate = hr.Date;
                            await henBatchService.UpdateAsync(henBatch);
                        }
                    }

                    // Define the GUID for eliminated/broken eggs
                    Guid eliminatedBrokenEggsGuid = new Guid("7B1BC3D9-B32B-4A42-45D4-08D8FABC0A06");

                    // set total eggs, excluding eliminated/broken eggs from commercial eggs
                    hr.CommercialEggs = (uint)hr.ClassifiedEggs
                        .Where(e => e.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercialPreclasificado)
                              && e.MaterialId != eliminatedBrokenEggsGuid)
                        .Sum(e => e.Quantity);
                    hr.HatchableEggs = (uint)hr.ClassifiedEggs
                        .Where(e => e.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubablePreclasificado))
                        .Sum(e => e.Quantity);

                    // create hen reports
                    //HenBatchHenReportDTO currentDTO = henReportDTOs.FirstOrDefault(dto => dto.HenBatchId == hr.HenBatchId);
                }

                var data = reports.Join(
                    henReportDTOs,
                    r => r.HenBatchId,
                    d => d.HenBatchId,
                    (r, d) => new { r.HenBatchId, r, d.FeedIntakeDTO })
                    .ToDictionary(r => r.HenBatchId, r => (henReport: r.r, feedIntake: r.FeedIntakeDTO));

                await CreateAndAddToHenBatchPerformanceAsync(data);
            }
        }

        private async Task CreateAndAddToHenBatchPerformanceAsync(IDictionary<Guid, (HenReport henReport, HenReportFeedIntakeDTO feedIntake)> data)
        {
            manager = new BatchManager()
            {
                Batches = data.Select(d => new HenReportBatch()
                {
                    HenBatch = henBatches.FirstOrDefault(hb => hb.Id == d.Key),
                    HenReport = d.Value.henReport,
                    FeedIntake = d.Value.feedIntake,
                    HasClassifiedEggs = d.Value.henReport.ClassifiedEggs.Count > 0
                }).ToList()
            };

            if (manager.HenBatches.Any(hb => hb == default))
            {
                throw new ValidationException(localizer[Lang.HenBatchNotFound]);
            }

            //LAYING
            if (manager.HenBatches.Any(hb => hb.HenStage == HenStage.Laying) && manager.Batches.Any(hr => hr.HasClassifiedEggs))
            {
                await HandleLaying();
            }

            //REMOVE HENS
            manager.Batches.ForEach(b =>
            {
                if (b.HenReport.DeadFemale != 0 || b.HenReport.DepopulateFemale != 0 || b.HenReport.DeadMale != 0 || b.HenReport.DepopulateMale != 0)
                {
                    b.Fixture.Add(GetStep<RemoveHensStep>(totalDeadFemale: b.HenReport.DeadFemale + b.HenReport.DepopulateFemale, totalDeadMale: b.HenReport.DepopulateMale + b.HenReport.DeadMale));
                }
            });

            //**********FEED INTAKE**********
            var feedIntakeOriginsQuery = containerService
                .GetAllIgnoringClaims(true)
                .Where(c => manager.FeedIntakeOrigins
                .Contains(c.Id))
                .Select(c => new
                {
                    container = c,
                    hasInconsistencies = c.OriginInconsistencyReports.Any(oir => oir.Status == InconsistencyReportStatusEnum.PendingReview)
                        || c.DestinationInconsistencyReports.Any(dir => dir.Status == InconsistencyReportStatusEnum.PendingReview)
                }).ToList();

            // Validates origin has no pending inconsistencies
            if (feedIntakeOriginsQuery.Any(x => x.hasInconsistencies))
            {
                throw new ValidationException(null, new BusinessValidationResult<ShippingNote>(localizer[Lang.PendingInconsistencyReviews]).UnsuccessfulValidations);
            }
            else
            {
                selectedFeedIntakeOrigins = feedIntakeOriginsQuery.Select(x => x.container).ToList();
            }

            GetMaterialBatchContainers(manager.FeedIntakes);

            availableFeedIntakeOrigins = GetFeedIntakeOrigins(manager.Ids.ToArray()).ToList();

            manager.Batches.ForEach(b =>
            {
                if (b.FeedIntake != null)
                {
                    // Get feed intake step according to the origins, if feed intake famele and male shared the same
                    //origin only add one step, if not add two
                    if (
                        b.FeedIntake.FeedIntakeOriginId.HasValue
                        && (b.HenReport.FeedIntakeFemale + b.HenReport.FeedIntakeMale) > 0
                        && b.FeedIntake.MaterialBatchConsumedId.HasValue)
                    {
                        b.Fixture.Add(GetFeedIntakeStep(b.FeedIntake.FeedIntakeOriginId, b.FeedIntake.MaterialBatchConsumedId, b.HenReport.FeedIntakeFemale + b.HenReport.FeedIntakeMale, HenReportSteps.ConsumedFoodStep));
                    }

                    else if (
                        b.FeedIntake.FeedIntakeOriginId.HasValue
                        && (b.HenReport.FeedIntakeMale > 0 || b.HenReport.FeedIntakeFemale > 0)
                        && !b.FeedIntake.MaterialBatchConsumedId.HasValue)
                    {
                        b.Fixture.Add(GetFeedIntakeStepDifferentMaterialBatch(b.FeedIntake.FeedIntakeOriginId, b.FeedIntake.MaterialBatchFemaleConsumedId,
                                                                           b.FeedIntake.MaterialBatchMaleConsumedId, b.HenReport.FeedIntakeFemale,
                                                                           b.HenReport.FeedIntakeMale, HenReportSteps.ConsumedFoodStep));
                    }

                    else if (b.FeedIntake.FeedIntakeFemaleOriginId.HasValue || b.FeedIntake.FeedIntakeMaleOriginId.HasValue)
                    {
                        if (b.FeedIntake.FeedIntakeFemaleOriginId.HasValue && b.HenReport.FeedIntakeFemale > 0)
                        {
                            //ValidateFeedIntakeOrigins();
                            b.Fixture.Add(GetFeedIntakeStep(b.FeedIntake.FeedIntakeFemaleOriginId, b.FeedIntake.MaterialBatchFemaleConsumedId, b.HenReport.FeedIntakeFemale, HenReportSteps.ConsumedFoodFemaleStep));
                        }
                        if (b.FeedIntake.FeedIntakeMaleOriginId.HasValue && b.HenReport.FeedIntakeMale > 0)
                        {
                            //ValidateFeedIntakeOrigins(b.FeedIntake.FeedIntakeMaleOriginId);
                            b.Fixture.Add(GetFeedIntakeStep(b.FeedIntake.FeedIntakeMaleOriginId, b.FeedIntake.MaterialBatchMaleConsumedId, b.HenReport.FeedIntakeMale, HenReportSteps.ConsumedFoodMaleStep));
                        }
                    }
                }
                else if ((b.HenReport.FeedIntakeFemale + b.HenReport.FeedIntakeMale) > 0)
                {
                    Container feedIntakeOrigin = availableFeedIntakeOrigins
                        .Where(cc => cc.ContainerId == b.HenBatch.Id)
                        .Select(cc => cc.Origin)
                        .FirstOrDefault();

                    if (feedIntakeOrigin == null)
                    {
                        throw new ValidationException(localizer[Lang.FeedIntakeOriginNotFound]);
                    }

                    b.Fixture.Add(GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, feedIntake: b.HenReport.FeedIntakeFemale + b.HenReport.FeedIntakeMale));
                }
            });
            //**********END FEED INTAKE**********

            foreach (var b in manager.Batches)
            {
                b.HenReport.ShippingNotes = new List<ShippingNote>();

                await ExecuteSteps(b.HenReport, b.HenBatch, null, b.Fixture, true);

                b.HenReport.HenBatchPerformanceId = await henBatchPerformanceBusinessLogic.CreateOrUpdateHenBatchPerformance(b.HenReport);

                b.HenReport.ReportEnum = ReportEnum.New;
                b.HenReport.HenAmountFemale = b.HenBatch.HenAmountFemale;
                b.HenReport.HenAmountMale = b.HenBatch.HenAmountMale;

                // set sector, farm and company id for filtering
                b.HenReport.CompanyId = b.HenBatch.CompanyId;
                b.HenReport.FarmId = b.HenBatch.FarmId;
                b.HenReport.SectorId = b.HenBatch.SectorId;

                await henReportService.CreateAsync(b.HenReport);

                // update henbatch values
                b.HenBatch.DeadAccumulatedFemale += b.HenReport.DeadFemale;
                b.HenBatch.DeadAccumulatedMale += b.HenReport.DeadMale;
                b.HenBatch.DepopulateAccumulatedFemale += b.HenReport.DepopulateFemale;
                b.HenBatch.DepopulateAccumulatedMale += b.HenReport.DepopulateMale;

                b.HenBatch.HenReportCreationMinDate = b.HenReport.Date;
                await henBatchService.UpdateAsync(b.HenBatch);

                // Update parent henbatch values.
                if (b.HenBatch.ParentId.HasValue)
                {
                    HenBatch parent = await henBatchService.GetAsync(b.HenBatch.ParentId.Value);

                    parent.DeadAccumulatedFemale += b.HenReport.DeadFemale;
                    parent.DeadAccumulatedMale += b.HenReport.DeadMale;
                    parent.DepopulateAccumulatedFemale += b.HenReport.DepopulateFemale;
                    parent.DepopulateAccumulatedMale += b.HenReport.DepopulateMale;

                    await henBatchService.UpdateAsync(parent);
                }
            }
        }

        private int GetEggsRest(int total, int sum)
        {
            if (total - sum > 0)
                return total - sum;
            return 0;
        }

        private void GetMaterialBatchContainers(IEnumerable<HenReportFeedIntakeDTO> feedIntakes)
        {
            List<KeyValuePair<Guid, Guid>> stock = new List<KeyValuePair<Guid, Guid>>();

            foreach (var feedIntake in feedIntakes)
            {
                if (feedIntake.FeedIntakeOriginId.HasValue)
                {
                    if (feedIntake.MaterialBatchConsumedId.HasValue)
                    {
                        Add(feedIntake.FeedIntakeOriginId.Value, feedIntake.MaterialBatchConsumedId.Value);
                    }
                    if (feedIntake.MaterialBatchFemaleConsumedId.HasValue)
                    {
                        Add(feedIntake.FeedIntakeOriginId.Value, feedIntake.MaterialBatchFemaleConsumedId.Value);
                    }
                    if (feedIntake.MaterialBatchMaleConsumedId.HasValue)
                    {
                        Add(feedIntake.FeedIntakeOriginId.Value, feedIntake.MaterialBatchMaleConsumedId.Value);
                    }
                }
                if (feedIntake.FeedIntakeFemaleOriginId.HasValue)
                {
                    if (feedIntake.MaterialBatchFemaleConsumedId.HasValue)
                    {
                        Add(feedIntake.FeedIntakeFemaleOriginId.Value, feedIntake.MaterialBatchFemaleConsumedId.Value);
                    }
                }

                if (feedIntake.FeedIntakeMaleOriginId.HasValue)
                {
                    if (feedIntake.MaterialBatchMaleConsumedId.HasValue)
                    {
                        Add(feedIntake.FeedIntakeMaleOriginId.Value, feedIntake.MaterialBatchMaleConsumedId.Value);
                    }
                }
            }

            var context = unitOfWork.GetModelDbContext();

            var containers = stock.Select(s => s.Key).ToHashSet();

            materialBatchContainers = context.Set<MaterialBatchContainer>()
                .Include(c => c.MaterialBatch)
                .Where(mbc => containers.Any(s => s == mbc.MaterialContainerContainerId))
                .AsEnumerable()
                .Where(mbc => stock.Any(s => s.Key == mbc.MaterialContainerContainerId && s.Value == mbc.MaterialBatchId))
                .ToList();


            void Add(Guid origin, Guid materialBatch)
            {
                if (!stock.Any(x => x.Key == origin && x.Value == materialBatch))
                {
                    stock.Add(new KeyValuePair<Guid, Guid>(origin, materialBatch));
                }
            }
        }

        private async Task HandleLaying()
        {
            // it is necessary to set the material type, in order to clasify the eggs
            HashSet<Guid> materialsIds = manager.HenReports
                .SelectMany(hr => hr.ClassifiedEggs)
                .Select(ce => ce.MaterialId)
                .ToHashSet();

            //UNIR
            var materialTypes = await materialService.GetAll()
                .Where(mt => materialsIds.Contains(mt.Id))
                .Select(m => new
                {
                    MaterialId = m.Id,
                    MaterialTypePath = m.MaterialType.Path
                })
                .ToListAsync();

            // check tenant configuration
            Guid? tenantId = operationContext.GetUserTenantId();
            List<TenantConfiguration> tenantConfigurations =
                tenantConfigurationService.GetAll(true)
                .Where(tc =>
                    tc.TenantId == tenantId
                    && tc.Value == bool.TrueString
                    && (tc.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeCommercial
                        || tc.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeHatching
                        || tc.TenantConfigurationEnum == TenantConfigurationEnum.AutomaticallySentEggs))
                .ToList();

            bool groupLayingEggsByMaterialTypeCommercial = tenantConfigurations.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeCommercial);
            bool groupLayingEggsByMaterialTypeHatching = tenantConfigurations.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeHatching);
            // see if its necessary to send the eggs to the storage warehouse
            bool automaticallySentEggs = tenantConfigurations.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.AutomaticallySentEggs);

            var materials = manager.ClassifiedEggs.Select(c => c.material);
            conciliations = shippingNoteConciliationService.GetWithShippingNotes()
                .Where(s => s.RemainingQuantity > 0 &&
                    materials.Any(ce => s.MaterialId == ce))
                .AsEnumerable()
                .Where(c => manager.ClassifiedEggs.Any(ce => ce.henBatch == c.ShippingNote.OriginId))
                .ToList();

            // Check if destinations exist for all lines
            var lineIds = manager.Batches.Select(b => b.HenBatch.LineId).ToList();
            var destinations = await containerService.GetAllIgnoringClaims()
                .Include(c => c.OriginContainers)
                .Where(c => c.OriginContainers.Any(oc => lineIds.Contains(oc.OriginId)) &&
                       c.ContainerType == ContainerTypes.StorageWarehouse &&
                       c.Active)
                .ToListAsync();

            bool canSendEggs = destinations.Count == lineIds.Count;

            manager.Batches.ForEach(b =>
            {
                // shipping note conciliation, according to egg type
                List<HenReportEggsDTO> EggsDTO = new List<HenReportEggsDTO>();

                if (b.HasClassifiedEggs)
                {
                    //CONCILIATION
                    b.HenReport.ClassifiedEggs.ForEach(ce =>
                    {
                        (List<HenReportConciliation> shippingNoteConciliations, int remainingEggs) =
                            ConciliateShippingNotes(b.HenBatch.Id, ce.MaterialId, ce.Quantity);

                        b.HenReport.ShippingNoteConciliations.AddRange(shippingNoteConciliations);
                        if (remainingEggs > 0)
                            EggsDTO.Add(new HenReportEggsDTO()
                            {
                                EggId = ce.MaterialId.ToString(),
                                Quantity = remainingEggs,
                                MaterialTypePath = materialTypes.FirstOrDefault(mt => mt.MaterialId == ce.MaterialId)?.MaterialTypePath ?? ""
                            });

                    });

                    // send eggs from the hen batch to the line
                    b.Fixture.Add(GetStep<EggsToLineStep>(
                        EggsDTO: EggsDTO,
                        groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial,
                        groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));

                    if (automaticallySentEggs && canSendEggs)
                    {
                        var destination = destinations.FirstOrDefault(d =>
                            d.OriginContainers.Any(oc => oc.OriginId == b.HenBatch.LineId));

                        if (destination != null)
                        {
                            b.Fixture.Add(GetStep<SendEggsStep>(
                                destination: destination,
                                EggsDTO: EggsDTO,
                                groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial,
                                groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));
                        }
                    }
                }
            });
        }

        /// <summary>
        ///  sees if there is any conciliation shipping note remaining for this batch and relates it
        /// </summary>
        private (List<HenReportConciliation> shippingNoteConciliations, int remainingEggs) ConciliateShippingNotes(Guid henBatchId, Guid eggId, int quantity)
        {
            List<HenReportConciliation> shippingNoteConciliations = new List<HenReportConciliation>();

            foreach (ShippingNoteConciliation sn in conciliations.Where(c => c.ShippingNote.OriginId == henBatchId && c.MaterialId == eggId))
            {
                if (quantity > 0)
                {
                    double conciledQuantity = 0;
                    if (quantity >= sn.RemainingQuantity)
                    {
                        conciledQuantity = sn.RemainingQuantity;
                        quantity -= Convert.ToInt32(sn.RemainingQuantity);
                        sn.RemainingQuantity = 0;
                    }
                    else
                    {
                        conciledQuantity = quantity;
                        sn.RemainingQuantity -= quantity;
                        quantity = 0;
                    }
                    shippingNoteConciliations.Add(new HenReportConciliation()
                    {
                        ShippingNoteConciliation = sn,
                        ConciledQuantity = conciledQuantity
                    });
                }
            }
            return (shippingNoteConciliations, quantity);
        }

        private HenReportBaseStep GetFeedIntakeStep(Guid? feedIntakeOriginId, Guid? materialBatchConsumedId, decimal quantity, string stepName)
        {
            Container feedIntakeOrigin = selectedFeedIntakeOrigins.FirstOrDefault(c => c.Id == feedIntakeOriginId.Value);

            if (feedIntakeOrigin == null)
            {
                throw new ValidationException(localizer[Lang.FeedIntakeOriginNotFound]);
            }

            MaterialBatchContainer materialBatch = materialBatchContainers.FirstOrDefault(mb => mb.MaterialContainerContainerId == feedIntakeOrigin.Id && mb.MaterialBatchId == materialBatchConsumedId);

            return GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, materialBatch: materialBatch, feedIntake: quantity, ConsumedFoodStepName: stepName);
        }

        private async Task ExecuteSteps(HenReport henReport, HenBatch henBatch, HenReport previousHenReport, List<HenReportBaseStep> fixture, bool createAndApprove)
        {
            foreach (HenReportBaseStep step in fixture)
            {
                (BusinessValidationResult<ShippingNote> validationResult, ShippingNote shippingNote) = await step.ProcessShippingNote(henBatch, henReport, previousHenReport, !createAndApprove);
                if (!validationResult.IsValid) throw new ValidationException(null, validationResult.UnsuccessfulValidations);

                if (shippingNote != null) henReport.ShippingNotes.Add(shippingNote);
            }
        }

        private HenReportBaseStep GetFeedIntakeStepDifferentMaterialBatch(Guid? feedIntakeOriginId, Guid? materialBatchFemaleConsumedId, Guid? materialBatchMaleConsumedId, decimal quantityFemale, decimal quantityMale, string stepName)
        {
            Container feedIntakeOrigin = selectedFeedIntakeOrigins.FirstOrDefault(c => c.Id == feedIntakeOriginId.Value);

            if (feedIntakeOrigin == null)
            {
                throw new ValidationException(localizer[Lang.FeedIntakeOriginNotFound]);
            }

            MaterialBatchContainer materialBatchFemale = materialBatchContainers.FirstOrDefault(mb => mb.MaterialContainerContainerId == feedIntakeOrigin.Id && mb.MaterialBatchId == materialBatchFemaleConsumedId);

            MaterialBatchContainer materialBatchMale = materialBatchContainers.FirstOrDefault(mb => mb.MaterialContainerContainerId == feedIntakeOrigin.Id && mb.MaterialBatchId == materialBatchMaleConsumedId);

            ConsumedFoodStep step = GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, materialBatchFemale: materialBatchFemale, materialBatchMale: materialBatchMale, feedIntakeFemale: quantityFemale, feedIntakeMale: quantityMale, ConsumedFoodStepName: stepName);

            return step;
        }

        /// <summary>
        /// Returns containers that have material stock of the formulas associated with the hen batch
        /// </summary>
        private IEnumerable<ContainerContainer> GetFeedIntakeOrigins(params Guid[] henBatches)
        {
            var formulasConsumed = henBatchService.GetAll()
                .Where(hb => henBatches.Contains(hb.Id))
                .Select(hb => new
                {
                    henBatch = hb.Id,
                    formulas = hb.FormulasConsumed.Select(fc => fc.FormulaId)
                }).ToList();


            var context = unitOfWork.GetModelDbContext();

            return context.Set<ContainerContainer>()
                .Where(cc => henBatchesIds.Any(fc =>
                    cc.ContainerId == fc))
                .Select(cc => new
                {
                    cc,
                    materialContainers = cc.Origin.MaterialContainers.Select(mc => new { mc.MaterialId, mc.Quantity }),
                    materialTypes = cc.Origin.AcceptedMaterialType.Select(amt => new { amt.MaterialType.Path, amt.ActionEnum })
                })
                .AsEnumerable()
                .Where(cc => formulasConsumed.Any(fc =>
                    (cc.materialContainers.Any(mc => fc.formulas.Contains(mc.MaterialId) && mc.Quantity > 0)
                    || cc.materialTypes.Any(amt => MaterialTypePaths.InsumoMateriaPrimaAlimentacionFormula.Contains(amt.Path) && (amt.ActionEnum == ActionsEnum.Produce || amt.ActionEnum == ActionsEnum.ConsumeAndProduce)))))
                .Select(cc => cc.cc);
        }

        public TStep GetStep<TStep>(Container destination = null, Formula formula = null,
            Container feedIntakeOrigin = null, MaterialBatchContainer materialBatch = null,
            List<HenReportEggsDTO> EggsDTO = null, MaterialBatchContainer materialBatchFemale = null,
            MaterialBatchContainer materialBatchMale = null, decimal? feedIntake = 0, decimal? feedIntakeFemale = 0,
            decimal? feedIntakeMale = 0, int totalDeadFemale = 0, int totalDeadMale = 0, bool groupLayingEggsByMaterialTypeHatching = false,
             bool groupLayingEggsByMaterialTypeCommercial = false, string ConsumedFoodStepName = "") where TStep : HenReportBaseStep
        {
            TStep step = serviceProvider.GetService<TStep>();
            if (destination != null)
                step.SetOptionalDestination(destination);
            if (formula != null)
                step.SetFeedIntakeOrigin(formula);
            if (feedIntakeOrigin != null)
                step.SetFeedIntakeOrigin(feedIntakeOrigin);
            if (materialBatch != null)
                step.SetMaterialBatchConsumed(materialBatch);
            if (materialBatchFemale != null)
                step.SetMaterialBatchFemaleConsumed(materialBatchFemale);
            if (materialBatchMale != null)
                step.SetMaterialBatchMaleConsumed(materialBatchMale);
            if (!string.IsNullOrEmpty(ConsumedFoodStepName))
                step.SetFoodConsumedStepName(ConsumedFoodStepName);
            if (EggsDTO != null)
                step.SetEggsQuantity(EggsDTO);
            if (feedIntake.Value != 0)
                step.SetFeedIntake(feedIntake.Value);
            if (feedIntakeFemale.Value != 0)
                step.SetFeedIntakeFemale(feedIntakeFemale.Value);
            if (feedIntakeMale.Value != 0)
                step.SetFeedIntakeMale(feedIntakeMale.Value);
            step.SetDeadHensQuantities(totalDeadFemale, totalDeadMale);
            step.SetEggsBool(groupLayingEggsByMaterialTypeHatching, groupLayingEggsByMaterialTypeCommercial);

            return step;
        }

        public async Task<IEnumerable<HenReportResultDTO>> CreateHenReportsFromTable(DateTime reportDate, List<HenReportFromTableDTO> henReportDTOs)
        {
            var reports = new List<HenReport>(); // for setup and results

            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await AsyncLogic();
            else await unitOfWork.ExecuteAsTransactionAsync(AsyncLogic);

            // return the created reports
            return reports
                .Select(hr => new HenReportResultDTO()
                {
                    Id = hr.Id,
                    TotalDeaths = hr.DeadFemale + hr.DeadMale,
                    TotalDepopulations = hr.DepopulateFemale + hr.DepopulateMale
                });

            async Task AsyncLogic()
            {
                var reportsData = new Dictionary<Guid, (HenReport henReport, HenReportFeedIntakeDTO feedIntake)>();

                foreach (HenReportFromTableDTO dto in henReportDTOs)
                {
                    HenReport currentReport = new HenReport()
                    {
                        HenBatchId = dto.HenBatchId,
                        Date = reportDate,
                        DeadFemale = dto.DeadFemale,
                        DeadMale = dto.DeadMale,
                        FeedIntakeFemale = dto.FeedIntakeFemale,
                        FeedIntakeMale = dto.FeedIntakeMale,
                        Humidity = dto.Humidity,
                        MaxTemp = dto.MaxTemp,
                        MinTemp = dto.MinTemp,
                        WaterChlorineConcentration = dto.WaterChlorineConcentration,
                        WaterConsumption = dto.WaterConsumption,
                        WaterPh = dto.WaterPh,
                        WaterPillQuantity = (int)dto.WaterPillQuantity,
                        UploadOrigin = ReportUploadEnum.WebApp,
                        HenAmountFemale = dto.HenAmountFemale,
                        HenAmountMale = dto.HenAmountMale,
                        ClassifiedEggs = new List<HenReportClassifiedEgg>(),
                    };

                    HenReportFeedIntakeDTO feedIntake = new HenReportFeedIntakeDTO()
                    {
                        HenBatchId = dto.HenBatchId,
                        FeedIntakeFemale = dto.FeedIntakeFemale,
                        FeedIntakeMale = dto.FeedIntakeMale,
                        FeedIntakeFemaleOriginId = dto.FeedIntakeFemaleOriginId,
                        FeedIntakeMaleOriginId = dto.FeedIntakeMaleOriginId,
                        // TODO: check if material batch consumed is needed here
                    };

                    // Process ClassifiedEggs
                    if (dto.ClassifiedEggs != null && dto.ClassifiedEggs.Count > 0)
                    {
                        // Load the materials to set the Material property
                        var materialIds = dto.ClassifiedEggs.Select(e => e.MaterialId).ToList();
                        var materials = await materialService.GetAll()
                            .Where(m => materialIds.Contains(m.Id))
                            .ToListAsync();

                        foreach (var eggDTO in dto.ClassifiedEggs)
                        {
                            var material = materials.FirstOrDefault(m => m.Id == eggDTO.MaterialId);
                            if (material != null)
                            {
                                currentReport.ClassifiedEggs.Add(new HenReportClassifiedEgg
                                {
                                    MaterialId = eggDTO.MaterialId,
                                    Material = material,
                                    Quantity = eggDTO.Quantity
                                });
                            }
                        }
                    }

                    reportsData.Add(currentReport.HenBatchId, (currentReport, feedIntake));

                    reports.Add(currentReport); // for setup
                }

                Setup(reports);

                await CreateAndAddToHenBatchPerformanceAsync(reportsData);
            }
        }

        public async Task<IEnumerable<HenReportResultDTO>> CreateBreedingReportsFromTable(DateTime reportDate, List<BreedingReportFromTableDTO> henReportDTOs)
        {
            var reports = new List<HenReport>(); // for setup and results

            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await AsyncLogic();
            else await unitOfWork.ExecuteAsTransactionAsync(AsyncLogic);

            // return the created reports
            return reports
                .Select(hr => new HenReportResultDTO()
                {
                    Id = hr.Id,
                    TotalDeaths = hr.DeadFemale + hr.DeadMale,
                    TotalDepopulations = hr.DepopulateFemale + hr.DepopulateMale
                });

            async Task AsyncLogic()
            {
                var reportsData = new Dictionary<Guid, (HenReport henReport, HenReportFeedIntakeDTO feedIntake)>();

                foreach (BreedingReportFromTableDTO dto in henReportDTOs)
                {
                    HenReport currentReport = new HenReport()
                    {
                        HenBatchId = dto.HenBatchId,
                        Date = reportDate,
                        DeadFemale = dto.DeadFemale,
                        DeadMale = dto.DeadMale,
                        FeedIntakeFemale = dto.FeedIntakeFemale,
                        FeedIntakeMale = dto.FeedIntakeMale,
                        Humidity = dto.Humidity,
                        MaxTemp = dto.MaxTemp,
                        MinTemp = dto.MinTemp,
                        WaterChlorineConcentration = dto.WaterChlorineConcentration,
                        WaterConsumption = dto.WaterConsumption,
                        WaterPh = dto.WaterPh,
                        WaterPillQuantity = (int)dto.WaterPillQuantity,
                        UploadOrigin = ReportUploadEnum.WebApp,
                        HenAmountFemale = dto.HenAmountFemale,
                        HenAmountMale = dto.HenAmountMale,
                        ClassifiedEggs = new List<HenReportClassifiedEgg>(),
                    };

                    HenReportFeedIntakeDTO feedIntake = new HenReportFeedIntakeDTO()
                    {
                        HenBatchId = dto.HenBatchId,
                        FeedIntakeFemale = dto.FeedIntakeFemale,
                        FeedIntakeMale = dto.FeedIntakeMale,
                        FeedIntakeFemaleOriginId = dto.FeedIntakeFemaleOriginId,
                        FeedIntakeMaleOriginId = dto.FeedIntakeMaleOriginId,
                    };

                    reportsData.Add(currentReport.HenBatchId, (currentReport, feedIntake));

                    reports.Add(currentReport); // for setup
                }

                Setup(reports);

                await CreateAndAddToHenBatchPerformanceAsync(reportsData);
            }
        }

        public async Task<IEnumerable<HenReportResultDTO>> CreateLayingReportsFromTable(DateTime reportDate, List<LayingReportFromTableDTO> henReportDTOs)
        {
            var reports = new List<HenReport>(); // for setup and results

            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await AsyncLogic();
            else await unitOfWork.ExecuteAsTransactionAsync(AsyncLogic);

            // return the created reports
            return reports
                .Select(hr => new HenReportResultDTO()
                {
                    Id = hr.Id,
                    TotalDeaths = hr.DeadFemale + hr.DeadMale,
                    TotalDepopulations = hr.DepopulateFemale + hr.DepopulateMale
                });

            async Task AsyncLogic()
            {
                var reportsData = new Dictionary<Guid, (HenReport henReport, HenReportFeedIntakeDTO feedIntake)>();

                // Validate egg production after first production date
                foreach (var dto in henReportDTOs)
                {
                    // Get the hen batch to check first production date
                    var henBatch = henBatchService.Get(dto.HenBatchId);

                    // Check if first production date is defined and is before or equal to the report date
                    if (henBatch.FirstProductionDate.HasValue && reportDate >= henBatch.FirstProductionDate.Value)
                    {
                        // Calculate total eggs from all egg types
                        int totalEggs = dto.CleanNestEggs + dto.DirtyNestEggs + dto.BedEggs +
                                       dto.DoubleYolkEggs + dto.SmallEggs + dto.DefectiveEggs +
                                       dto.DirtyRolledEggs + dto.CrackedEggs + dto.ThinShellEggs +
                                       dto.EliminatedBrokenEggs;

                        // Also include eggs from ClassifiedEggs collection if present
                        if (dto.ClassifiedEggs != null && dto.ClassifiedEggs.Any())
                        {
                            totalEggs += dto.ClassifiedEggs.Sum(e => e.Quantity);
                        }
                    }
                }

                foreach (LayingReportFromTableDTO dto in henReportDTOs)
                {
                    HenReport currentReport = new HenReport()
                    {
                        HenBatchId = dto.HenBatchId,
                        Date = reportDate,
                        DeadFemale = dto.DeadFemale,
                        DeadMale = dto.DeadMale,
                        FeedIntakeFemale = dto.FeedIntakeFemale,
                        FeedIntakeMale = dto.FeedIntakeMale,
                        Humidity = dto.Humidity,
                        MaxTemp = dto.MaxTemp,
                        MinTemp = dto.MinTemp,
                        WaterChlorineConcentration = dto.WaterChlorineConcentration,
                        WaterConsumption = dto.WaterConsumption,
                        WaterPh = dto.WaterPh,
                        WaterPillQuantity = (int)dto.WaterPillQuantity,
                        UploadOrigin = ReportUploadEnum.WebApp,
                        HenAmountFemale = dto.HenAmountFemale,
                        HenAmountMale = dto.HenAmountMale,
                        ClassifiedEggs = new List<HenReportClassifiedEgg>(),
                    };

                    HenReportFeedIntakeDTO feedIntake = new HenReportFeedIntakeDTO()
                    {
                        HenBatchId = dto.HenBatchId,
                        FeedIntakeFemale = dto.FeedIntakeFemale,
                        FeedIntakeMale = dto.FeedIntakeMale,
                        FeedIntakeFemaleOriginId = dto.FeedIntakeFemaleOriginId,
                        FeedIntakeMaleOriginId = dto.FeedIntakeMaleOriginId,
                    };

                    // Process ClassifiedEggs
                    if (dto.ClassifiedEggs != null && dto.ClassifiedEggs.Count > 0)
                    {
                        // Load the materials to set the Material property
                        var materialIds = dto.ClassifiedEggs.Select(e => e.MaterialId).ToList();
                        var materials = await materialService.GetAll()
                            .Include(m => m.MaterialType)
                            .Where(m => materialIds.Contains(m.Id))
                            .ToListAsync();

                        foreach (var eggDTO in dto.ClassifiedEggs)
                        {
                            var material = materials.FirstOrDefault(m => m.Id == eggDTO.MaterialId);
                            if (material != null)
                            {
                                currentReport.ClassifiedEggs.Add(new HenReportClassifiedEgg
                                {
                                    MaterialId = eggDTO.MaterialId,
                                    Material = material,
                                    Quantity = eggDTO.Quantity
                                });
                            }
                        }

                        // Define the GUID for eliminated/broken eggs
                        Guid eliminatedBrokenEggsGuid = new Guid("7B1BC3D9-B32B-4A42-45D4-08D8FABC0A06");

                        // Set the egg totals based on the classified eggs, excluding eliminated/broken eggs
                        currentReport.CommercialEggs = (uint)currentReport.ClassifiedEggs
                            .Where(e => e.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercialPreclasificado)
                                  && e.MaterialId != eliminatedBrokenEggsGuid)
                            .Sum(e => e.Quantity);

                        currentReport.HatchableEggs = (uint)currentReport.ClassifiedEggs
                            .Where(e => e.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubablePreclasificado))
                            .Sum(e => e.Quantity);

                        // Set BrokenEggs from eliminatedBrokenEggs in ClassifiedEggs
                        var eliminatedBrokenEgg = currentReport.ClassifiedEggs
                            .FirstOrDefault(e => e.MaterialId == eliminatedBrokenEggsGuid);

                        if (eliminatedBrokenEgg != null)
                        {
                            currentReport.BrokenEggs = (uint)eliminatedBrokenEgg.Quantity;
                        }
                    }

                    reportsData.Add(currentReport.HenBatchId, (currentReport, feedIntake));

                    reports.Add(currentReport); // for setup
                }

                Setup(reports);

                // Get all hen batches involved in this report
                var henBatchIds = reports.Select(r => r.HenBatchId).Distinct().ToList();
                var henBatches = await henBatchService.GetAll()
                    .Where(hb => henBatchIds.Contains(hb.Id))
                    .ToListAsync();

                // Check for first egg production and update FirstProductionDate if needed
                foreach (var report in reports)
                {
                    // Calculate total eggs for this report
                    uint totalEggs = report.BrokenEggs + report.CommercialEggs + report.HatchableEggs + (uint)report.ClassifiedEggs.Sum(ce => ce.Quantity);

                    // If there are eggs and the hen batch doesn't have a first production date yet, set it
                    if (totalEggs > 0)
                    {
                        var henBatch = henBatches.FirstOrDefault(hb => hb.Id == report.HenBatchId);
                        if (henBatch != null && !henBatch.FirstProductionDate.HasValue)
                        {
                            henBatch.FirstProductionDate = report.Date;
                            await henBatchService.UpdateAsync(henBatch);
                        }
                    }
                }

                await CreateAndAddToHenBatchPerformanceAsync(reportsData);
            }
        }
    }
}