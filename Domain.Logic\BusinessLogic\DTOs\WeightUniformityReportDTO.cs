using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Domain.Entities.Model;

namespace Domain.Logic.BusinessLogic.DTOs
{
    public class WeightComparisonReportDTO
    {
        public Guid Id { get; set; } //HenBatchId
        public int HenAmountMale { get; set; }
        public int InitialHenAmountMale { get; set; }
        public int HenAmountFemale { get; set; }
        public int InitialHenAmountFemale { get; set; }

        public string Farm { get; set; }
        public string ParentBatch { get; set; }
        public string ChildBatch { get; set; }
        public string CategoryName { get; set; }
        public string LineName { get; set; }
        public string WarehouseName { get; set; }

        public string PerformanceGender { get; set; }
        public int DeadAccumulatedMale { get; set; }
        public int DeadAccumulatedFemale { get; set; }
        public int Week { get; set; }
        public DateTime? Date { get; set; }
        public decimal StandardWeightMale { get; set; }
        public decimal StandardWeightFemale { get; set; }
        public decimal ActualWeightMale { get; set; }
        public decimal ActualWeightFemale { get; set; }
        public decimal WeightDifferenceMale { get; set; }
        public decimal WeightDifferenceFemale { get; set; }
        public decimal WeightPercentageMale { get; set; }
        public decimal WeightPercentageFemale { get; set; }
        public decimal UniformityMale { get; set; }
        public decimal UniformityFemale { get; set; }
    }

    public class WeightUniformityReportDTO
    {
        public string Farm { get; set; }
        public string Batch { get; set; }
        public string Distribution { get; set; }
        public string BatchStatus { get; set; }

        public int HenAmount { get; set; }
        public int InitialHenAmount { get; set; }
        public DateTime? HenReportCreationMinDate { get; set; }

        public List<ReportExcelRowDTO> ReportExcelRows { get; set; }
        public List<WeightComparisonReportDTO> DetailedWeightComparison { get; set; }

        public WeightUniformityReportDTO()
        {
            ReportExcelRows = new List<ReportExcelRowDTO>();
        }
        public class ReportExcelRowDTO
        {
            [Display(Name = "Data")]
            public DateTime Data { get; set; }

            [Display(Name = "Idade")]
            public int Idade { get; set; }

            [Display(Name = "Boxe")]
            public string Boxe { get; set; }

            [Display(Name = "Genética")]
            public string Genetica { get; set; }

            [Display(Name = "Peso Real (g)")]
            public decimal PesoReal { get; set; }

            [Display(Name = "Peso Padrão (g)")]
            public decimal PesoPadrao { get; set; }

            [Display(Name = "Relação Peso Real/Padrão (%)")]
            public decimal RelacaoPeso { get; set; }

            [Display(Name = "(%)")]
            public decimal Uniformidade { get; set; }

            public ReportExcelRowDTO()
            {

            }
        }
    }

    public class WeightUniformityReportFilterDTO
    {
        public Guid Farm { get; set; }
        public Guid ParentHenBatch { get; set; }
        public bool? Active { get; set; }
        public HenStage HenStage { get; set; }
    }
}