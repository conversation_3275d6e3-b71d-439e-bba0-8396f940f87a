@model WebApp.Models.AnalyticReportViewModel
@using Binit.Framework
@using Domain.Entities.Model.Enum
@using Microsoft.Extensions.Localization
@using System.Globalization;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.AnalyticReport.CreateOrEdit
@inject IStringLocalizer<SharedResources> localizer

@{
    var action = ViewData["Action"] as string;
    var submitLabel = action == "Create" ? localizer[Lang.BtnCreate] : localizer[Lang.BtnUpdate];
    var areas = ViewData["Areas"] as List<SelectListItem>;
    var editors = ViewData["EditorsUsers"] as List<SelectListItem>;
    var viewers = ViewData["ViewersUsers"] as List<SelectListItem>;
    var categories = ViewData["Categories"] as List<SelectListItem>;
    var userIsAdmin = (bool)ViewData["UserIsAdmin"];
    string lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
    bool configuration = true;
    string loadAction = ViewData["LoadAction"] as string;
}

<form id="analytics-form" class="floating-labels" method="POST">
    <input type="hidden" asp-for="Id" />
    <div asp-validation-summary="ModelOnly" class="m-b-40 text-danger"></div>

    <div class="row align-items-end">
        <div class="col-md-5">
            <ignite-input for-property="Name"></ignite-input>
        </div>
        <div class="col-md-5">
            <ignite-dropdown for-property="Category" items="@categories"></ignite-dropdown>
        </div>
    </div>

    @if (!Model.Area.HasValue)
    {
        <div class="row align-items-end">

            <div class="col-md-5">
                <ignite-dropdown for-property="Area"
                                 items="@areas">
                </ignite-dropdown>
            </div>
        </div>
    }
    else
    {
        <input type="hidden" asp-for="Area" />
    }

    @{ if (userIsAdmin)
        {
            <h5>@localizer[Lang.TypeSwitchesLabel]</h5>
            <div class="row align-items-end">

                <div class="col-md-2" id="corporateCheckBox">
                    <ignite-checkbox for-property="Corporate" color="#29313e" css-classes="corporate-checkbox"></ignite-checkbox>
                </div>
                <div class="col-md-2" id="personalCheckBox">
                    <ignite-checkbox for-property="Personal" color="#29313e" css-classes="personal-checkbox"></ignite-checkbox>
                </div>
            </div>
        }
        else
        {
            <input type="hidden" asp-for="Corporate" />
            <input type="hidden" asp-for="Personal" />
        }
    }
    <h5>@localizer[Lang.ShowSwitchesLabel]</h5>
    <div class="row align-items-end">

        <div class="col-md-2">
            <ignite-checkbox for-property="Table" color="#29313e" css-classes="inconsistency-checkbox"></ignite-checkbox>
        </div>
        <div class="col-md-2">
            <ignite-checkbox for-property="Chart" color="#29313e" css-classes="inconsistency-checkbox"></ignite-checkbox>
        </div>
    </div>


    <h5>@localizer[Lang.UsersLabel]</h5>
    <div class="row align-items-end">

        <div class="col-md-5">
            <ignite-dropdown for-property="Editors"
                             items="@editors"
                             multiple="true">
            </ignite-dropdown>
        </div>
        <div class="col-md-5">
            <ignite-dropdown for-property="Viewers"
                             items="@viewers"
                             multiple="true">
            </ignite-dropdown>
        </div>
    </div>

</form>

@await Component.InvokeAsync("PivotGridTable", new { configuration, loadAction, Model.Area, configurationName = Model.Name, configJson = Model.ConfigurationJson })

<div class="d-flex justify-content-end">
    <button type="button" class="btn btn-secondary mr-2"
            onclick="window.location.href='@Url.Action("Index","Analytics",new { area = Model.Area })'">
        @localizer[Lang.BtnCancel]
    </button>
    <button id="create" type="submit" class="btn btn-themecolor">@submitLabel</button>
</div>

<ignite-load plugins="select2,switchery"></ignite-load>

@section Scripts {
    <script src="@Url.Content("~/js/views/analyticReport/createOrEdit.js")" type="text/javascript"></script>
    <script>
        const pivotGridResources = @Json.Serialize(ViewData["PivotGridResources"]);
        const configuration = Boolean('@configuration');
        const area = '@Model.Area';
    </script>
    <script src="~/js/views/analyticReport/pivot-grid.js"></script>
    <script src="~/js/views/analyticReport/configuration.js"></script>
    <script src="~/js/devextreme/localization/dx.messages.es.js"></script>
    <script src="~/js/devextreme/localization/dx.messages.pt.js"></script>
    <script>
        DevExpress.localization.locale('@lang');
    </script>
}