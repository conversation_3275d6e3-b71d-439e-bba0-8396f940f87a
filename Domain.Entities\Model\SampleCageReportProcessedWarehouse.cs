using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Entities.Model;

namespace Domain.Entities
{
    /// <summary>
    /// Represents a warehouse that has been processed in a sample cage report
    /// </summary>
    [Table("SampleCageReportProcessedWarehouse")]
    public class SampleCageReportProcessedWarehouse
    {
        /// <summary>
        /// Primary key
        /// </summary>
        [Key]
        public Guid Id { get; set; }

        /// <summary>
        /// The date of the report
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// The hen batch ID
        /// </summary>
        public Guid HenBatchId { get; set; }

        /// <summary>
        /// The warehouse ID that has been processed
        /// </summary>
        public Guid WarehouseId { get; set; }

        /// <summary>
        /// Navigation property to the hen batch
        /// </summary>
        [ForeignKey("HenBatchId")]
        public virtual HenBatch HenBatch { get; set; }
    }
}
