using Binit.Framework;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers.DAL;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Binit.Shaper.Interfaces.Services;
using DAL.Interfaces;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Entities.Model.Views;
using Domain.Logic.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.Services.HenBatchService;

namespace Domain.Logic.Services
{
    /// <summary>
    /// HenBatch specific services.
    /// </summary>
    public class HenBatchService : ContainerService<HenBatch>, IHenBatchService
    {
        private readonly IServiceTenantDependent<ShippingNote> shippingNoteService;

        public HenBatchService(IExceptionManager exceptionManager, ILogger logger, IOperationContext
            operationContext, IUnitOfWork unitOfWork, IStringLocalizer<SharedResources> localizer, IMediator
            mediator, IFarmService farmService, IClusterService clusterService, IGeneticService geneticService,
            IServiceProvider provider, IMaterialTypeService materialTypeService, IInconsistencyReportService iInconsistencyReportService,
            IServiceTenantDependent<TenantDependentEntityFile> fileService, ISectorService sectorService,
            IAliasExtensionService aliasExtensionService, IService<TenantConfiguration> tenantConfigurationService,
            IServiceTenantDependent<ShippingNote> shippingNoteService) : base(
                exceptionManager,
                logger,
                operationContext,
                unitOfWork,
                localizer,
                mediator,
                farmService,
                clusterService,
                geneticService,
                iInconsistencyReportService,
                provider,
                materialTypeService,
                fileService,
                sectorService,
                aliasExtensionService,
                tenantConfigurationService)
        {
            this.shippingNoteService = shippingNoteService;
        }

        /// <summary>
        /// Asynchronously retrieves henbatch distributions by code.
        /// </summary>
        public async Task<IQueryable<HenBatch>> GetDistributionsByCodeAsync(string code)
        {
            IQueryable<HenBatch> distributions = GetAll().Where(hb => hb.Code.ToUpper() == code.ToUpper() && hb.LineId.HasValue);

            if (!await distributions.AnyAsync())
                throw new NotFoundException(localizer[Lang.GetFullNotFoundEx]);

            return distributions;
        }

        /// <summary>
        /// Returns a hen batch by Id including all its relationships.
        /// </summary>
        public new HenBatch GetFull(Guid id)
        {
            HenBatch henBatch = base.GetAll(useDefaultSorting: false)
           .Where(hb => hb.Id == id)
           .Include(hb => hb.Company)
           .Include(hb => hb.Line).ThenInclude(l => l.Warehouse).ThenInclude(w => w.Cluster).ThenInclude(c => c.Farm)
           .Include(hb => hb.Line).ThenInclude(l => l.MaterialContainers)
           .Include(hb => hb.Genetic)
           .Include(hb => hb.Category)
           .Include(hb => hb.Reports)
           .Include(hb => hb.SampleCages)
           .Include(hb => hb.FormulasConsumed).ThenInclude(oc => oc.Formula)
           .Include(hb => hb.OriginContainers).ThenInclude(oc => oc.Origin).ThenInclude(c => c.AcceptedMaterialType)
           .Include(hb => hb.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
           .Include(hb => hb.EggMaterial).ThenInclude(em => em.MaterialType)
           .Include(hb => hb.AreaContainers)
           .Include(hb => hb.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType)
           .Include(hb => hb.AcceptedMaterialType).ThenInclude(amt => amt.CapacityUnit)
           .Include(hb => hb.Farm)
           .FirstOrDefault();

            if (henBatch == null || henBatch.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]));

            henBatch.Files = fileService.GetAll().Where(f => f.TenantDependentEntityId == henBatch.Id).ToList();

            return henBatch;
        }

        /// <summary>
        /// Returns a hen batch by Id including its materials.
        /// </summary>
        public HenBatch GetWithMaterials(Guid id)
        {
            HenBatch henBatch = base.GetAll()
           .Where(hb => hb.Id == id)
           .Include(hb => hb.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
           .FirstOrDefault();

            if (henBatch == null || henBatch.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]));

            return henBatch;
        }

        /// <summary>
        /// Returns a hen batch by Id including all its reports.
        /// </summary>
        public HenBatch GetWithReports(Guid id)
        {
            HenBatch henBatch = base.GetAll(useDefaultSorting: false)
           .Where(hb => hb.Id == id)
           .Include(hb => hb.Reports)
           .FirstOrDefault();

            if (henBatch == null || henBatch.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]));

            return henBatch;
        }

        public IQueryable<HenBatch> GetAll(bool asNoTracking = false, bool useDefaultSorting = true, HenBatchTypeEnum? henBatchType = null)
        {
            IQueryable<HenBatch> henBatches = base.GetAll(asNoTracking, useDefaultSorting);

            return henBatchType switch
            {
                HenBatchTypeEnum.Parent => henBatches.Where(hb => !hb.LineId.HasValue),
                HenBatchTypeEnum.Child => henBatches.Where(hb => hb.LineId.HasValue),
                _ => henBatches
            };
        }

        public IQueryable<HenBatch> GetAllIgnoringClaims(bool asNoTracking = false, bool useDefaultSorting = true, HenBatchTypeEnum? henBatchType = null)
        {
            IQueryable<HenBatch> henBatches = base.GetAllIgnoringClaims(asNoTracking, useDefaultSorting);

            return henBatchType switch
            {
                HenBatchTypeEnum.Parent => henBatches.Where(hb => !hb.LineId.HasValue),
                HenBatchTypeEnum.Child => henBatches.Where(hb => hb.LineId.HasValue),
                _ => henBatches
            };
        }

        /// <summary>
        /// Returns all active hen batches.
        /// </summary>
        public IQueryable<HenBatch> GetAllActive(HenBatchTypeEnum? henBatchType = null)
        {
            return GetAll(henBatchType: henBatchType)
                .Include(hb => hb.Genetic)
                .Include(hb => hb.FormulasConsumed).ThenInclude(f => f.Formula)
                .Where(hb => !hb.DateEnd.HasValue);
        }

        public IQueryable<HenBatch> GetAllWithInitialHenAmountGreaterThanZero(HenBatchTypeEnum? henBatchType = null)
        {
            return GetAll(henBatchType: henBatchType)
                .Where(hb => !hb.DateEnd.HasValue && (hb.InitialHenAmountFemale + hb.InitialHenAmountMale) > 0);
        }

        /// <summary>
        /// Returns all hen batches including all its relationships.
        /// </summary>
        public IQueryable<HenBatch> GetAllFull(HenStage? henStage = null, bool? active = null, HenBatchTypeEnum? henBatchType = null)
        {
            IQueryable<HenBatch> batches = GetAll(useDefaultSorting: true, henBatchType: henBatchType)
                .Include(hb => hb.Farm)
                .Include(hb => hb.Line)
                    .ThenInclude(l => l.Warehouse)
                        .ThenInclude(w => w.Cluster)
                            .ThenInclude(c => c.Farm)
                .Include(hb => hb.Genetic)
                .Include(hb => hb.Reports)
                .Include(hb => hb.SampleCages)
                .Include(hb => hb.HenBatchPerformances)
                .Include(hb => hb.MaterialContainers)
                .Include(hb => hb.Category)
                .Include(hb => hb.FormulasConsumed).ThenInclude(f => f.Formula)
                .Include(hb => hb.EggMaterial).ThenInclude(em => em.MaterialType)
                .AsQueryable();

            if (henStage.HasValue)
                batches = batches.Where(hb => hb.HenStage == henStage.Value);

            if (active.HasValue)
                batches = active.Value switch
                {
                    true => batches.Where(hb => !hb.DateEnd.HasValue),
                    _ => batches.Where(hb => hb.DateEnd.HasValue)
                };

            return batches;
        }

        /// <summary>
        /// Returns all children hen batches including all its relationships.
        /// </summary>
        public IQueryable<HenBatch> GetChildreFull(Guid parentId)
        {
            return GetAll(useDefaultSorting: true)
                .Include(hb => hb.Line)
                    .ThenInclude(l => l.Warehouse)
                .Include(hb => hb.OriginContainers)
                .Include(hb => hb.AcceptedMaterialType)
                    .ThenInclude(am => am.MaterialType)
                .Include(hb => hb.MaterialContainers)
                    .ThenInclude(mc => mc.Material)
                        .ThenInclude(m => m.MaterialType)
                .Where(hb => hb.ParentId == parentId);
        }

        /// <summary>
        /// Returns all children hen batches or the unique distribution
        /// </summary>
        public IQueryable<HenBatch> GetAllChildrenOrUniqueDistribution(Guid parentId)
        {
            var query = GetAll(useDefaultSorting: true)
                .Include(hb => hb.Line)
                    .ThenInclude(l => l.Warehouse)
                .Include(hb => hb.MaterialContainers)
                    .ThenInclude(mc => mc.Material)
                        .ThenInclude(m => m.MaterialType);

            IQueryable<HenBatch> henBatches = query.Where(hb => hb.ParentId == parentId);
            henBatches = henBatches.Any() ? henBatches : query.Where(hb => hb.Id == parentId);

            return henBatches;
        }

        /// <summary>
        /// Returns all hen batches with it's farm and genetic
        /// </summary>
        public IQueryable<HenBatch> GetAllWithFarmAndGenetic(HenStage? henStage = null, bool active = false, bool asNoTracking = false, bool useDefaultSorting = false)
        {
            IQueryable<HenBatch> batches = base.GetAll(useDefaultSorting: useDefaultSorting, asNoTracking: asNoTracking)
                .Include(hb => hb.Farm)
                .Include(hb => hb.Genetic);

            if (henStage.HasValue)
                batches = batches.Where(hb => hb.HenStage == henStage.Value);

            if (active)
                batches = batches.Where(hb => !hb.DateEnd.HasValue);

            return batches;
        }

        /// <summary>
        /// Returns all hen batches filtered by hen stage including all its relationships.
        /// </summary>
        public IQueryable<HenBatch> GetByHenStage(HenStage henStage) => GetAllFull().Where(hb => hb.HenStage == henStage);

        /// <summary>
        /// Return asynchronously a hen batch by Id including all its relationships.
        /// </summary>
        public async Task<HenBatch> GetFullAsync(Guid id, bool asNoTracking = false)
        {
            HenBatch henBatch = await base.GetAll(asNoTracking)
           .Where(hb => hb.Id == id)
           .Include(hb => hb.Line).ThenInclude(l => l.Warehouse)
           .Include(hb => hb.Genetic)
           .Include(hb => hb.SampleCages)
           .Include(hw => hw.AreaContainers)
           .Include(hw => hw.OriginContainers)
           .Include(hw => hw.FormulasConsumed)
           .Include(hw => hw.AcceptedMaterialType)
           .Include(hb => hb.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
           .FirstOrDefaultAsync();

            if (henBatch == null || henBatch.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullAsyncNotFoundEx]));

            henBatch.Files = fileService.GetAll().Where(f => f.TenantDependentEntityId == henBatch.Id).ToList();

            return henBatch;
        }

        /// <summary>
        /// Returns the current week number the henbatch is in.
        /// If the henbatch has closed, it returns the week number the henbatch was in when it closed.
        /// </summary>
        public int GetCurrentWeekNumber(HenBatch henBatch)
        {
            DateTime weekStart = henBatch.DateStart.Value.GetPastSelectedDay(henBatch.Farm.DayOfWeek);

            if (!henBatch.DateEnd.HasValue)
                return henBatch.BatchWeekNumber + (int)((DateTime.Now - weekStart).Days / 7);
            else
                return henBatch.BatchWeekNumber + (int)((henBatch.DateEnd.Value - weekStart).Days / 7);
        }

        /// <summary>
        /// Returns the current week number the henbatch is in.
        /// If the henbatch has closed, it returns the week number the henbatch was in when it closed.
        /// </summary>
        public int GetCurrentWeekNumber(Guid id)
        {
            HenBatch henBatch = GetAll(asNoTracking: true)
                .Include(hb => hb.Farm)
                .First(hb => hb.Id == id);

            DateTime weekStart = henBatch.DateStart.Value.GetPastSelectedDay(henBatch.Farm.DayOfWeek);

            if (!henBatch.DateEnd.HasValue)
                return henBatch.BatchWeekNumber + (DateTime.Now - weekStart).Days / 7;
            else
                return henBatch.BatchWeekNumber + (henBatch.DateEnd.Value - weekStart).Days / 7;
        }

        /// <summary>
        /// Returns the current week number the henbatch is in at the passed date.
        /// </summary>
        public int GetCurrentWeekNumberForDate(Guid henBatchId, DateTime date)
        {
            var henBatch = GetAll()
                .Where(hb => hb.Id == henBatchId)
                .Select(hb => new { hb.DateStart, hb.BatchWeekNumber, hb.Farm.DayOfWeek })
                .FirstOrDefault();

            DateTime weekStart = henBatch.DateStart.Value.GetPastSelectedDay(henBatch.DayOfWeek);

            return henBatch.BatchWeekNumber + (date - weekStart).Days / 7;
        }

        /// <summary>
        /// Returns the current week number the henbatch is in at the passed date.
        /// </summary>
        public int GetCurrentWeekNumberForDate(HenBatch henBatch, DateTime date)
        {
            DateTime weekStart = henBatch.DateStart.Value.GetPastSelectedDay(henBatch.Farm.DayOfWeek);

            return henBatch.BatchWeekNumber + (date - weekStart).Days / 7;
        }

        /// <summary>
        /// Returns the last day of the week the henbatch is in or the last day of the input week.
        /// </summary>
        public DateTime GetLastDayOfTheWeek(HenBatch henBatch, int? weekNumber = null)
        {
            DateTime weekStart = henBatch.DateStart.Value.GetPastSelectedDay(henBatch.Farm.DayOfWeek);
            if (!weekNumber.HasValue)
                return weekStart.AddDays((GetCurrentWeekNumber(henBatch) - henBatch.BatchWeekNumber + 1) * 7 - 1);
            else
                return weekStart.AddDays((weekNumber.Value - henBatch.BatchWeekNumber + 1) * 7 - 1);
        }

        ///<summary>
        /// Get days since last hen report
        /// </summary>
        public string GetDaysSinceLastHenReport(HenBatch henBatch)
        {
            // If there is no start date we cant compare the default
            if (henBatch.DateStart == null)
                return null;

            DateTime today = DateTime.Today;

            // If there are no reports, return every day since batch start date
            if (henBatch.Reports == null || henBatch.Reports.Count() == 0)
                return (today - henBatch.DateStart.Value).Days.ToString();

            // order reports by date and find the oldest
            DateTime lasReportDate = henBatch.Reports
                .OrderByDescending(hr => hr.Date)
                .FirstOrDefault().Date;

            return (today - lasReportDate).Days.ToString();
        }

        /// <summary>
        /// Gets the day of the first loading of birds in the submitted hen batch.
        /// </summary>
        public DateTime? GetBirdsFirstLoadingDate(Guid henBathId)
        {
            IQueryable<HenBatch> henBatches = GetAll().Where(hb => hb.Id == henBathId && hb.DestinationShippingNotes.Any(sn => sn.MaterialsShipped.Any(ms => ms.Material.MaterialType.Path.StartsWith(MaterialTypePaths.ActivoBiologicoProductivoAve))));
            if (henBatches.Any())
                return henBatches.SelectMany(hb => hb.DestinationShippingNotes.OrderBy(sn => sn.Date)).Select(sn => sn.Date).FirstOrDefault().Value.Date;
            else
                return null;
        }

        /// <summary>
        /// Gets the day of the last loading of birds in the submitted hen batch.
        /// </summary>
        public DateTime? GetBirdsLastLoadingDate(Guid henBathId)
        {
            IQueryable<HenBatch> henBatches = GetAllWithShippingNotes().Where(hb => hb.Id == henBathId && hb.DestinationShippingNotes.Any(sn => sn.MaterialsShipped.Any(ms => ms.Material.MaterialType.Path.StartsWith(MaterialTypePaths.ActivoBiologicoProductivoAve))));
            if (henBatches.Any())
                return henBatches.SelectMany(hb => hb.DestinationShippingNotes
                                 .Where(sn => sn.MaterialsShipped.Any(ms => ms.Material.MaterialType.Path.StartsWith(MaterialTypePaths.ActivoBiologicoProductivoAve))))
                                 .OrderByDescending(sn => sn.Date)
                                 .Select(sn => sn.Date)
                                 .FirstOrDefault().Value.Date;
            else
                return null;
        }

        /// <summary>
        /// Returns all hen batches including their shipping notes, with their origins and destinations, and the materials shipped for tracking
        /// </summary>
        public IQueryable<HenBatch> GetAllWithShippingNotes() =>
            this.GetAll().Include(hb => hb.OriginShippingNotes).ThenInclude(sn => sn.Destination)
                .Include(hb => hb.OriginShippingNotes).ThenInclude(sn => sn.Origin)
                .Include(hb => hb.OriginShippingNotes).ThenInclude(sn => sn.MaterialsShipped).ThenInclude(ms => ms.Material)
                .Include(hb => hb.DestinationShippingNotes).ThenInclude(sn => sn.Destination)
                .Include(hb => hb.DestinationShippingNotes).ThenInclude(sn => sn.Origin)
                .Include(hb => hb.DestinationShippingNotes).ThenInclude(sn => sn.PersonOrigin)
                .Include(hb => hb.DestinationShippingNotes).ThenInclude(sn => sn.MaterialsShipped).ThenInclude(ms => ms.Material);

        /// <summary>
        /// Returns a list of hen batch by parent id including the shipping notes destinated to it and the materials shipped
        /// </summary>
        public IQueryable<HenBatch> GetWithDestinationShippingNotes(Guid parentId) =>
            this.GetAll().Where(hb => hb.Id == parentId || hb.ParentId == parentId)
                .Include(hb => hb.DestinationShippingNotes)
                    .ThenInclude(sn => sn.MaterialsShipped)
                        .ThenInclude(ms => ms.Material)
                            .ThenInclude(m => m.MaterialType);

        /// <summary>
        /// Returns all hen batches including the genetic 
        /// </summary>
        public IQueryable<HenBatch> GetAllWithGenetic() =>
            this.GetAll()
            .Include(hb => hb.Genetic);

        /// <summary>
        /// Returns all hen batches including the genetic 
        /// </summary>
        public HenBatch GetByAliasApi(Guid namerId, string alias)
        {
            HenBatch henBatch = GetByAlias(namerId, alias)
                .Include(hb => hb.EggMaterial).ThenInclude(em => em.MaterialType)
                .Include(hb => hb.Genetic)
                .FirstOrDefault();

            if (henBatch is null)
                throw new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]);

            return henBatch;
        }

        /// <summary>
        /// Returns a Page<HenBatch> representing a set of rows of a Farm table.
        /// </summary>
        public async Task<Page<HenBatch>> GetPage(Guid farmId, HenStage? henStage, string name, int page, int pageSize)
        {
            IQueryable<HenBatch> query = GetAll().Where(hb => hb.FarmId == farmId);

            if (henStage.HasValue && Enum.IsDefined(typeof(HenStage), henStage))
                query = query.Where(hb => hb.HenStage == henStage);

            if (!string.IsNullOrEmpty(name))
                query = query.Where(f => f.Name.Contains(name));

            Task<int> total = query.CountAsync();
            query = query.Skip((page - 1) * pageSize).Take(pageSize);

            return new Page<HenBatch>()
            {
                CurrentPage = page,
                PageSize = pageSize,
                Total = await total,
                Items = await query.ToListAsync()
            };
        }

        /// <summary>
        /// Returns children henbatches for henbatch with multiple distributions.
        /// </summary>
        public IQueryable<HenBatch> GetChildren(Guid parentdId)
        {
            return GetAll().Where(hb => hb.ParentId == parentdId)
                .Include(hb => hb.AcceptedMaterialType)
                .Include(hb => hb.Category)
                .Include(hb => hb.Line).ThenInclude(l => l.Warehouse)
                .Include(hb => hb.Reports);
        }

        /// <summary>
        /// Asynchronously retrieves a parent or single henbatch by code.
        /// </summary>
        public override async Task<HenBatch> GetByCodeAsync(string code)
        {
            HenBatch entity = await GetAll().FirstOrDefaultAsync(hb => hb.Code.ToUpper() == code.ToUpper() && !hb.ParentId.HasValue);

            if (entity == null)
                throw new NotFoundException(localizer[Lang.GetFullNotFoundEx]);

            return entity;
        }

        public async Task<HenBatch> GetByFarmHenWarehouseAndLineCode(string farm, string henwarehouse, string line)
        {
            HenBatch henBatch = await GetAll()
                .Where(hb => hb.Farm.Code == farm && hb.Line.Warehouse.Code == henwarehouse && hb.Line.Code == line)
                .FirstOrDefaultAsync();

            if (henBatch is null)
                throw new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]);

            return henBatch;
        }

        /// <summary>
        /// returns every open hen batch from a specific hen warehouse including its relationships
        /// </summary>
        public IQueryable<HenBatch> GetAllFromWarehouse(Guid warehouseId)
        {
            IQueryable<HenBatch> henBatches = GetAll()
                .Include(hb => hb.FormulasConsumed)
                .Include(hb => hb.OriginContainers).ThenInclude(oc => oc.Origin).ThenInclude(o => o.MaterialContainers)
                .Include(hb => hb.OriginContainers).ThenInclude(oc => oc.Origin).ThenInclude(o => o.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType)
                .Include(hb => hb.Line).ThenInclude(l => l.Warehouse)
                .Where(hb => hb.Line.WarehouseId == warehouseId && hb.Active && !hb.DateEnd.HasValue);

            bool hasHenBatchCategories = tenantConfigurationService.GetAll().Any(c => c.TenantId == operationContext.GetUserTenantId() && c.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && c.Value == "True");
            if (hasHenBatchCategories)
                henBatches = henBatches.Include(sc => sc.Category);

            return henBatches;
        }

        /// <summary>
        /// Get a list of hen batches with details data for offline functionality
        /// </summary>
        public IQueryable<HenBatch> GetFullHenBatches()
        {
            IQueryable<HenBatch> henBatches = GetAll()
                .Include(hb => hb.FormulasConsumed)
                .Include(hb => hb.OriginContainers).ThenInclude(oc => oc.Origin).ThenInclude(o => o.MaterialContainers)
                .Include(hb => hb.OriginContainers).ThenInclude(oc => oc.Origin).ThenInclude(o => o.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType)
                .Include(hb => hb.Line).ThenInclude(l => l.Warehouse)
                .Where(hb => hb.Line.WarehouseId != null && hb.Active && !hb.DateEnd.HasValue);

            bool hasHenBatchCategories = tenantConfigurationService.GetAll().Any(c => c.TenantId == operationContext.GetUserTenantId() && c.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && c.Value == "True");
            if (hasHenBatchCategories)
                henBatches = henBatches.Include(sc => sc.Category);

            return henBatches;
        }

        /// <summary>
        /// Returns a hen batch by Id including all its formulas.
        /// </summary>
        public HenBatch GetWithFormulas(Guid id)
        {
            HenBatch henBatch = base.GetAll(useDefaultSorting: false)
              .Where(hb => hb.Id == id)
              .Include(hb => hb.FormulasConsumed).ThenInclude(oc => oc.Formula)
              .FirstOrDefault();

            if (henBatch == null || henBatch.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]));

            return henBatch;
        }

        /// <summary>
        /// Returns a hen batch by Id including its warehouse.
        /// </summary>
        public HenBatch GetAllWithWarehouse(Guid id)
        {
            HenBatch henBatch = base.GetAll(useDefaultSorting: false)
              .Where(hb => hb.Id == id)
              .Include(hb => hb.Line).ThenInclude(l => l.Warehouse)
              .FirstOrDefault();

            return henBatch;
        }

        /// <summary>
        /// Returns a hen batch by Id including its warehouse.
        /// </summary>
        public HenBatch GetAllWithWarehouseAndHenerports(Guid id)
        {
            HenBatch henBatch = base.GetAll(useDefaultSorting: false)
              .Where(hb => hb.Id == id)
              .Include(hb => hb.Line).ThenInclude(l => l.Warehouse)
              .Include(hb => hb.Reports)
              .FirstOrDefault();

            return henBatch;
        }

        /// <summary>
        /// Returns all parent henbatches and henbatches without childs 
        /// </summary>
        public IQueryable<HenBatch> GetParentHenBatches(bool asNoTracking = false, bool useDefaultSorting = true)
        {
            return base.GetAll(asNoTracking, useDefaultSorting)
                .Where(hb => !hb.DateEnd.HasValue
                && (!hb.LineId.HasValue // parent henbatches 
                || (hb.LineId.HasValue && !hb.ParentId.HasValue))); // henbatches without children
        }

        /// <summary>
        /// Returns a parent henbatches and henbatches without childs by id 
        /// </summary>
        public HenBatch GetParentById(Guid Id)
        {
            return base.GetAll().Where(p => p.Id == Id)
                                   .Include(hb => hb.Line).ThenInclude(l => l.Warehouse)
                                   .Include(hb => hb.OriginContainers)
                                   .Include(hb => hb.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
                                   .Include(hb => hb.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType)
                                   .FirstOrDefault();
        }

        /// <summary>
        /// Returns hen batches including line and warehouse.
        /// </summary>
        public IQueryable<HenBatch> GetAllWithWarehouseAndLine()
        {
            IQueryable<HenBatch> henBatches = GetAll().Include(hb => hb.Line).ThenInclude(l => l.Warehouse);

            return henBatches;
        }

        /// <summary>
        /// Returns the first production date of a given henWarehouse.
        /// </summary>
        public DateTime? GetFirstProductionDate(Guid warehouseId)
        {
            return GetAll().Where(hb => hb.Line.WarehouseId == warehouseId)
                    .Select(hb => hb.FirstProductionDate)
                    .Min();
        }
        public DateTime? GetFirstProductionDate(List<Guid> henBatches)
        {
            return GetAll().Where(hb => henBatches.Contains(hb.Id))
                    .Select(hb => hb.FirstProductionDate)
                    .Min();
        }
        /// <summary>
        /// Returns the capitalization date of a given henWarehouse.
        /// </summary>
        public DateTime? GetCapitalizationDate(Guid warehouseId)
        {
            return GetAll().Where(hb => hb.Line.WarehouseId == warehouseId)
                    .Select(hb => hb.CapitalizationDate)
                    .Min();
        }

        /// <summary>
        /// Get the first shipping note of movement of hens into the hen batch.
        /// </summary>
        public DateTime? GetFirstHenMovementDate(Guid id)
        {
            return shippingNoteService.GetAll(asNoTracking: true, useDefaultSorting: false)
                .Where(sn =>
                    sn.DestinationId == id
                    && sn.MaterialsShipped.Any(ms => ms.Material.MaterialType.Path.StartsWith(MaterialTypePaths.ActivoBiologicoProductivoAve)))
                .OrderByDescending(d => d.Date)
                .Select(sn => sn.Date)
                .FirstOrDefault();
        }

        /// <summary>
        /// Get the first shipping note of movement of hens into the hen batch.
        /// </summary>
        public IEnumerable<(Guid henBatch, DateTime firstHenMovementDate)> GetFirstHenMovementDate(IEnumerable<Guid> ids)
        {
            return shippingNoteService.GetAll(asNoTracking: true, useDefaultSorting: false).TagWith($"{nameof(HenBatchService)} 659")
                .Where(sn =>
                    sn.DestinationId.HasValue
                    && ids.Contains(sn.DestinationId.Value)
                    && sn.MaterialsShipped.Any(ms => ms.Material.MaterialType.Path.StartsWith(MaterialTypePaths.ActivoBiologicoProductivoAve)))
                .Select(sn => new
                {
                    Destination = sn.DestinationId.Value,
                    Date = sn.Date.Value
                })
                .AsEnumerable()
                .GroupBy(sn => sn.Destination)
                .Select(sn => (henBatch: sn.Key, firstHenMovementDate: sn.OrderBy(sn => sn.Date).Select(sn => sn.Date).First()))
                .ToArray();
        }

        /// <summary>
        ///  Get all Hen Batches for grid
        /// </summary>
        public IQueryable<HenBatchGridView> GetAllView()
        {
            DbContext db = unitOfWork.GetDbContext<HenBatch>();
            IQueryable<HenBatchGridView> henBatches = db.Set<HenBatchGridView>().Where(CanAccess());

            return henBatches;
        }

        private Expression<Func<HenBatchGridView, bool>> CanAccess()
        {
            List<Guid> userSectorIds = this.operationContext.GetUserSectorIds();
            List<Guid> userSiteIds = this.operationContext.GetUserSiteIds();
            List<Guid> userCompanyIds = this.operationContext.GetUserCompanyIds();
            Guid? userTenantId = this.operationContext.GetUserTenantId();

            return e => (e.TenantId == userTenantId)
            && (!e.CompanyId.HasValue || userCompanyIds == null || !userCompanyIds.Any() || userCompanyIds.Contains(e.CompanyId.Value))
            && (userSiteIds == null || !userSiteIds.Any() || userSiteIds.Contains(e.FarmId))
            && (!e.SectorId.HasValue || userSectorIds == null || !userSectorIds.Any() || userSectorIds.Contains(e.SectorId.Value));
        }

        public IQueryable<HenBatch> GetAllHenBatchWithMaterialsAccepted()
        {
            IQueryable<HenBatch> henBatch = base.GetAll().Include(hb => hb.Farm)
                                             .Include(hb => hb.Genetic).ThenInclude(g => g.FemaleMaterial).ThenInclude(m => m.MaterialType)
                                             .Include(hb => hb.Genetic).ThenInclude(g => g.MaleMaterial).ThenInclude(m => m.MaterialType)
                                             .Include(hb => hb.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType);
            return henBatch;
        }
    }
}