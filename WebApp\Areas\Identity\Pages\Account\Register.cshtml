﻿@page
@model RegisterModel
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Areas.Identity.Pages.Account.Register
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer
@{
    ViewData["Title"] = localizer[Lang.Title];
    Layout = "/Views/Shared/_ExternalLayout.cshtml";
}

<section id="wrapper" class="login-register login-sidebar" style="background-image:url('@Url.Content("~/images/bg-register.png")');">
    <div class="login-box card">
        <div class="card-body">
            <form id="account" method="post" class="floating-labels" >
                <a href="javascript:void(0)" class="text-center db">
                    <img src="@Url.Action("Display", "Tenant", new { replace = "logo-promanager.png" })" class="mb-2" width="200" alt="Home" />
                </a>
                <h3 class="box-title m-t-40">@ViewData["Title"]</h3>
                <small class="mb-4">@localizer[Lang.Subtitle]</small>
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                
                <!-- Email -->
                <div class="form-group m-t-40 m-b-40">
                    <div class="col-xs-12">                
                        <input asp-for="Input.Email" class="form-control" type="text" required ondrop="return false;">
                        <span class="bar"></span>
                        <label asp-for="Input.Email"></label>
						<span asp-validation-for="Input.Email" class="text-danger"></span>
                    </div>
                </div>

                <!-- Name -->
                <div class="form-group m-b-40">
                    <div class="col-xs-12">                
                        <input asp-for="Input.Name" class="form-control" type="text" required ondrop="return false;">
                        <span class="bar"></span>
                        <label asp-for="Input.Name"></label>
						<span asp-validation-for="Input.Name" class="text-danger"></span>
                    </div>
                </div>

                <!-- Last Name -->
                <div class="form-group m-b-40">
                    <div class="col-xs-12">                
                        <input asp-for="Input.LastName" class="form-control" type="text" required ondrop="return false;">
                        <span class="bar"></span>
                        <label asp-for="Input.LastName"></label>
						<span asp-validation-for="Input.LastName" class="text-danger"></span>
                    </div>
                </div>

                <!-- Password -->
                <div class="form-group m-b-40">
                    <div class="col-xs-12">               
                        <input asp-for="Input.Password" class="form-control" type="password" required maxlength=20 ondrop="return false;">
                        <span class="bar"></span>
                        <label asp-for="Input.Password"></label>
						<span asp-validation-for="Input.Password" class="text-danger"></span>
						<p>@localizer[Lang.PasswordRequirements]</p>
                    </div>
                </div>

                <!-- Confirm Password -->
                <div class="form-group">
                    <div class="col-xs-12">
                        <input asp-for="Input.ConfirmPassword" class="form-control" type="password" required maxlength=20 ondrop="return false;">
                        <span class="bar"></span>
                        <label asp-for="Input.ConfirmPassword"></label>
						<span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
                    </div>
                </div>

                <div class="form-group text-center m-t-20">
                    <div class="col-xs-12">
                        <button class="btn btn-themecolor btn-lg btn-block text-uppercase btn-rounded" type="submit">@localizer[Lang.SubmitButton]</button>
                    </div>
                </div>
                <div class="form-group m-b-0">
                    <div class="d-flex justify-content-center m-b-40">
                        <p>@localizer[Lang.ExistingAccount] </p>
                        <a asp-page="./Login" asp-route-returnUrl="@Model.ReturnUrl" class="text-info m-l-5"><b>@localizer[Lang.LoginButton]</b></a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
