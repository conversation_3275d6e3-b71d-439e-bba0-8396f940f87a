name: "Build and deploy Promanager <PERSON><PERSON><PERSON> Breeder - Rovah"

trigger:
  - dev-rovah
  - prod-rovah

pool:
  vmImage: windows-latest

variables:
  buildConfiguration: "Release"
  serviceConnection: "pmbb-subscription-connection-01"
  ${{ if eq(variables['Build.SourceBranchName'], 'dev-rovah') }}:
    webApiAppName: "pmbbrovah-dev-app-api-br01"
    webAppAppName: "pmbbrovah-dev-app-web-br01"
  ${{ if eq(variables['Build.SourceBranchName'], 'prod-rovah') }}:
    webApiAppName: ""
    webAppAppName: "pmbbrovah-prd-app-web-01"

steps:
  - task: UseDotNet@2
    displayName: "Use .NET Core 3.1"
    inputs:
      version: "3.1.426"
      installLatestRuntime: true
      includePreviewVersions: true

  - task: DotNetCoreCLI@2
    displayName: Clean
    inputs:
      command: custom
      custom: clean
      projects: "**/*.sln"
      arguments: "--configuration $(buildConfiguration)"

  - task: DotNetCoreCLI@2
    displayName: Build
    inputs:
      command: build
      projects: "**/*.sln"
      arguments: "--configuration $(buildConfiguration)"

  - task: DotNetCoreCLI@2
    displayName: Publish WebAPI
    inputs:
      command: publish
      publishWebProjects: false
      projects: "**/WebApi.csproj"
      arguments: "--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/WebApi"
      zipAfterPublish: True

  - task: DotNetCoreCLI@2
    displayName: Publish WebApp
    inputs:
      command: publish
      publishWebProjects: true
      projects: "**/*.csproj"
      arguments: "--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/WebApp"
      zipAfterPublish: True

  - task: AzureWebApp@1
    displayName: "Deploy WebApi"
    condition: ne(variables.webApiAppName, '')
    inputs:
      azureSubscription: "$(serviceConnection)"
      appName: "$(webApiAppName)"
      appType: "webApp"
      package: "$(Build.ArtifactStagingDirectory)/WebApi/WebApi.zip"

  - task: AzureWebApp@1
    displayName: "Deploy WebApp"
    condition: ne(variables.webAppAppName, '')
    inputs:
      azureSubscription: "$(serviceConnection)"
      appName: "$(webAppAppName)"
      appType: "webApp"
      package: "$(Build.ArtifactStagingDirectory)/WebApp/WebApp.zip"
