@using Binit.Framework
@using Binit.Framework.Interfaces.DAL
@using Binit.Framework.Constants.Authentication
@using Domain.Entities.Model;
@using Microsoft.Extensions.Localization
@using System.Globalization;
@using WebApp.WebTools;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.SampleCageReport.Index
@inject IStringLocalizer<SharedResources> localizer
@inject IOperationContext operationContext
@{
    string lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
    List<SelectListItem> farms = ViewData["Farms"] as List<SelectListItem>;
    List<SelectListItem> clusters = ViewData["Clusters"] as List<SelectListItem>;
    List<SelectListItem> warehouses = ViewData["Warehouses"] as List<SelectListItem>;
    List<SelectListItem> lines = ViewData["Lines"] as List<SelectListItem>;
    var genetics = ViewData["Genetics"] as List<SelectListItem>;
    var henBatches = ViewData["HenBatches"] as List<SelectListItem>;
    var henStage = ViewData["HenStage"] as HenStage?;
    string validationError = ViewData["ValidationError"] as string;
    var hasHenBatchCategories = (bool)ViewData["HasHenBatchCategories"];
    var reportConfig = (bool)ViewData["ReportsHenWarehouseAndParentBatchWeightMeasurement"];
    DateTime fromDate = DateTime.Today.AddDays(-30);
    string tableId = ViewData["TableId"] as string;

    bool CheckAuthorizationForInconsistency()
    {
        if (this.operationContext.UserIsInAnyRole(
                Roles.BackofficeSuperAdministrator,
                        Roles.BackofficeLayingAdministrator,
                        Roles.BackofficeLayingUser,
                        Roles.BackofficeLayingDailyReportsAdministrator,
                        Roles.BackofficeLayingDailyReportsUser,
                        Roles.BackofficeLayingSampleCageReportAdministrator))
            return true;
        return false;
    }
}

<input id="henStage" type="hidden" value="@henStage" />

@{ if (reportConfig)
    {
        <div class="d-flex justify-content-end">
            <div class="btn-group show ">
                <button type="button" class="btn btn-themecolor dropdown-toggle" data-toggle="dropdown" aria-haspopup="false"
                    aria-expanded="false">
                    <i class="fa fa-plus"></i> @(localizer[Lang.BtnNew])
                </button>
                <div class="dropdown-menu dropdown-menu-lg-right" x-placement="left-start"
                    style="position: absolute; transform: translate3d(-250px, 40px, 0px); top: 0px; left: 0px;">
                    <a class="dropdown-item"
                        href="@Url.Action("CreateFromWarehouse", "SampleCageReport", new { henStage = henStage , reportConfig = reportConfig })">@(localizer[Lang.FromWarehouse])</a>
                    <a class="dropdown-item"
                        href="@Url.Action("Create", "SampleCageReport", new { henStage = henStage })">@(localizer[Lang.FromHenBatch])</a>
                    <a class="dropdown-item" href="@Url.Action(henStage == HenStage.Breeding ? "CreateBreedingWeightReport" : "CreateLayingWeightReport", "SampleCageReport")">
                        @localizer["Novo lançamento"] </a>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="d-flex justify-content-end" id="createSampleCageReport">
            <button type="button" class="btn btn-create btn-themecolor" onclick="location.href='@Url.Action("CreateFromWarehouse", "SampleCageReport", new { henStage = henStage, reportConfig = reportConfig })'">
                <i class="fa fa-plus"></i>
                @(localizer[Lang.BtnNewFromWarehouse])
            </button>
        </div>
    }
    <a id="export" class="btn btn-primary excel mr-2">
        <i class="fa fa-file-excel m-l-5"></i>
        @(localizer[Lang.BtnExportAll])
    </a>
}
<div class="row">
    <div class="col-md-2">
        <label for="henBatchStatus">
            @(localizer[Lang.HenBatchStatusLabel])
        </label>
        <select class="select2" id="henBatchStatus">
            <option value="open" selected>
                @(localizer[Lang.OpenHenBatchOption])
            </option>
            <option value="closed">
                @(localizer[Lang.ClosedHenBatchOption])
            </option>
            <option value="all">
                @(localizer[Lang.HenBatchAllOption])
            </option>
        </select>
    </div>

    <div class="col-1 mt-4">
        <button id="btnFilter"
                class="btn waves-effect waves-light btn-dark btn-sm datatable-action-button mt-1">
            @(localizer[Lang.BtnFilter])
        </button>
    </div>
</div>
</br>

@(Html.DevExtreme().IgniteDataGrid<SampleCageReportRow>("SampleCageReport", "GetAll", tableId, localizer, true,
    new {
        henStage = henStage,
        henBatchStatus = new JS("function(){return getHenBatchStatus()}"),
        hasHenBatchCategories = hasHenBatchCategories
    })
    .Columns(columns =>
    {
        columns.Add().DataField("date").Caption(localizer[Lang.TableColDate]).DataType(GridColumnDataType.Date).Format("dd/MM/yyyy");

        columns.Add().DataField("farmCode").Caption(localizer[Lang.TableColFarmCode]).DataType(GridColumnDataType.String);

        columns.Add().DataField("farmName").Caption(localizer[Lang.TableColFarmName]).DataType(GridColumnDataType.String);

        columns.Add().DataField("warehouse").Caption(localizer[Lang.TableColWarehouse]).DataType(GridColumnDataType.String);

        columns.Add().DataField("line").Caption(localizer[Lang.TableColLine]).DataType(GridColumnDataType.String);

        columns.Add().DataField("henBatch").Caption(localizer[Lang.TableColHenbatch]).DataType(GridColumnDataType.String);

        columns.Add().DataField("genetic").Caption(localizer[Lang.TableColGenetic]).DataType(GridColumnDataType.String)
            .Visible(false);

        columns.Add().DataField("avgFemaleBirdWeight").Caption(localizer[Lang.TableColAvgFemaleBirdWeight]).DataType(GridColumnDataType.String);

        columns.Add().DataField("avgMaleBirdWeight").Caption(localizer[Lang.TableColAvgMaleBirdWeight]).DataType(GridColumnDataType.String);

        columns.Add().DataField("uniformityFemale").Caption(localizer[Lang.TableColUniformityFemale]).DataType(GridColumnDataType.String)
        .AllowHeaderFiltering(false)
        .AllowGrouping(false);

        columns.Add().DataField("uniformityMale").Caption(localizer[Lang.TableColUniformityMale]).DataType(GridColumnDataType.String)
        .AllowHeaderFiltering(false)
        .AllowGrouping(false);

        columns.Add().DataField("variationCoefficientFemale").Caption(localizer[Lang.TableColVariationCoefficientFemale]).DataType(GridColumnDataType.String)
        .AllowHeaderFiltering(false)
        .AllowGrouping(false);

        columns.Add().DataField("variationCoefficientMale").Caption(localizer[Lang.TableColVariationCoefficientMale]).DataType(GridColumnDataType.String)
        .AllowHeaderFiltering(false)
        .AllowGrouping(false);

        if (hasHenBatchCategories)
        {
            columns.Add().DataField("henBatchCategory").Caption(localizer[Lang.TableColHenBatchCategory]).DataType(GridColumnDataType.String);
        }

        if(reportConfig)
        {
            columns.Add().DataField("henWarehouseAvgFemaleBirdWeight").Caption(localizer[Lang.TableColHenWarehouseAvgFemaleBirdWeight]).DataType(GridColumnDataType.String);

            columns.Add().DataField("henWarehouseAvgMaleBirdWeight").Caption(localizer[Lang.TableColHenWarehouseAvgMaleBirdWeight]).DataType(GridColumnDataType.String);

            columns.Add().DataField("henWarehouseUniformityFemale").Caption(localizer[Lang.TableColHenWarehouseUniformityFemale]).DataType(GridColumnDataType.String);

            columns.Add().DataField("henWarehouseUniformityMale").Caption(localizer[Lang.TableColHenWarehouseUniformityMale]).DataType(GridColumnDataType.String);

            columns.Add().DataField("henWarehouseVariationCoefficientFemale").Caption(localizer[Lang.TableColHenWarehouseVariationCoefficientFemale]).DataType(GridColumnDataType.String)
            .AllowHeaderFiltering(false)
            .AllowGrouping(false);

            columns.Add().DataField("henWarehouseVariationCoefficientMale").Caption(localizer[Lang.TableColHenWarehouseVariationCoefficientMale]).DataType(GridColumnDataType.String)
            .AllowHeaderFiltering(false)
            .AllowGrouping(false);
        }

        columns.AddEmptyActionsColumn(localizer)
        .Buttons(buttons =>
        {
            buttons.Add(new DataTableRedirectAction()
            {
                InternalName = "edit-normal",
                Icon = "edit",
                Class = "btn-primary edit-normal",
                Url = $"/SampleCageReport/edit/{{id}}",
                ShowDisplayName = false,
                DisplayName = localizer[Lang.Edit]
            });
            if (reportConfig)
            {
                buttons.Add(new DataTableRedirectAction()
                {
                    InternalName = "edit-from-warehouse",
                    Icon = "edit",
                    Class = "btn-warning edit-from-warehouse",
                    Url = $"/SampleCageReport/EditFromWarehouse/{{id}}",
                    ShowDisplayName = false,
                    DisplayName = localizer[Lang.EditFromWarehouse]
                }, "IsChild");
            }
            buttons.Add(new DataTableRedirectAction()
            {
                InternalName = "details",
                Icon = "fa fa-eye",
                Class = "btn-success",
                Url = $"/SampleCageReport/details/{{id}}",
                ShowDisplayName = false,
                DisplayName = localizer[Lang.Details]
            });
            if (CheckAuthorizationForInconsistency())
            {
                buttons.Add(
                    new DataTableRedirectAction()
                    {
                        InternalName = "delete",
                        Class = "btn-danger",
                        Icon = "trash",
                        Url = $"/SampleCageReport/Delete/{{id}}",
                        ShowDisplayName = false,
                        DisplayName = localizer[Lang.Delete],
                        SuccessTitle = localizer[Lang.DeleteConfirmationMessage]
                    },
                    "deleteVisibility",
                    "deleteAction");
            }

        });
    })

)@Html.AntiForgeryToken()

@section scripts {
    <!-- Script required to localize datatable resources -->
    <script>
        const datatableResources = @Json.Serialize(ViewData["DatatableResources"]);
        const sampleCageReportIndexResources = @Json.Serialize(ViewData["SampleCageReportIndexResources"]);
        const validationError = '@Html.Raw(validationError)';
        const henStage = '@henStage';
        const hasHenBatchCategories = '@hasHenBatchCategories';
        const title = '@Html.Raw(ViewData["Title"])';
        const tableId = '@tableId'
    </script>
    <script src="@Url.Content("~/js/views/SampleCageReport/actionButtonHandler.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/views/SampleCageReport/index.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/views/SampleCageReport/actionButtonVisibility.js")" type="text/javascript"></script>
}

<ignite-load plugins="datatable,select2, date-time-picker"></ignite-load>
