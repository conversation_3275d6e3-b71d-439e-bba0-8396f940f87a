using Binit.Framework;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Binit.Shaper.Interfaces.Services;
using DAL.Interfaces;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Binit.Framework.Localization.LocalizationConstants.DomainLogic.Services;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.Services.HenWarehouseService;

namespace Domain.Logic.Services
{
    /// <summary>
    /// Warehouse specific services.
    /// </summary>
    public class HenWarehouseService : ContainerService<HenWarehouse>, IHenWarehouseService

    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHenBatchService _henBatchService;

        public HenWarehouseService(IExceptionManager exceptionManager, ILogger logger, IOperationContext
            operationContext, IUnitOfWork unitOfWork, IHenBatchService henBatchService, IStringLocalizer<SharedResources> localizer, IMediator
            mediator, IFarmService farmService, IClusterService clusterService, IGeneticService geneticService,
            IServiceProvider provider, IMaterialTypeService materialTypeService, IInconsistencyReportService inconsistencyReportService,
            ISectorService sectorService, IServiceTenantDependent<TenantDependentEntityFile> fileService,
            IAliasExtensionService aliasExtensionService, IService<TenantConfiguration> tenantConfigurationService) : base(exceptionManager, logger, operationContext,
            unitOfWork, localizer, mediator, farmService, clusterService, geneticService, inconsistencyReportService, provider,
            materialTypeService, fileService, sectorService, aliasExtensionService, tenantConfigurationService)
        {
            _unitOfWork = unitOfWork;
            _henBatchService = henBatchService;
        }

        /// <summary>
        /// Creates a new henwarehouse with auto-generated name and number
        /// </summary>
        public async Task CreateHenWarehouseAsync(HenWarehouse entity)
        {
            var clusters = clusterService.GetAll();
            // If the tenant is configured to not have clusters, ClusterId will be empty Guid and must be defined as the farm's only cluster (automatically created). 
            if (entity.ClusterId == Guid.Empty)
                entity.ClusterId = clusters.FirstOrDefault(c => c.FarmId == entity.FarmId).Id;

            IQueryable<int> numbers = clusters.Where(c => c.Id == entity.ClusterId)
                .Include(c => c.HenWarehouses)
                .SelectMany(c => c.HenWarehouses.Select(hw => hw.Number));

            entity.Number = numbers.Any() ? numbers.Max() + 1 : 1;
            entity.Name = $"{localizer[Lang.HenWarehouse]} {entity.Number}";
            entity.Code = entity.Number.ToString();

            await base.CreateAsync(entity);
        }

        /// <summary>
        /// Hen warehouse updateAsync override.
        /// Updates the hen warehouse and all its dependant relationships.
        /// Generates FarmId.
        /// </summary>
        public override async Task UpdateAsync(HenWarehouse entity)
        {
            // Get current hen warehouse from db.
            // Only include relationships that need to be auto-updated by EF Core.
            HenWarehouse dbHenWarehouse = await base.GetAll()
                 .Where(sw => sw.Id == entity.Id)
                 .Include(sw => sw.Farm)
                 .Include(sw => sw.AreaContainers)
                 .Include(sw => sw.OriginContainers)
                 .Include(sw => sw.AcceptedMaterialType)
                 .FirstOrDefaultAsync();

            // Set values from memory hen warehouse to db tracked hen warehouse.
            entity.Name = entity.Name == dbHenWarehouse.Name || string.IsNullOrEmpty(entity.Name) ? dbHenWarehouse.Name : entity.Name;
            entity.FarmId = dbHenWarehouse.FarmId ?? Guid.Empty;
            entity.CopyTo(dbHenWarehouse);

            await base.UpdateAsync(dbHenWarehouse);
        }

        /// <summary>
        /// Work around for updating HenWarehouses from the Edit View.
        /// </summary>
        public async Task UpdateFromViewModelAsync(HenWarehouse entity)
        {
            // Get current hen warehouse from db.
            // Only include relationships that need to be auto-updated by EF Core.
            HenWarehouse dbHenWarehouse = await base.GetAll()
                .Where(hw => hw.Id == entity.Id)
                .Include(hw => hw.Company)
                .Include(hw => hw.Farm)
                .Include(hw => hw.Sector)
                .Include(hw => hw.AreaContainers)
                .Include(hw => hw.OriginContainers)
                .Include(hw => hw.AcceptedMaterialType)
                .Include(hw => hw.Lines)
                .FirstOrDefaultAsync();

            bool hasCluster = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");

            if (dbHenWarehouse.ClusterId != entity.ClusterId && hasCluster)
            {
                dbHenWarehouse.ClusterId = entity.ClusterId;
                IQueryable<int> numbers = clusterService.GetAll().Where(c => c.Id == dbHenWarehouse.ClusterId)
                    .Include(c => c.HenWarehouses)
                    .SelectMany(c => c.HenWarehouses.Select(hw => hw.Number));
                dbHenWarehouse.Number = numbers.Any() ? numbers.Max() + 1 : 1;
                dbHenWarehouse.Name = $"{localizer[Lang.HenWarehouse]} {dbHenWarehouse.Number}";
                dbHenWarehouse.Code = dbHenWarehouse.Number.ToString();
            }

            dbHenWarehouse.Name = entity.Name;
            dbHenWarehouse.DetailedName = entity.DetailedName;
            dbHenWarehouse.CompanyId = entity.CompanyId;
            dbHenWarehouse.FarmId = entity.FarmId;
            dbHenWarehouse.SectorId = entity.SectorId;
            dbHenWarehouse.HenStage = entity.HenStage;
            dbHenWarehouse.AreaContainers = entity.AreaContainers;
            dbHenWarehouse.AcceptedMaterialType = entity.AcceptedMaterialType;
            dbHenWarehouse.OriginContainers = entity.OriginContainers;
            dbHenWarehouse.Aliases = entity.Aliases;
            dbHenWarehouse.Code = entity.Code;
            dbHenWarehouse.AirConditioningSystem = entity.AirConditioningSystem;
            dbHenWarehouse.FeederSystemTypeFemale = entity.FeederSystemTypeFemale;
            dbHenWarehouse.FeederSystemTypeMale = entity.FeederSystemTypeMale;
            dbHenWarehouse.WaterSystemType = entity.WaterSystemType;
            dbHenWarehouse.NestType = entity.NestType;
            dbHenWarehouse.LightSystemType = entity.LightSystemType;

            await base.UpdateAsync(dbHenWarehouse);
        }


        /// <summary>
        /// Returns all warehouses with its relationships.
        /// </summary>
        public IQueryable<HenWarehouse> GetAllFull(Guid? clusterId = null, HenStage? henStage = null)
        {
            IQueryable<HenWarehouse> warehouses = base.GetAll(useDefaultSorting: false)
                .Include(hw => hw.Company)
                .Include(hw => hw.Farm)
                .Include(hw => hw.Sector)
                .Include(hw => hw.Cluster)
                .Include(hw => hw.Lines).ThenInclude(l => l.HenBatches).ThenInclude(hb => hb.HenBatchPerformances)
                .Include(hw => hw.OriginShippingNotes)
                .Include(hw => hw.DestinationShippingNotes)
                .AsQueryable();

            if (clusterId.HasValue) warehouses = warehouses.Where(hw => hw.ClusterId == clusterId.Value);

            if (henStage.HasValue) warehouses = warehouses.Where(hw => hw.HenStage == henStage.Value);

            return warehouses;
        }

        /// <summary>
        /// Returns a warehouse by Id with its relationships.
        /// </summary>
        public new HenWarehouse GetFull(Guid id)
        {
            HenWarehouse warehouse = base.GetAll()
                   .Where(hw => hw.Id == id)
                   .Include(hw => hw.Company)
                   .Include(hw => hw.Farm)
                   .Include(hw => hw.Sector)
                   .Include(hw => hw.Cluster)
                   .Include(hw => hw.Lines).ThenInclude(l => l.AcceptedMaterialType)
                   .Include(hw => hw.AreaContainers)
                   .Include(hw => hw.OriginContainers).ThenInclude(cc => cc.Origin)
                   .Include(hw => hw.AcceptedMaterialType).ThenInclude(cmt => cmt.MaterialType)
                   .Include(hw => hw.AcceptedMaterialType).ThenInclude(cmt => cmt.CapacityUnit)
                   .Include(hw => hw.MaterialContainers).ThenInclude(mt => mt.Material).ThenInclude(m => m.MaterialType)
                   .FirstOrDefault();

            if (warehouse == null || warehouse.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]));

            return warehouse;
        }

        /// <summary>
        /// Returns asynchronously a warehouse by Id with its relationships.
        /// </summary>
        public async Task<HenWarehouse> GetFullAsync(Guid id, bool asNoTracking = false)
        {
            HenWarehouse warehouse = await base.GetAll(asNoTracking)
               .Where(hw => hw.Id == id)
               .Include(hw => hw.Cluster)
               .Include(hw => hw.Lines).ThenInclude(hb => hb.HenBatches)
               .Include(hw => hw.OriginShippingNotes)
               .Include(hw => hw.DestinationShippingNotes)
               .Include(hw => hw.AreaContainers)
               .Include(hw => hw.OriginContainers)
               .Include(hw => hw.AcceptedMaterialType)

               .FirstOrDefaultAsync();

            if (warehouse == null || warehouse.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullAsyncNotFoundEx]));

            return warehouse;
        }

        /// <summary>
        /// Returns all warehouses associated with a specific hen batch
        /// </summary>
        public IQueryable<HenWarehouse> GetAllByHenBatch(Guid henBatchId)
        {
            var batchIds = _henBatchService.GetAll()
                .Where(hb => hb.Id == henBatchId || hb.ParentId == henBatchId)
                .Select(hb => hb.Id)
                .ToArray();

            return base.GetAll()
                .Where(hw => hw.Lines
                    .Any(l => l.HenBatches
                        .Any(hb => batchIds.Contains(hb.Id))))
                .Include(hw => hw.Lines)
                    .ThenInclude(l => l.HenBatches)
                    .ThenInclude(hb => hb.SampleCages)
                .Include(hw => hw.Company)
                .Include(hw => hw.Farm)
                .Include(hw => hw.Cluster)
                .Include(hw => hw.Sector);
        }


        /// <summary>
        /// Returns a warehouse by hen batch ID with its relationships
        /// </summary>
        public HenWarehouse GetByHenBatchId(Guid henBatchId)
        {
            HenWarehouse warehouse = base.GetAll()
                .Include(hw => hw.Lines)
                .ThenInclude(l => l.HenBatches)
                .Where(hw => hw.Lines.Any(l => l.HenBatches.Any(hb => hb.Id == henBatchId)))
                .Include(hw => hw.Company)
                .Include(hw => hw.Farm)
                .Include(hw => hw.Sector)
                .Include(hw => hw.Cluster)
                .Include(hw => hw.AreaContainers)
                .Include(hw => hw.OriginContainers).ThenInclude(cc => cc.Origin)
                .Include(hw => hw.AcceptedMaterialType).ThenInclude(cmt => cmt.MaterialType)
                .Include(hw => hw.AcceptedMaterialType).ThenInclude(cmt => cmt.CapacityUnit)
                .Include(hw => hw.MaterialContainers).ThenInclude(mt => mt.Material).ThenInclude(m => m.MaterialType)
                .FirstOrDefault();

            if (warehouse == null || warehouse.Deleted)
                throw base.exceptionManager.Handle(new NotFoundException(this.localizer[Lang.GetFullNotFoundEx]));

            return warehouse;
        }

        /// <summary>
        /// Returns all hen warehouses flitered
        /// </summary>
        public IQueryable<HenWarehouse> GetAllFullFiltered(Dictionary<string, string> filters)
        {
            IQueryable<HenWarehouse> henWarehouses = GetAll()
                .Include(h => h.Cluster)
                .Include(h => h.Sector)
                .Include(h => h.Company)
                .Include(h => h.Farm)
                .Include(h => h.AreaContainers)
                .Include(h => h.AcceptedMaterialType);

            if (filters != null)
            {
                if (!string.IsNullOrEmpty(filters["status"]))
                {
                    if (filters["status"] == "Active")
                        henWarehouses = henWarehouses.Where(hb => hb.Active);
                    else if (filters["status"] == "Inactive")
                        henWarehouses = henWarehouses.Where(hb => !hb.Active);
                }
            }

            return henWarehouses;
        }

        /// <summary>
        /// Returns all warehouses that have lines with no active hen batches.
        /// </summary>
        public IQueryable<HenWarehouse> GetWithAvailableLines(HenStage? henStage = null)
        {
            IQueryable<HenWarehouse> henWarehouses = base.GetAll()
                .Where(l => l.Lines.Any(h => !h.HenBatches.Any(h => h.DateEnd == null)));

            if (henStage.HasValue)
                henWarehouses = henWarehouses.Where(hw => hw.HenStage == henStage.Value);

            return henWarehouses;
        }

        /// <summary>
        /// Hen warehouse deleteAsync override.
        /// Deletes the hen warehouse and all its dependant relationships.
        /// </summary>
        public async override Task DeleteAsync(Guid id)
        {
            // Gets hen warehouse with all its relationships included.
            HenWarehouse henWarehouse = await this.GetFullAsync(id, asNoTracking: true);

            if (henWarehouse.Lines.SelectMany(x => x.HenBatches).Any())
                throw base.exceptionManager.Handle(new UserException(this.localizer[Lang.CantDeleteWithHenbatchesAsociated]));
            //Check if there's any shipping notes
            if (henWarehouse.OriginShippingNotes.Any() || henWarehouse.DestinationShippingNotes.Any())
                // Delete hen warehouse.
                throw base.exceptionManager.Handle(new UserException(this.localizer[Lang.DeleteWithRelatedShippinNote]));

            //Check if there's any accepted material type
            if (henWarehouse.AcceptedMaterialType != null)
                // Delete henWarehouse accepted material type
                henWarehouse.AcceptedMaterialType.Clear();

            //Check if there's any origin containers
            if (henWarehouse.OriginContainers != null)
                // Delete henWarehouse origin containers
                henWarehouse.OriginContainers.Clear();

            //Check if there's any area containers
            if (henWarehouse.AreaContainers != null)
                // Delete henWarehouse area containers
                henWarehouse.AreaContainers.Clear();

            await base.DeleteAsync(henWarehouse);
        }

        /// <summary>
        /// Returns unused space by lines
        /// </summary>
        public async Task<decimal> GetHenWarehouseAvailableSpace(Guid id)
        {
            IQueryable<HenWarehouse> henWarehouse = GetAll(true).Where(hw => hw.Id == id);

            decimal lineCapacity = await henWarehouse
                .SelectMany(hw => hw.Lines)
                .Where(l => l.Active)
                .SelectMany(l => l.AcceptedMaterialType)
                .Where(amt => amt.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve)
                .SumAsync(amt => amt.CapacityStandarizedValue);

            decimal totalCapacity = await henWarehouse
                .SelectMany(hw => hw.AcceptedMaterialType)
                .Where(amt => amt.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve)
                .SumAsync(amt => amt.CapacityStandarizedValue);

            // if totalCapacity is equal to zero, it means that the warehouse storages 0, which means
            // that it doesn't have a limitation for the storage capacity. To avoid confusion the
            // function returns -1 when you dont need to worry about capacity and zero when there isn't
            // available space 
            return totalCapacity == 0 ? -1 : totalCapacity - lineCapacity;
        }

        /// <summary>
        /// Return an Enum DTO with the data for the capacityAlert
        /// </summary>
        public IEnumerable<HenWarehouseDTO> GetAll(HenStage henStage)
        {
            return GetAll().Where(hw => hw.HenStage == henStage)
                .Include(hw => hw.Farm)
                .Include(hw => hw.Lines).ThenInclude(l => l.HenBatches)
                .Include(hw => hw.MaterialContainers).ThenInclude(mc => mc.Material)
                .Include(hw => hw.AcceptedMaterialType)
                .AsEnumerable()
                .Select(hw => new HenWarehouseDTO()
                {
                    Id = hw.Id,
                    FarmCode = hw.Farm.Code,
                    FarmName = hw.Farm.Name,
                    HenWarehouse = hw.Name,
                    StockActual = (hw.MaterialContainers.Any()) ? (hw.MaterialContainers.Where(mc => mc.Material.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).FirstOrDefault() == null) ? 0 : hw.MaterialContainers.Where(mc => mc.Material.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).FirstOrDefault().Quantity : 0,
                    MaxCapacity = hw.AcceptedMaterialType.Where(mt => mt.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).Sum(mt => mt.MaximumCapacity),
                    StockOverMaxCapacityAverage = this.GetAverage(hw, hw.AcceptedMaterialType.Sum(mt => mt.MaximumCapacity)),
                    AllowedCapacity = hw.AcceptedMaterialType.Sum(mt => mt.AllowedCapacity),
                    StockOverAllowedCapacityAverage = this.GetAverage(hw, hw.AcceptedMaterialType.Sum(mt => mt.AllowedCapacity)),
                    RecommendedCapacity = hw.AcceptedMaterialType.Sum(mt => mt.RecommendedCapacity),
                    StockOverRecommendedCapacityAverage = this.GetAverage(hw, hw.AcceptedMaterialType.Sum(mt => mt.RecommendedCapacity)),
                    ControlCapacity = hw.AcceptedMaterialType.Sum(mt => mt.CapacityStandarizedValue),
                    StockOverControlCapacityAverage = this.GetAverage(hw, hw.AcceptedMaterialType.Sum(mt => mt.CapacityStandarizedValue)),
                })
                .OrderBy(w => w.FarmCode)
                .ThenBy(w => w.HenWarehouse);

        }


        public decimal GetAverage(HenWarehouse hw, decimal capacity)
        {
            var stock = (hw.MaterialContainers.Any()) ? ((hw.MaterialContainers.Where(mc => mc.Material.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).FirstOrDefault() == null) ? 0 : hw.MaterialContainers.Where(mc => mc.Material.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).FirstOrDefault().Quantity) : 0;
            if (capacity == 0 && stock == 0)
                return 0;
            else
                return stock / capacity;
        }

        public IQueryable<HenWarehouse> GetAllWithAcceptedMaterialType()
        {
            return GetAll()
                .Include(hw => hw.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType);
        }

        private List<string> BuildAreaColumn(HenWarehouse henwarehouse)
        {
            List<string> areaNames = new List<string>();

            foreach (AreaContainer areaContainer in henwarehouse.AreaContainers)
            {
                areaNames.Add(areaContainer.AreaEnum.ToString());
            }
            return areaNames;
        }

    }
}