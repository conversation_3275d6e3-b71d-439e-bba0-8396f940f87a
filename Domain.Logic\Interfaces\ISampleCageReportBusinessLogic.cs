using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.SampleCageReportDTOs;
using Domain.Logic.DTOs.SampleCageReportDTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Domain.Logic.Interfaces
{
    public interface ISampleCageReportBusinessLogic
    {
        /// <summary>
        /// fetch a hen batch performance for the same day and hen batch,
        /// add the values of the report to it.
        /// </summary>
        Task CreateAndAddToHenBatchPerformanceAsync(SampleCageReport sampleCageReport);

        /// <summary>
        /// creates warehouse reports and add them to the hen batch performance
        /// </summary>
        Task CreateReports(List<SampleCageReport> reports);
        /// <summary>
        /// Updates henWarehouse data in all sampleCageReports belonging to a report's warehouse on that date
        /// </summary>
        Task UpdateHenWarehouseReports(SampleCageReport report);

        /// <summary>
        /// Sample cage report UpdateAsync override.
        /// Updates the sample cage report and all its dependant relationships.
        /// </summary>
        Task UpdateAndAddToHenBatchPerformanceAsync(SampleCageReport sampleCageReport);


        /// <summary>
        /// Returns all sample cage reports filtered including its hen batch with its line with its warehouse and its genetic.
        /// </summary>
        IQueryable<SampleCageReportDTO> GetFullFiltered(Dictionary<string, string> filters);

        /// <summary>
        /// Deletes a sample cage report, removes it from the HenBatchPerformance,
        /// and updates the values from the HenBatchPerformance
        /// </summary>
        Task DeleteAndRemoveFromHenBatchPerformanceAsync(Guid id);

        /// <summary>
        /// Validate DTO of sample cage report create from warehouse endpoint and returns entity
        /// </summary>
        List<SampleCageReport> ValidateDTO(SampleCageReportReq sampleCageReportReq);

        /// <summary>
        /// Returns a list with all sample cages from specific warehouse to use in mobile app sample cage report.
        /// </summary>
        Task<List<SampleCageRes>> GetSampleCages(Guid warehouseId, DateTime date);

        /// <summary>
        /// Returns a list with all sample cages for offline functionality.
        /// </summary>
        Task<List<SampleCageFullRes>> GetSampleCagesForOffline();
        /// <summary>
        /// Get all reports for offline functionality.
        /// </summary>
        Task<List<SampleCageReportFullItem>> GetFullListForOffline();
        /// <summary>
        /// Get all sample cage reports from the respective farm.
        /// </summary>
        Task<SampleCageReportPage> GetListByFarm(Guid farmId, int page, int pageSize);

        Task<List<SampleCageReport>> CreateReportsFromTableAsync(CreateSampleCageReportFromTableDTO dto);

        List<SampleCageReportHenBatchDTO> GetActiveHenBatchesByFarm(Guid farmId, HenStage henStage);

        /// <summary>
        /// Gets the list of processed warehouse IDs for a specific hen batch and date
        /// </summary>
        List<Guid> GetProcessedWarehouseIds(Guid henBatchId, DateTime reportDate);

        /// <summary>
        /// Saves processed warehouse IDs for a specific hen batch and date
        /// </summary>
        Task SaveProcessedWarehouseIds(Guid henBatchId, DateTime reportDate, List<Guid> warehouseIds);
    }
}