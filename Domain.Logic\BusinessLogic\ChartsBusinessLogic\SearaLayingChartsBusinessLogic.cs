using Binit.Framework;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.Interfaces.ExceptionHandling;
using Binit.Framework.Interfaces.DAL;
using DAL.Interfaces;
using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.ChartsBusinessLogic.LayingChartsBusinessLogic;
using HenStageLang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.ChartsBusinessLogic.HenStageChartsBusinessLogic;
using Binit.Framework.ExceptionHandling.Types;
using Domain.Logic.BusinessLogic.DTOs.Genetic;

namespace Domain.Logic.BusinessLogic.ChartsBusinessLogic
{
    public class SearaLayingChartsBusinessLogic : ISearaLayingChartsBusinessLogic
    {
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IExceptionManager exceptionManager;
        private readonly IUnitOfWork unitOfWork;
        private readonly ICapacityUnitService capacityUnitService;
        private readonly ICapacityUnitBusinessLogic capacityUnitBusinessLogic;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IHenBatchService henBatchService;
        private readonly IHatcheringReportService hatcheringReportService;
        private readonly IHenReportService henReportService;

        public SearaLayingChartsBusinessLogic(
            IStringLocalizer<SharedResources> localizer,
            IExceptionManager exceptionManager,
            IUnitOfWork unitOfWork,
            ICapacityUnitService capacityUnitService,
            ICapacityUnitBusinessLogic capacityUnitBusinessLogic,
            IGeneticsParameterService geneticsParameterService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IHenBatchService henBatchService,
            IHatcheringReportService hatcheringReportService,
            IHenReportService henReportService)
        {
            this.localizer = localizer;
            this.exceptionManager = exceptionManager;
            this.unitOfWork = unitOfWork;
            this.capacityUnitService = capacityUnitService;
            this.capacityUnitBusinessLogic = capacityUnitBusinessLogic;
            this.geneticsParameterService = geneticsParameterService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.henBatchService = henBatchService;
            this.hatcheringReportService = hatcheringReportService;
            this.henReportService = henReportService;
        }
        #region Colors
        public const string redColor = "#E74C3C";
        public const string blueColor = "#2471A3";
        public const string greenColor = "#2ECC71";
        public const string lightblueColor = "#85C1E9";
        public const string orangeColor = "#F18A2E";
        public const string yellowColor = "#EDFB42";
        public const string violetColor = "#C042FB";
        public const string uniformitySTDColor = "#145A32"; // dark green
        public const string cvSTDColor = "#633974"; // violet
        public const string femaleSeriesColor = redColor;
        public const string maleSeriesColor = blueColor;
        #endregion

        #region Seara Laying Charts

        public DashboardLineOrBarChartDTO GetSearaLayingFeedIntakeGADChart(FilterDataDTO filters = null)
        {
            int weekStart = filters.StartWeek ?? 22;
            int weekEnd = filters.EndWeek ?? 70;
            int totalWeeks = (weekEnd - weekStart + 1);

            var henBatchPerformancesQuery = GetSearaLayingPerformances(filters)
                .Where(hbp => hbp.WeekNumber >= weekStart && hbp.WeekNumber <= weekEnd);

            var isMale = filters.Gender == "1";
            var isBoth = filters.Gender == "3";

            if (!henBatchPerformancesQuery.Any())
            {
                throw exceptionManager.Handle(new NotFoundException(localizer["Não há dados de desempenho para o lote de aves."]));
            }

            var feedIntakeData = henBatchPerformancesQuery
                .Where(hbp => isMale ? hbp.HenAmountMale > 0 : hbp.HenAmountFemale > 0)
                .Select(hbp => new LayingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    Value = isMale ?
                            (hbp.HenAmountMale > 0 ? hbp.FeedIntakeMale * 1000M / hbp.HenAmountMale / 7M : 0) :
                            (hbp.HenAmountFemale > 0 ? hbp.FeedIntakeFemale * 1000M / hbp.HenAmountFemale / 7M : 0),
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + (isBoth ? (isMale ? " (M)" : " (F)") : ""),
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = isMale ? hbp.HenAmountMale : hbp.HenAmountFemale,
                })
                .ToList();

            if (isBoth)
            {
                var femaleFeedIntakeData = henBatchPerformancesQuery
                    .Where(hbp => hbp.HenAmountFemale > 0)
                    .Select(hbp => new LayingDataPointDTO
                    {
                        Date = hbp.Date,
                        WeekNumber = hbp.WeekNumber,
                        Value = hbp.FeedIntakeFemale * 1000M / hbp.HenAmountFemale / 7M,
                        ParentHenBatchId = hbp.HenBatch.ParentId,
                        ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (F)",
                        WarehouseId = hbp.HenBatch.Line.WarehouseId,
                        WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                        LineId = hbp.HenBatch.Line.Id,
                        LineName = hbp.HenBatch.Line.Name,
                        GeneticId = hbp.HenBatch.GeneticId,
                        AveragingWeight = hbp.HenAmountFemale,
                    })
                    .ToList();

                if (isMale)
                {
                    feedIntakeData.AddRange(femaleFeedIntakeData);
                }
                else if (!feedIntakeData.Any())
                {
                    feedIntakeData = henBatchPerformancesQuery
                        .Where(hbp => hbp.HenAmountMale > 0)
                        .Select(hbp => new LayingDataPointDTO
                        {
                            Date = hbp.Date,
                            WeekNumber = hbp.WeekNumber,
                            Value = hbp.FeedIntakeMale * 1000M / hbp.HenAmountMale / 7M,
                            ParentHenBatchId = hbp.HenBatch.ParentId,
                            ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (M)",
                            WarehouseId = hbp.HenBatch.Line.WarehouseId,
                            WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                            LineId = hbp.HenBatch.Line.Id,
                            LineName = hbp.HenBatch.Line.Name,
                            GeneticId = hbp.HenBatch.GeneticId,
                            AveragingWeight = hbp.HenAmountMale,
                        })
                        .ToList();
                }
            }

            if (!feedIntakeData.Any())
            {
                throw exceptionManager.Handle(new NotFoundException(localizer["Não há dados de consumo de ração para o lote de aves."]));
            }

            LayingChartOptionsDTO options = new LayingChartOptionsDTO
            {
                Title = this.localizer[Lang.FeedIntakeChartTitle] + (isBoth ? " (machos e fêmeas)" : (isMale ? " (machos)" : " (fêmeas)")),
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = this.localizer[Lang.FeedIntakeAxis],
                YMin = 0,
                YMax = 300,
                YTickAmount = 14,
                TotalWeeks = totalWeeks
            };

            LayingChart chart = new LayingChart(options);

            chart.WeekOffset = weekStart;

            for (int week = weekStart; week <= weekEnd; week++)
            {
                chart.GetChart().Categories.Add(week.ToString());
            }

            if (feedIntakeData.Any() && feedIntakeData.First().GeneticId.HasValue)
            {
                AddStandardSeries(chart, feedIntakeData.First().GeneticId.Value,
                    isBoth ? this.localizer[Lang.GADMaleSTD] + " / " + this.localizer[Lang.GADFemaleSTD] :
                            (isMale ? this.localizer[Lang.GADMaleSTD] : this.localizer[Lang.GADFemaleSTD]),
                    isMale ? maleSeriesColor : femaleSeriesColor,
                    p => isMale ? (p.FeedIntakeMale * 1000M / 7M) : (p.FeedIntakeFemale * 1000M / 7M),
                    "g");
            }

            AddGroupedSeriesToChart(filters, chart, feedIntakeData,
                isBoth ? this.localizer[Lang.GADMaleReal] + " / " + this.localizer[Lang.GADFemaleReal] :
                        (isMale ? this.localizer[Lang.GADMaleReal] : this.localizer[Lang.GADFemaleReal]),
                "g");

            return chart.GetChart();
        }


        public DashboardLineOrBarChartDTO GetSearaMaleFemaleChart(FilterDataDTO filters = null)
        {
            var henBatchPerformancesQuery = GetSearaLayingPerformances(filters);

            var maleFemaleData = henBatchPerformancesQuery
                .Select(hbp => new LayingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    HenAmountMale = hbp.HenAmountMale,
                    HenAmountFemale = hbp.HenAmountFemale,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                })
                .ToList();

            int weekStart = 22;
            int weekEnd = 70;
            int totalWeeks = weekEnd - weekStart + 1;

            LayingChartOptionsDTO options = new LayingChartOptionsDTO
            {
                Title = "Relação M/F",
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = "Machos/Fêmeas (%)",
                YMin = 0,
                YMax = 26,
                YTickAmount = 10,
                TotalWeeks = totalWeeks
            };

            LayingChart chart = new LayingChart(options);

            chart.WeekOffset = weekStart;

            for (int week = weekStart; week <= weekEnd; week++)
            {
                chart.GetChart().Categories.Add(week.ToString());
            }

            AddGroupedSeriesToChart(filters, chart, maleFemaleData, "Machos/Fêmeas (%)", "%", CalculateMaleFemaleRatio);

            AddStandardSeries(chart, maleFemaleData.First().GeneticId.Value, "Machos/Fêmeas STD (%)", greenColor, p => p.MaleFemaleRatio);

            return chart.GetChart();
        }


        private decimal CalculateMaleFemaleRatio(IGrouping<int, LayingDataPointDTO> dataPoints)
        {
            var maleCount = dataPoints.Sum(point => point.HenAmountMale);
            var femaleCount = dataPoints.Sum(point => point.HenAmountFemale);
            return femaleCount == 0 ? 0 : (100M * maleCount / femaleCount);
        }

        public GeneticGraphicResponseDTO GetGeneticChartData(FilterDataDTO filters)
        {
            var henBatchId = filters.HenBatchId;

            if (henBatchId == null)
                throw exceptionManager.Handle(new NotFoundException("É necessário selecionar um lote"));

            // Get genetic id from hen batch
            var geneticId = henBatchService.GetAll()
                .Where(hb => hb.Id == henBatchId)
                .Select(hb => hb.GeneticId)
                .Distinct()
                .FirstOrDefault();

            if (geneticId == null)
                throw exceptionManager.Handle(new NotFoundException("Não há dados para exibir"));

            // Get genetics parameters by genetic id
            var geneticsParametersList = geneticsParameterService.GetAll()
                .Where(gp => gp.GeneticsId == geneticId)
                .Where(gp => gp.HenStage == HenStage.Laying)
                .Where(gp => gp.WeightFemale > 0 || gp.WeightMale > 0 || gp.MortalityFemaleWeek > 0) // only weeks with relevant data
                .Include(gp => gp.Genetics)
                .OrderBy(gp => gp.TimePeriodValue)
                .ToList();

            // Get hen batch performances
            var henBatchPerformances = henBatchPerformanceService.GetAll();

            if (filters.LineId != null)
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.HenBatch.ParentId == henBatchId)
                    .Where(hbp => hbp.HenBatch.LineId == filters.LineId);
            }
            else if (filters.WarehouseId != null)
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.HenBatch.ParentId == henBatchId)
                    .Where(hbp => hbp.HenBatch.Line.WarehouseId == filters.WarehouseId);
            }
            else
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.HenBatchId == henBatchId);
            }

            var henBatchPerformancesList = henBatchPerformances
                .Include(hbp => hbp.HenBatch)
                .OrderBy(hbp => hbp.WeekNumber)
                .ToList();

            // Set period from Genetic Parameters
            int firstWeek = geneticsParametersList.First().TimePeriodValue;
            int lastWeek = geneticsParametersList.Last().TimePeriodValue;

            //Initialize DTO
            GeneticGraphicResponseDTO geneticGraphicDTOs = new GeneticGraphicResponseDTO()
            {
                HenStage = HenStage.Laying,
                Data = new List<GeneticGraphicDTO>()
            };

            //Build graphic data -> one DTO per week. 
            for (int week = firstWeek; week <= lastWeek; week++)
            {
                GeneticGraphicDTO dto = new GeneticGraphicDTO() { WeekNumber = week };

                //Set current values
                //Mortality is accumulative.
                HenBatchPerformance hbp = henBatchPerformancesList.Where(h => h.WeekNumber == week).FirstOrDefault();
                if (hbp != null)
                {
                    dto.BodyWeightFemale = hbp.AvgFemaleBirdWeight != null ? Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, hbp.AvgFemaleBirdWeight.Value)) : 0;
                    dto.BodyWeightMale = hbp.AvgMaleBirdWeight != null ? Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, hbp.AvgMaleBirdWeight.Value)) : 0;
                    dto.MortalityFemale = hbp.HenBatch.InitialHenAmountFemale != 0 ? Math.Round(((double)hbp.DeadFemale / (double)hbp.HenBatch.InitialHenAmountFemale) * 100, 2) : 0;
                    dto.MaxTemp = Decimal.ToDouble(hbp.MaxTemp);
                    dto.MinTemp = Decimal.ToDouble(hbp.MinTemp);
                }

                //STD: standard -> genetic parameters reference
                //Set standard values
                GeneticsParametersReference gpr = geneticsParametersList.Where(g => g.TimePeriodValue == week).FirstOrDefault();
                if (gpr != null)
                {
                    dto.BodyWeightSTDMaxFemale = Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, gpr.WeightFemale));
                    dto.BodyWeightSTDMaxMale = Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, gpr.WeightMale));
                    dto.MortalitySTDFemale = Math.Round((double)gpr.MortalityFemaleWeek, 2);
                }

                geneticGraphicDTOs.Data.Add(dto);
            }

            return geneticGraphicDTOs;
        }

        public DashboardLineOrBarChartDTO GetSaleableChicksChart(FilterDataDTO filters)
        {
            var hatcheringReportsQuery = GetHatcheringReports(filters);

            var realData = hatcheringReportsQuery
                .Select(hr => new LayingDataPointDTO
                {
                    WeekNumber = hr.Week,
                    Value = hr.ViableFemales + hr.ViableMales + hr.ViableMixed,
                    ParentHenBatchId = hr.HenBatch.ParentId,
                    ParentHenBatchName = hr.HenBatch.Parent.DetailedName,
                    WarehouseId = hr.HenBatch.Line.WarehouseId,
                    WarehouseName = hr.HenBatch.Line.Warehouse.Name,
                    LineId = hr.HenBatch.Line.Id,
                    LineName = hr.HenBatch.Line.Name,
                    GeneticId = hr.HenBatch.GeneticId,
                    AveragingWeight = hr.HenBatch.InitialHenAmountFemale
                })
                .Where(dp => dp.WeekNumber >= 22 && dp.WeekNumber <= 70)
                .ToList();

            if (!realData.Any())
            {
                throw exceptionManager.Handle(
                    new NotFoundException("Não há dados de pintos vendáveis na tabela HatcheringReport.")
                );
            }

            int weekStart = 22;
            int weekEnd = 70;
            int totalWeeks = weekEnd - weekStart + 1;

            var chartOptions = new LayingChartOptionsDTO
            {
                Title = "Pintos Vendáveis / F",
                XAxisLabel = $"Semanas ({weekStart} a {weekEnd})",
                YAxisLabel = "Pintos Vendáveis",
                YMin = 0,
                YTickAmount = 5,
                TotalWeeks = totalWeeks
            };

            var chart = new LayingChart(chartOptions);
            chart.WeekOffset = weekStart;

            for (int week = weekStart; week <= weekEnd; week++)
            {
                chart.GetChart().Categories.Add(week.ToString());
            }

            if (realData.First().GeneticId.HasValue)
            {
                AddStandardSeries(
                    chart,
                    realData.First().GeneticId.Value,
                    "Pintos Vendáveis STD",
                    redColor,
                    p => p.SaleableACM,
                    "",
                    false,
                    "spline"
                );
            }

            AddGroupedSeriesToChartWithColor(filters, chart, realData, "Pintos Vendáveis (Real)", blueColor, "", g => g.Sum(p => p.Value));

            return chart.GetChart();
        }


        public DashboardLineOrBarChartDTO GetOIFCChart(FilterDataDTO filters)
        {
            int weekStart = filters.StartWeek ?? 22;
            int weekEnd = filters.EndWeek ?? 70;
            int totalWeeks = (weekEnd - weekStart + 1);

            var performancesQuery = GetSearaLayingPerformances(filters)
                .Where(hbp => hbp.WeekNumber >= weekStart && hbp.WeekNumber <= weekEnd);

            // get hen batch ids associated with the performances
            var henBatchIds = performancesQuery.Select(p => p.HenBatchId).Distinct();
            // get accumulated pre-capitalization hatchable eggs
            // this is used to get the accumulated hatchable eggs after capitalization
            var preCapitalizationHatchableEggs = henReportService.GetAll(asNoTracking: true)
                .Where(hr => henBatchIds.Contains(hr.HenBatchId))
                .Where(hr => hr.Date < hr.HenBatch.CapitalizationDate)
                .Select(hr => new
                {
                    hr.HenBatchId,
                    hr.HatchableEggs
                })
                .ToList()
                .GroupBy(x => x.HenBatchId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Sum(x => x.HatchableEggs)
                );

            var realData = performancesQuery
                .Select(hbp => new
                {
                    WeekNumber = hbp.WeekNumber,
                    Date = hbp.Date,
                    FemalesOnFirstProductionDate = hbp.FemalesOnFirstProductionDate,
                    HatchableEggsAccum = hbp.HatchableEggsAccum,
                    HenBatchId = hbp.HenBatchId,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = (int)hbp.FemalesOnFirstProductionDate
                })
                .ToList()
                .Select(p => new LayingDataPointDTO
                {
                    WeekNumber = p.WeekNumber,
                    Date = p.Date,
                    Value = (p.FemalesOnFirstProductionDate > 0)
                            ? (decimal)(p.HatchableEggsAccum - preCapitalizationHatchableEggs.GetValueOrDefault(p.HenBatchId, 0)) / (decimal)p.FemalesOnFirstProductionDate
                            : 0,
                    ParentHenBatchId = p.ParentHenBatchId,
                    ParentHenBatchName = p.ParentHenBatchName,
                    WarehouseId = p.WarehouseId,
                    WarehouseName = p.WarehouseName,
                    LineId = p.LineId,
                    LineName = p.LineName,
                    GeneticId = p.GeneticId,
                    AveragingWeight = p.AveragingWeight
                })
                .ToList();

            if (!realData.Any())
            {
                throw exceptionManager.Handle(new NotFoundException(
                    localizer["Não há dados de OIFC (ovos incubáveis/fêmea) para o lote de aves nesse intervalo."]));
            }

            var chartOptions = new LayingChartOptionsDTO
            {
                Title = "OI/FC",
                XAxisLabel = $"Semanas ({weekStart} a {weekEnd})",
                YAxisLabel = "OIFC",
                YMin = 0,
                YMax = 220,
                YTickAmount = 11,
                TotalWeeks = totalWeeks
            };

            var chart = new LayingChart(chartOptions)
            {
                WeekOffset = weekStart
            };

            for (int week = weekStart; week <= weekEnd; week++)
            {
                chart.GetChart().Categories.Add(week.ToString());
            }

            if (realData.Any() && realData.First().GeneticId.HasValue)
            {
                AddStandardSeries(
                    chart,
                    realData.First().GeneticId.Value,
                    "OIFC (STD)",
                    redColor,
                    p => p.CumulativeIncubatedEggsByHen,
                    ""
                );
            }

            AddGroupedSeriesToChart(filters, chart, realData, "OIFC (Real)", blueColor);

            return chart.GetChart();
        }

        public DashboardLineOrBarChartDTO GetBedEggsChart(FilterDataDTO filters)
        {
            // set max date to last day of the month
            if (filters?.MaxDate != null)
            {
                filters.MaxDate = filters.MaxDate.Value.AddMonths(1).AddDays(-1);
            }

            var henBatchIds = GetHenBatchIdsFromFilters(filters).ToList();
            if (!henBatchIds.Any())
                throw exceptionManager.Handle(new NotFoundException("Nenhum lote encontrado."));

            int weekStart = 22;
            int weekEnd = 70;
            int totalWeeks = (weekEnd - weekStart + 1);

            var reportsQuery = henReportService.GetAll(asNoTracking: true)
               .Include(hr => hr.ClassifiedEggs)
               .ThenInclude(ce => ce.Material)
               .Include(hr => hr.HenBatch)
               .Where(hr => hr.HenBatch.HenStage == HenStage.Laying);

            var performancesQuery = henBatchPerformanceService.GetAll(asNoTracking: true)
               .Include(hbp => hbp.HenBatch)
               .Where(hbp => hbp.HenBatch.HenStage == HenStage.Laying);

            reportsQuery = reportsQuery.Where(hr => henBatchIds.Contains(hr.HenBatchId));
            performancesQuery = performancesQuery.Where(hbp => henBatchIds.Contains(hbp.HenBatchId));

            if (filters.MinDate.HasValue && filters.MaxDate.HasValue)
            {
                reportsQuery = reportsQuery.Where(hr => hr.Date >= filters.MinDate.Value
                                                     && hr.Date <= filters.MaxDate.Value);

                performancesQuery = performancesQuery.Where(hbp => hbp.Date >= filters.MinDate.Value
                                                               && hbp.Date <= filters.MaxDate.Value);
            }


            if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
            {
                performancesQuery = performancesQuery.Where(hbp =>
                    hbp.WeekNumber >= filters.StartWeek.Value &&
                    hbp.WeekNumber <= filters.EndWeek.Value);
            }

            var listReports = reportsQuery.ToList();
            var listPerformances = performancesQuery.ToList();

            if (!listReports.Any())
                throw exceptionManager.Handle(new NotFoundException("Não há HenReports para esse filtro."));

            if (!listPerformances.Any())
                throw exceptionManager.Handle(new NotFoundException("Não há dados de performance para esse filtro."));

            var reportWithWeeks = listReports
                .Select(hr =>
                {
                    var performance = listPerformances
                        .Where(hbp => hbp.HenBatchId == hr.HenBatchId)
                        .OrderBy(hbp => Math.Abs((hbp.Date - hr.Date).TotalDays))
                        .FirstOrDefault();

                    return new
                    {
                        Report = hr,
                        WeekNumber = performance?.WeekNumber ?? 0,
                        FloorEggs = hr.ClassifiedEggs
                                     .Where(ce => ce.Material.Name == "Cama")
                                     .Sum(ce => ce.Quantity),
                        hr.TotalEggs
                    };
                })
                .Where(x => x.WeekNumber >= weekStart && x.WeekNumber <= weekEnd)
                .ToList();

            if (!reportWithWeeks.Any())
                throw exceptionManager.Handle(new NotFoundException("Nenhum registro de ovos de cama no intervalo (22..70)."));

            var groupedByWeek = reportWithWeeks
                .GroupBy(x => x.WeekNumber)
                .Select(g => new
                {
                    Week = g.Key,
                    TotalEggs = g.Sum(r => r.TotalEggs),
                    FloorEggs = g.Sum(r => r.FloorEggs)
                })
                .OrderBy(x => x.Week)
                .ToList();

            var realDataPoints = groupedByWeek
                .Select(x => new LayingDataPointDTO
                {
                    WeekNumber = x.Week,
                    Value = x.TotalEggs > 0
                          ? 100m * x.FloorEggs / x.TotalEggs
                          : 0m
                })
                .ToList();

            var chartOptions = new LayingChartOptionsDTO
            {
                Title = "% Ovos Cama",
                XAxisLabel = "Semanas (22 a 70)",
                YAxisLabel = "% Ovos Cama",
                YMin = 0,
                YMax = 25,
                YTickAmount = 5,
                TotalWeeks = totalWeeks
            };
            var chart = new LayingChart(chartOptions);
            chart.WeekOffset = weekStart;

            for (int w = weekStart; w <= weekEnd; w++)
                chart.GetChart().Categories.Add(w.ToString());

            chart.AddSeries(new LayingChartSeriesDTO
            {
                SeriesLabel = "Ovos Cama (Real)",
                Color = blueColor,
                Type = "line",
                UnitSymbol = "%",
                Data = realDataPoints
                    .Select(d => new LayingChartPointDTO
                    {
                        WeekNumber = d.WeekNumber,
                        Value = d.Value
                    })
                    .ToList(),
                IsSecondaryAxis = false
            });

            return chart.GetChart();
        }


        #endregion

        #region Data queries        
        /// <summary>
        /// Get list of hen batch Ids contained in the given container. Only parents or only children (self contained batches belong to all categories)
        /// </summary>
        public IQueryable<Guid> GetHenBatchIdsFromFilters(FilterDataDTO filters)
        {
            // set max date to last day of the month
            if (filters?.MaxDate != null)
            {
                filters.MaxDate = filters.MaxDate.Value.AddMonths(1).AddDays(-1);
            }

            if (!(filters.StartWeek.HasValue && filters.EndWeek.HasValue) && !(filters.MinDate.HasValue && filters.MaxDate.HasValue))
            {
                throw exceptionManager.Handle(new ValidationException("Date range is required"));
            }

            var henBatchesQuery = henBatchService.GetAll()
                .Where(hb => hb.HenStage == HenStage.Laying);

            if (filters.HenBatchStatus == "active")
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Active && hb.DateEnd == null);
            }
            else if (filters.HenBatchStatus == "closed")
            {
                henBatchesQuery = henBatchesQuery.Where(hb => !hb.Active || hb.DateEnd != null);
            }

            if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb =>

                    hb.BatchWeekNumber <= filters.EndWeek.Value &&
                    (
                        (hb.DateEnd != null &&
                         (hb.BatchWeekNumber + (int)Math.Ceiling(EF.Functions.DateDiffDay(hb.DateStart.Value, hb.DateEnd.Value) / 7.0)) >= filters.StartWeek.Value) ||
                        (hb.DateEnd == null &&
                         (hb.BatchWeekNumber + (int)Math.Ceiling(EF.Functions.DateDiffDay(hb.DateStart.Value, DateTime.Today) / 7.0)) >= filters.StartWeek.Value)
                    )
                );
            }

            if (filters.ProductorId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.FarmId == filters.ProductorId);
            }

            if (filters.HenBatchId.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Id == filters.HenBatchId.Value || hb.ParentId == filters.HenBatchId.Value);
            }

            if (filters.WarehouseId.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Line.WarehouseId == filters.WarehouseId.Value);
            }

            if (filters.LineId.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.LineId == filters.LineId.Value);
            }

            if (filters.RegionalId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.Company.RegionalId == filters.RegionalId);
            }

            if (filters.UnitId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.CompanyId == filters.UnitId);
            }

            if (filters.SupervisorId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.SupervisorId == filters.SupervisorId);
            }

            if (filters.ExtensionistId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.TechnicianId == filters.ExtensionistId);
            }

            if (filters.MinDate.HasValue && filters.MaxDate.HasValue)
            {
                DateTime minDate = filters.MinDate.Value.Date;
                DateTime maxDate = filters.MaxDate.Value.Date;

                henBatchesQuery = henBatchesQuery.Where(hb =>
                    hb.DateStart <= maxDate &&
                    (hb.DateEnd ?? DateTime.Today) >= minDate
                );
            }

            return henBatchesQuery.Select(hb => hb.Id);
        }


        /// <summary>
        /// query for getting all the laying performances for the given filters
        /// </summary>
        private IQueryable<HatcheringReport> GetHatcheringReports(FilterDataDTO filters)
        {
            // set max date to last day of the month
            if (filters?.MaxDate != null)
            {
                filters.MaxDate = filters.MaxDate.Value.AddMonths(1).AddDays(-1);
            }

            IQueryable<HatcheringReport> query = hatcheringReportService.GetAll(asNoTracking: true)
            .Include(hr => hr.HenBatch)
            .Where(hr => hr.HenBatch.HenStage == HenStage.Laying);

            if (filters != null)
            {
                if (filters.MinDate != null && filters.MaxDate != null)
                {
                    query = query.Where(hr => hr.HatcheringDate >= filters.MinDate.Value && hr.HatcheringDate <= filters.MaxDate.Value);
                }

                if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
                {
                    query = query.Where(hr =>
                        hr.Week >= filters.StartWeek.Value &&
                        hr.Week <= filters.EndWeek.Value);
                }
            }

            var henBatchIds = GetHenBatchIdsFromFilters(filters);
            query = query.Where(hr => henBatchIds.Contains(hr.HenBatch.ParentId.Value) || henBatchIds.Contains(hr.HenBatchId));

            return query;
        }

        private IQueryable<HenBatchPerformance> GetSearaLayingPerformances(FilterDataDTO filters)
        {
            // set max date to last day of the month
            if (filters?.MaxDate != null)
            {
                filters.MaxDate = filters.MaxDate.Value.AddMonths(1).AddDays(-1);
            }

            // Get hen batch performance data
            IQueryable<HenBatchPerformance> query = this.henBatchPerformanceService.GetAll(asNoTracking: true)
                .Include(hbp => hbp.HenBatch)
                .Where(hbp => hbp.HenBatch.HenStage == HenStage.Laying && hbp.ParentId != null);

            if (filters != null)
            {
                if (filters.MinDate != null && filters.MaxDate != null)
                {
                    query = query.Where(hbp => hbp.Date >= filters.MinDate.Value && hbp.Date <= filters.MaxDate.Value);
                }

                if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
                {
                    query = query.Where(hbp =>
                        hbp.WeekNumber >= filters.StartWeek.Value &&
                        hbp.WeekNumber <= filters.EndWeek.Value);
                }
            }

            var henBatchIds = GetHenBatchIdsFromFilters(filters);
            query = query.Where(hbp => henBatchIds.Contains(hbp.HenBatch.ParentId.Value) || henBatchIds.Contains(hbp.HenBatchId));

            return query;
        }
        #endregion

        #region Chart data grouping
        private void AddGroupedSeriesToChart(FilterDataDTO filters, LayingChart chart, List<LayingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null, bool isSecondaryAxis = false)
        {
            // consolidated
            if (filters.ReportType == "1")
            {
                var consolidatedSeries = GetChartSeriesConsolidated(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                consolidatedSeries.IsSecondaryAxis = isSecondaryAxis;
                chart.AddSeries(consolidatedSeries);
            }
            // open
            else if (filters.ReportType == "2")
            {
                // if batch is not selected, display data by batch
                if (!filters.HenBatchId.HasValue)
                {
                    var batchSeries = GetChartSeriesByBatch(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                    batchSeries.ForEach(s => s.IsSecondaryAxis = isSecondaryAxis);
                    chart.AddSeries(batchSeries);
                }
                // if batch is selected, display data by warehouse
                else
                {
                    var warehouseSeries = GetChartSeriesByWarehouse(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                    warehouseSeries.ForEach(s => s.IsSecondaryAxis = isSecondaryAxis);
                    chart.AddSeries(warehouseSeries);

                    // if warehouse is selected, also display data by line
                    if (filters.WarehouseId.HasValue)
                    {
                        var lineSeries = GetChartSeriesByLine(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                        lineSeries.ForEach(s => s.IsSecondaryAxis = isSecondaryAxis);
                        chart.AddSeries(lineSeries);
                    }
                }
            }
        }

        #region Chart data grouping
        private void AddGroupedSeriesToChartWithColor(FilterDataDTO filters, LayingChart chart, List<LayingDataPointDTO> dataPoints, string seriesLabel, string color = null, string unitSymbol = "",
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null, bool isSecondaryAxis = false)
        {
            // consolidated
            if (filters.ReportType == "1")
            {
                var consolidatedSeries = GetChartSeriesConsolidated(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                consolidatedSeries.IsSecondaryAxis = isSecondaryAxis;
                consolidatedSeries.Color = color; // Set the color
                chart.AddSeries(consolidatedSeries);
            }
            // open
            else if (filters.ReportType == "2")
            {
                // if batch is not selected, display data by batch
                if (!filters.HenBatchId.HasValue)
                {
                    var batchSeries = GetChartSeriesByBatch(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                    batchSeries.ForEach(s =>
                    {
                        s.IsSecondaryAxis = isSecondaryAxis;
                        s.Color = color; // Set the color
                    });
                    chart.AddSeries(batchSeries);
                }
                // if batch is selected, display data by warehouse
                else
                {
                    var warehouseSeries = GetChartSeriesByWarehouse(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                    warehouseSeries.ForEach(s =>
                    {
                        s.IsSecondaryAxis = isSecondaryAxis;
                        s.Color = color; // Set the color
                    });
                    chart.AddSeries(warehouseSeries);

                    // if warehouse is selected, also display data by line
                    if (filters.WarehouseId.HasValue)
                    {
                        var lineSeries = GetChartSeriesByLine(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                        lineSeries.ForEach(s =>
                        {
                            s.IsSecondaryAxis = isSecondaryAxis;
                            s.Color = color; // Set the color
                        });
                        chart.AddSeries(lineSeries);
                    }
                }
            }
        }


        private List<LayingChartSeriesDTO> GetChartSeriesByBatch(List<LayingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null)
        {
            return GetChartSeriesByProperty(
                dataPoints,
                point => point.ParentHenBatchId,
                point => seriesLabel + " - " + point.ParentHenBatchName,
                unitSymbol,
                averagingFunction
            );
        }

        private List<LayingChartSeriesDTO> GetChartSeriesByWarehouse(List<LayingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null)
        {
            return GetChartSeriesByProperty(
                dataPoints,
                point => point.WarehouseId,
                point => seriesLabel + " - " + point.WarehouseName,
                unitSymbol,
                averagingFunction
            );
        }

        private List<LayingChartSeriesDTO> GetChartSeriesByLine(List<LayingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null)
        {
            return GetChartSeriesByProperty(
                dataPoints,
                point => point.LineId,
                point => seriesLabel + " - " + point.LineName,
                unitSymbol,
                averagingFunction
            );
        }

        private LayingChartSeriesDTO GetChartSeriesConsolidated(List<LayingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null)
        {
            return new LayingChartSeriesDTO
            {
                SeriesLabel = seriesLabel,
                UnitSymbol = unitSymbol,
                Data = GroupDataPointsByWeek(dataPoints.ToList(), averagingFunction)
            };
        }

        private List<LayingChartSeriesDTO> GetChartSeriesByProperty<TKey>(
            List<LayingDataPointDTO> dataPoints,
            Func<LayingDataPointDTO, TKey> groupingSelector,
            Func<LayingDataPointDTO, string> labelSelector,
            string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null)
        {
            return dataPoints
                .GroupBy(groupingSelector)
                .Select(groupedPoints => new LayingChartSeriesDTO
                {
                    SeriesLabel = labelSelector(groupedPoints.FirstOrDefault()),
                    UnitSymbol = unitSymbol,
                    Data = GroupDataPointsByWeek(groupedPoints.ToList(), averagingFunction)
                })
                .OrderBy(w => w.SeriesLabel)
                .ToList();
        }

        private List<LayingChartPointDTO> GroupDataPointsByWeek(List<LayingDataPointDTO> dataPoints, Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null)
        {
            return dataPoints
                .GroupBy(point => point.WeekNumber)
                .Select(a => new LayingChartPointDTO
                {
                    WeekNumber = a.Key,
                    Date = a.FirstOrDefault()?.Date ?? DateTime.MinValue,
                    Value = averagingFunction?.Invoke(a) ?? CalculateWeightedAverage(a)
                })
                .OrderBy(p => p.WeekNumber)
                .ToList();
        }

        private decimal CalculateWeightedAverage(IGrouping<int, LayingDataPointDTO> group)
        {
            var totalWeight = group.Sum(point => point.Value != 0 ? point.AveragingWeight : 0);
            if (totalWeight == 0) return 0;

            return group.Sum(point => point.Value * point.AveragingWeight) / totalWeight;
        }

        private void AddStandardSeries(LayingChart chart, Guid geneticId, string seriesLabel, string color,
            Func<GeneticsParametersReference, decimal> valueSelector, string unitSymbol = "%", bool isSecondaryAxis = false, string seriesType = "line")
        {
            chart.AddSeries(new LayingChartSeriesDTO()
            {
                SeriesLabel = seriesLabel,
                Data = geneticsParameterService.GetAll()
                    .Where(gpr => gpr.HenStage == HenStage.Laying && gpr.GeneticsId == geneticId)
                    .ToList()
                    .Select(p => new LayingChartPointDTO()
                    {
                        WeekNumber = p.TimePeriodValue,
                        Value = valueSelector(p)
                    }).ToList(),
                Color = color,
                Type = seriesType,
                UnitSymbol = unitSymbol,
                IsSecondaryAxis = isSecondaryAxis
            });
        }
        #endregion
    }

    public class LayingDataPointDTO
    {
        public DateTime Date { get; set; }
        public int WeekNumber { get; set; }
        public decimal Value { get; set; }
        public string SeriesLabel { get; set; }
        public int AveragingWeight { get; set; }
        public Guid? ParentHenBatchId { get; set; }
        public string ParentHenBatchName { get; set; }
        public Guid? WarehouseId { get; set; }
        public string WarehouseName { get; set; }
        public Guid? LineId { get; set; }
        public string LineName { get; set; }
        public Guid? GeneticId { get; set; }
        public int HenAmountMale { get; set; }
        public int HenAmountFemale { get; set; }
    }

    public class LayingChartPointDTO
    {
        public DateTime Date { get; set; }
        public int WeekNumber { get; set; }
        public decimal Value { get; set; }
    }

    public class LayingChartSeriesDTO
    {
        public string SeriesLabel { get; set; }
        public string UnitSymbol { get; set; } = "%";
        public string Color { get; set; }
        public string Type { get; set; } = "spline";
        public List<LayingChartPointDTO> Data { get; set; }
        public bool IsSecondaryAxis { get; set; } = false;
    }

    public class LayingChartOptionsDTO
    {
        public string Title { get; set; }
        public string XAxisLabel { get; set; }
        public string YAxisLabel { get; set; }
        public int? YMin { get; set; } = null;
        public int? YMax { get; set; } = null;
        public int? YTickAmount { get; set; } = null;
        public int? YTickInterval { get; set; } = null;
        public int TotalWeeks { get; set; } = 25;
    }

    public class LayingChart
    {
        DashboardLineOrBarChartDTO chart;
        int totalWeeks = 25;
        public int WeekOffset { get; set; } = 0;

        List<string> colors = new List<string>() { "#C042FB", "#2ECC71", "#F18A2E", "#2471A3", "#85C1E9", "#EDFB42", "#E74C3C", "#145A32", "#633974" };

        bool IsAutoRangeYAxis = true;

        public LayingChart(LayingChartOptionsDTO options)
        {
            this.chart = new DashboardLineOrBarChartDTO();
            this.chart.Title = options.Title;
            this.chart.XAxisLabel = options.XAxisLabel;
            this.chart.XAxisGridLineWidth = 1;
            this.chart.ToolTipShared = false;

            this.IsAutoRangeYAxis = options.YMin == null;

            this.chart.YAxis = new List<YAxis>
            {
                new YAxis()
                {
                    Min = options.YMin ?? int.MaxValue,
                    Max = options.YMax,
                    TickAmount = options.YTickAmount,
                    TickInterval = options.YTickInterval,
                    GridLineWidth = 1,
                    Opposite = false,
                    Title = new TitleDTO()
                    {
                        Text = options.YAxisLabel
                    }
                }
            };

            this.chart.Series = new List<DecimalSeriesDTO>();

            this.totalWeeks = options.TotalWeeks;
        }

        public void AddSeries(LayingChartSeriesDTO series)
        {
            List<decimal?> seriesValues = new List<decimal?>(new decimal?[totalWeeks]);

            foreach (var point in series.Data)
            {
                int index = point.WeekNumber - WeekOffset;
                if (index >= 0 && index < totalWeeks && point.Value != 0M)
                {
                    seriesValues[index] = decimal.Round(point.Value, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero);
                }
            }

            this.chart.Series.Add(new DecimalSeriesDTO
            {
                Name = series.SeriesLabel,
                Type = series.Type,
                Data = seriesValues,
                SeriesUnitsSymbol = series.UnitSymbol,
                ToolTip = new ToolTipDTO() { ValueSuffix = series.UnitSymbol },
                ShowInLegend = true,
                YAxis = series.IsSecondaryAxis ? 1 : 0,
                Color = series.Color ?? colors[chart.Series.Count % colors.Count]
            });

            if (this.IsAutoRangeYAxis)
            {
                this.chart.YAxis[series.IsSecondaryAxis ? 1 : 0].Min = (int)Math.Min(seriesValues.Where(d => d != null).Min() ?? 0, (decimal)chart.YAxis[series.IsSecondaryAxis ? 1 : 0].Min);
            }
        }

        public void AddSecondaryYAxis(string label, int? min = null, int? max = null, int? tickAmount = null)
        {
            this.chart.YAxis.Add(new YAxis()
            {
                Min = min ?? 0,
                Max = max,
                TickAmount = tickAmount,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = label }
            });
        }

        public void AutoRangeYAxis()
        {
            if (!this.IsAutoRangeYAxis)
            {
                return;
            }

            foreach (var series in chart.Series)
            {
                var seriesMinimum = series.Data.Where(d => d != null).Min();
                this.chart.YAxis[0].Min = (int)Math.Min(seriesMinimum ?? 0, (decimal)chart.YAxis[0].Min);
                if (chart.YAxis.Count > 1)
                {
                    this.chart.YAxis[1].Min = (int)Math.Min(seriesMinimum ?? 0, (decimal)chart.YAxis[1].Min);
                }
            }
        }

        public void AddSeries(List<LayingChartSeriesDTO> series)
        {
            foreach (var s in series)
            {
                AddSeries(s);
            }
        }

        public DashboardLineOrBarChartDTO GetChart()
        {
            return chart;
        }

    }
    #endregion
}