@using Binit.Framework;
@using Microsoft.Extensions.Localization;

@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Breeding.Inventory;

@inject IStringLocalizer<SharedResources> localizer;

@{
    var regionals = ViewData["Regionals"] as List<SelectListItem>;
    var units = ViewData["Units"] as List<SelectListItem>;
    var supervisors = ViewData["Supervisors"] as List<SelectListItem>;
    var extensionists = ViewData["Extensionists"] as List<SelectListItem>;
    var productors = ViewData["Productors"] as List<SelectListItem>;

    var materialTypes = ViewData["MaterialTypes"] as List<SelectListItem>;
    var materialTypeFormula = materialTypes[0];

    var materials = ViewData["Materials"] as List<SelectListItem>;
}


@section ViewStyles {
    <link href="~/css/views/inventory/index.css" rel="stylesheet" />
}


<!-- Start Filters  -->
<div id="filterContainer" class="container-fluid">
    <div class="row">
        <!-- Regional/Unit/Supervisor/Extensionist filters section -->
        <div class="filterGroup col-md-5 p-1 py-1 border rounded shadow-sm bg-light">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold"
                            for="regionalFilter">@localizer[Lang.RegionalSelectLabel]</label>
                        <select class="form-control select2" id="regionalFilter">
                            <option value="">@localizer[Lang.AllFemaleTextSelectPlaceholder]</option>
                            @if (regionals != null)
                            {
                                foreach (var item in (List<SelectListItem>)regionals)
                                {
                                    <option value="@item.Value">@item.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold" for="unitFilter">@localizer[Lang.UnitSelectLabel] </label>
                        <select class="form-control select2" id="unitFilter">
                            <option value="">@localizer[Lang.AllFemaleTextSelectPlaceholder]</option>
                            @if (units != null)
                            {
                                foreach (var item in (List<SelectListItem>)units)
                                {
                                    <option value="@item.Value">@item.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold"
                            for="supervisorFilter">@localizer[Lang.SupervisorUserSelectLabel]</label>
                        <select class="form-control select2" id="supervisorFilter">
                            <option value="">@localizer[Lang.AllMaleTextSelectPlaceholder]</option>
                            @foreach (var item in supervisors)
                            {
                                <option value="@item.Value">@item.Text</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="font-weight-bold"
                            for="extensionistFilter">@localizer[Lang.ExtensionistUserSelectLabel]</label>
                        <select class="form-control select2" id="extensionistFilter">
                            <option value="">@localizer[Lang.AllMaleTextSelectPlaceholder]</option>
                            @foreach (var item in extensionists)
                            {
                                <option value="@item.Value">@item.Text</option>
                            }
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Producer/Material filters section -->
        <div class="filterGroup col-md-6 p-1 border rounded shadow-sm bg-light">
            <div class="row">
                <div class="col-md-5">
                    <div class="form-group">
                        <label class="font-weight-bold"
                            for="productorFilter">@localizer[Lang.ProductorUserSelectLabel]</label>
                        <select class="form-control select2" id="productorFilter">
                            <option value="">@localizer[Lang.AllMaleTextSelectPlaceholder]</option>
                            @foreach (var item in productors)
                            {
                                <option value="@item.Value">@item.Text</option>
                            }
                        </select>
                    </div>
                </div>

                <div class="col-md-5">
                    <div class="form-group" id="materialTypeDiv" style="margin-right:0.5rem">
                        <label class="font-weight-bold" for="materialType">
                            @(localizer[Lang.TableMaterialType])
                        </label>
                        <select disabled class="form-control select2 filter" id="materialType">
                            <option value="@materialTypeFormula.Value">@materialTypeFormula.Text</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-5">
                    <div class="form-group" id="materialDiv" style="margin-right:0.5rem">
                        <label class="font-weight-bold" for="material">
                            @(localizer[Lang.MaterialLabel])
                        </label>
                        <select class="form-control select2 filter" id="material">
                            <option value="">@localizer[Lang.AllMaleTextSelectPlaceholder]</option>
                            @if (materials != null)
                            {
                                @foreach (var material in materials)
                                {
                                    <option value="@material.Value">@material.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12 d-flex align-items-center">
        <label class="font-weight-bold mr-2">@localizer[Lang.SortByLabel]:</label>
        <button class="btn btn-outline-secondary btn-sm mr-2 sort-button  active"
            data-sort="days">@localizer[Lang.SortByDays]</button>
        <button class="btn btn-outline-secondary btn-sm mr-2 sort-button"
            data-sort="material">@localizer[Lang.SortByMaterial]</button>
        <button class="btn btn-outline-secondary btn-sm sort-button"
            data-sort="stock">@localizer[Lang.SortByStock]</button>
    </div>
</div>

<div id="stockCardsContainer">
    <!-- Cards will be dynamically inserted here -->
</div>

@section scripts {
    <script>
        const localizedStrings = {
            emptyDataMessage: "@localizer[Lang.EmptyDataMessage]",
            stock: "@localizer[Lang.Stock]",
            days: "@localizer[Lang.Days]",
            noConsumption: "@localizer[Lang.NoConsumption]",

            serverError: "An internal server error occurred. Please try again later.",
            genericError: "An error occurred while fetching data. Please try again.",
        };
    </script>

    <script src="@Url.Content("~/js/views/managerialBreeding/inventory.js")" type="text/javascript"></script>
}

<ignite-load plugins="select2"></ignite-load>