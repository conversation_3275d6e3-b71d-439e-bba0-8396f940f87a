using DevExpress.XtraReports.UI;
using System.Drawing.Printing;
using DevExpress.XtraPrinting;
using System.Drawing;
using DevExpress.XtraPrinting.Drawing;
using Binit.Framework.Helpers.Reporting.Controls;
using System;
using Microsoft.Extensions.Localization;
using Binit.Framework.Interfaces.Configuration;
using Binit.Framework.Helpers.Reporting.Extensions;
using Lang = Binit.Framework.Localization.LocalizationConstants.BinitFramework.Helpers.Reporting.IgniteExtremeReport;

namespace Binit.Framework.Helpers.Reporting
{
    /// <summary>
    /// Ignite base report setup.
    /// </summary>
    public abstract class IgniteExtremeReport : XtraReport
    {
        #region Properties

        /// <summary>
        /// Report title
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Report subtitle (optional)
        /// </summary>
        public string Subtitle { get; set; }

        /// <summary>
        /// Report logo
        /// </summary>
        public string LogoFilename { get; set; }

        /// <summary>
        /// If set to true, repeats the header in all pages.
        /// </summary>
        public bool RepeatHeaderInEveryPage { get; set; }

        /// <summary>
        /// If set to true, repeats the footer in all pages.
        /// </summary>
        public bool RepeatFooterInEveryPage { get; set; }

        public DetailBand Content { get; set; }
        public GroupHeaderBand Header { get; set; }
        public GroupFooterBand Footer { get; set; }

        #endregion

        #region Fields

        protected readonly IStringLocalizer<SharedResources> localizer;
        protected readonly IGeneralConfiguration generalConfiguration;

        #endregion

        protected IgniteExtremeReport(string title, IStringLocalizer<SharedResources> localizer, IGeneralConfiguration generalConfiguration)
        {
            this.Title = title;
            this.DisplayName = title;
            this.PaperKind = PaperKind.Letter;
            this.Margins = new Margins(50, 50, 50, 50);
            this.LogoFilename = "wwwroot\\images\\logo-promanager.png";
            this.RepeatHeaderInEveryPage = true;
            this.RepeatFooterInEveryPage = true;

            this.localizer = localizer;
            this.generalConfiguration = generalConfiguration;
        }

        protected IgniteExtremeReport(string title, string subtitle, IStringLocalizer<SharedResources> localizer, IGeneralConfiguration generalConfiguration)
            : this(title, localizer, generalConfiguration)
        {
            this.Subtitle = subtitle;
        }

        /// <summary>
        /// Setup and build each report section.
        /// </summary>
        public void BuildReport()
        {
            this.Header = new GroupHeaderBand
            {
                RepeatEveryPage = this.RepeatHeaderInEveryPage
            };
            this.Bands.Add(this.Header);

            this.BuildHeader();

            this.Content = new DetailBand();
            this.Bands.Add(this.Content);

            this.BuildContent();

            this.Footer = new GroupFooterBand
            {
                RepeatEveryPage = true
            };
            this.Bands.Add(this.Footer);

            this.BuildFooter();
        }

        /// <summary>
        /// Generates the header for the report within the Header band.
        /// Override to generate a different Header.
        /// </summary>
        public virtual void BuildHeader()
        {
            var headerPanel = new XRPanel
            {
                SizeF = new SizeF(width: 0, height: 150) // Width will be set later by the FitParentHorizontally() method.
            };

            this.Header.Controls.Add(headerPanel);
            headerPanel.FitParentHorizontally();

            headerPanel.AddSimpleBorder();

            // Add title.
            var titleLabel = IgniteReportLabel.CreateHeader1(this.Title);
            titleLabel.SizeF = new SizeF(400, 100);

            headerPanel.Controls.Add(titleLabel);
            titleLabel.CenterInParent();

            // Add logo.
            var logoPictureBox = new XRPictureBox();
            var imagesrc = new Bitmap(this.LogoFilename);
            logoPictureBox.ImageSource = new ImageSource(imagesrc);
            logoPictureBox.Sizing = ImageSizeMode.Squeeze;
            logoPictureBox.SizeF = new SizeF(150, 150);
            logoPictureBox.ImageAlignment = ImageAlignment.MiddleLeft;
            logoPictureBox.Padding = new PaddingInfo(20, 0, 0, 0);

            headerPanel.Controls.Add(logoPictureBox);
            logoPictureBox.CenterVerticallyInParent();

            // Add subtitle.
            if (!string.IsNullOrEmpty(this.Subtitle))
            {
                var subtitleLabel = IgniteReportLabel.CreateHeader3(this.Subtitle, shrinkable: true);
                subtitleLabel.SizeF = new SizeF(400, 50);

                headerPanel.Controls.Add(subtitleLabel);
                subtitleLabel.CenterHorizontallyInParent();
                subtitleLabel.PositionBelow(titleLabel, marginTop: 20, measureExact: true);
            }

            headerPanel.UndoBorderPropagation();

            // Add extra info.
            var extraInfoPanel = new XRPanel
            {
                SizeF = new SizeF(width: 0, height: 50)
            };

            extraInfoPanel.AddSimpleBorder();
            extraInfoPanel.Borders = BorderSide.Bottom | BorderSide.Left | BorderSide.Right;

            this.Header.Controls.Add(extraInfoPanel);
            extraInfoPanel.FitParentHorizontally();
            extraInfoPanel.PositionBelow(headerPanel, marginTop: 0);

            var dateNow = DateTime.Now;

            var hourLabeledAttribute = new IgniteReportLabeledTextAttribute(this.localizer[Lang.TimeLabel], dateNow.ToString("HH:mm"), horizontal: false);
            extraInfoPanel.Controls.Add(hourLabeledAttribute);

            var dayLabeledAttribute = new IgniteReportLabeledTextAttribute(this.localizer[Lang.DayLabel], dateNow.ToString("dd"), horizontal: false);
            dayLabeledAttribute.PositionRight(hourLabeledAttribute, marginLeft: 50);
            extraInfoPanel.Controls.Add(dayLabeledAttribute);

            var monthLabeledAttribute = new IgniteReportLabeledTextAttribute(this.localizer[Lang.MonthLabel], dateNow.ToString("MM"), horizontal: false);
            monthLabeledAttribute.PositionRight(dayLabeledAttribute, marginLeft: 50);
            extraInfoPanel.Controls.Add(monthLabeledAttribute);

            var yearLabeledAttribute = new IgniteReportLabeledTextAttribute(this.localizer[Lang.YearLabel], dateNow.ToString("yyyy"), horizontal: false);
            yearLabeledAttribute.PositionRight(monthLabeledAttribute, marginLeft: 50);
            extraInfoPanel.Controls.Add(yearLabeledAttribute);

            extraInfoPanel.UndoBorderPropagation();
        }

        /// <summary>
        /// Generates the content for the report within the Content band.
        /// Implement to setup the content of your report.
        /// </summary>
        public abstract void BuildContent();

        /// <summary>
        /// Generates the footer of the report within the Footer band.
        /// Override to generate a different footer.
        /// </summary>
        public virtual void BuildFooter()
        {
            var footerLblText = $"{this.generalConfiguration.SystemName} - {VersionHelper.GetVersion()}";
            var footerLabel = IgniteReportLabel.CreateSmallLabel(footerLblText, shrinkable: false, TextAlignment.BottomRight);

            this.Footer.Controls.Add(footerLabel);
            footerLabel.FitParentHorizontally();

            this.Footer.HeightF = footerLabel.HeightF;
        }
    }
}