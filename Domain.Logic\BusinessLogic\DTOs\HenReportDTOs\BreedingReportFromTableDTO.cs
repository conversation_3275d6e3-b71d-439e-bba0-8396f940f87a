using System;
using System.Collections.Generic;

namespace Domain.Logic.BusinessLogic.DTOs.HenReportDTOs
{
    public class BreedingReportFromTableDTO
    {
        public Guid HenBatchId { get; set; }

        public decimal WaterConsumption { get; set; }
        public decimal WaterPh { get; set; }
        public decimal? WaterChlorineConcentration { get; set; }
        public decimal WaterPillQuantity { get; set; }
        public decimal MinTemp { get; set; }
        public decimal MaxTemp { get; set; }
        public decimal Humidity { get; set; }

        public decimal FeedIntakeFemale { get; set; }
        public Guid FeedIntakeFemaleOriginId { get; set; }
        public decimal FeedIntakeMale { get; set; }
        public Guid FeedIntakeMaleOriginId { get; set; }

        public int DeadMale { get; set; }
        public int DeadFemale { get; set; }

        public int HenAmountFemale { get; set; }
        public int HenAmountMale { get; set; }
    }

    public class CreateBreedingReportFromTableDTO
    {
        public string ReportDate { get; set; }
        public List<BreedingReportFromTableDTO> Reports { get; set; }
    }
}
