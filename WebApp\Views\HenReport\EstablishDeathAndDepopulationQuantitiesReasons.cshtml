@model Domain.Logic.BusinessLogic.DTOs.HenReportDTOs.EstablishDeathQuantitiesReasonsDTO
@using Binit.Framework
@using Microsoft.Extensions.Localization
@using Domain.Entities.Model;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.HenReport.EstablishDeathQuantitiesReasons
@inject IStringLocalizer<SharedResources> localizer
@{
    var henReportId = ViewData["HenReportId"] as Guid?;
    var henReports = ViewData["HenReports"] as List<Guid>;
    var nextHenReport = ViewData["NextHenReport"] as string;
    var subTitles = ((string deadSubtitle, string depopulateSubtitle))ViewData["SubTitles"];
    var quantities = ((int deadFemale, int deadMale, int depopulateFemale, int depopulateMale))ViewData["Quantities"];
    var casualtyReasonsCount = ViewData["CasualtyReasonsCount"] as int?;
    var henStage = ViewData["HenStage"] as HenStage?;
    string informativeMessage = ViewData["Message"] as string;
    bool fromCreation = (bool)ViewData["FromCreation"];
}

<link href="~/css/handsontable.css" rel="stylesheet" />
<style>
    #hot-display-license-info {
        display: none
    }

    .ht_clone_top {
        z-index: 1
    }
</style>

<div class="row">
    <div class="col-md-6 mt-2">
        <ignite-input for-property="Farm" disabled></ignite-input>
    </div>

    <div class="col-md-6 mt-2">
        <ignite-input for-property="HenBatch" disabled></ignite-input>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mt-2">
        <ignite-input for-property="Date" disabled></ignite-input>
    </div>
</div>


<div class="row">
    <div class="container col-md-4 mt-2">
        <div style="margin-bottom: 20px">
            <h2>@subTitles.deadSubtitle</h2>
        </div>
        <div class="quantities-table" id="dead" type="dead"></div>
    </div>

    <div class="container col-md-4 mt-2">
        <div style="margin-bottom: 20px" id="depopulate-subtitle">
            <h2>@subTitles.depopulateSubtitle</h2>
        </div>
        <div class="quantities-table" type="depopulate"></div>
    </div>
</div>


<div class="d-flex justify-content-end" style="margin-top: 20px">
    @if (!fromCreation)
    {
        <button class="btn btn-secondary mr-2"
            onclick="window.location.href='@Url.Action("Index","HenReport", new { henStage = henStage })'">@localizer[Lang.BtnBack]</button>
    }
    <button id="edit-quantities-table" class="btn btn-themecolor">
        @(henReports.Count() <= 1 ? localizer[Lang.BtnEditDeathQuantities] : localizer[Lang.BtnEditAndContinue,
                nextHenReport])
    </button>
</div>

@section scripts {
    <script>
        let henReportId = '@henReportId';
        let henReports = '@Json.Serialize(henReports)';
        let messages = @Json.Serialize(ViewData["Messages"]);
        let hideDepopulate = @Json.Serialize(ViewData["HideDepopulate"]);
        let quantities = {
            dead: { female: '@quantities.deadFemale', male: '@quantities.deadMale' },
            depopulate: { female: '@quantities.depopulateFemale', male: '@quantities.depopulateMale' }
        }
        let henStage = '@henStage';
        const informativeMessage = '@Html.Raw(informativeMessage)';
    </script>
    <script src="~/js/handsontable.js"></script>
    <script src="@Url.Content("~/js/views/henReport/establishDeathQuantitiesReasons.js")" type="text/javascript"></script>
}