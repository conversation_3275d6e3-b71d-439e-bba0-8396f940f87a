﻿@model WebApp.Models.PlannerViewModel.PlannerViewModel

@using Binit.Framework;
@using Microsoft.Extensions.Localization;
@using Domain.Entities.Model;

@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Planner.GAD.Male;

@inject IStringLocalizer<SharedResources> localizer;

@{
    var henstage = ViewData["HenStage"] as HenStage?;
    var henbatchesParent = ViewData["HenbatchesParent"] as List<SelectListItem>;
    var henbatchesChild = new List<SelectListItem>();
    var warehouses = ViewData["Warehouses"] as List<SelectListItem>;
}

@section ViewStyles{
    <link href="~/css/handsontable.css" rel="stylesheet" />
    <link href="~/css/handsontable/plannerGAD.css" rel="stylesheet" />
    <link href="~/css/jquery-chosen.css" rel="stylesheet" />
}

<input type="hidden" id="hen-stage" value="@henstage" />
<input type="hidden" id="henbatch-selected" />

<!-- Initial filter -->
<div class="d-flex justify-content-start align-items-center ">

    <div class="form-group col-3">
        <label for="henBatch">@localizer[Lang.HenbatchParentLabel]</label>
        <select class="form-control select2" id="parent-henbatch-selector">
            <option value="" selected disabled>@localizer[Lang.PlaceholderHenbatchParent]</option>
            @foreach (var batch in henbatchesParent)
            {
                <option value="@batch.Value">@batch.Text</option>
            }
        </select>
    </div>

    <div class="form-group col-2" id="WHDiv" style="margin-left:0.5rem;width: 30rem;">
        <label for="warehouse">
            @(localizer[Lang.LineNumberLabel])
        </label>
        <select class="form-control select2" id="warehouse">
            <option value="" select>
                @(localizer[Lang.PlaceholderLineNumber])
            </option>
            @if (warehouses != null)
            {
                @foreach (var item in warehouses)
                {
                    <option warehouse value="@item.Value">@item.Text</option>
                }
            }
        </select>
    </div>

    <div class="col-2">
        <button id="btn-filter-planner" class="btn btn-primary m-0">@localizer[Lang.BtnFilterLabel]</button>
    </div>

</div>

<div class="d-flex">
    <div class="col-3 form-group">
        <label for="farm-name">@localizer[Lang.FarmNameLabel]</label>
        <input id="farm-name" name="farm-name" type="text" class="form-control" disabled />
    </div>

    <div class="col-2 form-group">
        <label for="production-date">@localizer[Lang.ProductionDateLabel]</label>
        <input id="production-date" name="production-date" type="text" class="form-control" disabled />
    </div>

    <div class="col-2 form-group">
        <label for="genetic-name">@localizer[Lang.GeneticLabel]</label>
        <input id="genetic-name" name="genetic-name" type="text" class="form-control" disabled />
    </div>

    <div class="col-2 form-group">
        <label for="AvgBirdWeight">@localizer[Lang.AvgBirdWeight]</label>
        <input id="AvgBirdWeight" name="AvgBirdWeight" type="text" class="form-control" disabled />
    </div>

    <div class="col-2 form-group">
        <label for="AvgGAD">@localizer[Lang.AvgGAD]</label>
        <input id="AvgGAD" name="AvgGAD" type="text" class="form-control" disabled />
    </div>
</div>
<hr />

<div class="d-flex justify-content-end align-items-center">
    <div class="col-2 text-right">
        <a id="export" class="btn btn-primary excel mr-2">
            <i class="fa fa-file-excel m-l-5"></i>
            @(localizer[Lang.BtnExportAll])
        </a>
    </div>
    <div class="col-2 text-right">    
        <button id="btn-save-planner" class="btn btn-primary m-0" data-save-label="@localizer[Lang.SaveOnlyLabel]" data-next-label="@localizer[Lang.SaveAndNextLabel]">@localizer[Lang.SaveOnlyLabel]</button>
    </div>
</div>

<!-- Planner container table-->
<div class="col-12 mt-3">
    <div id="planner-tables-container">
        <!-- As tabelas serão adicionadas dinamicamente aqui -->
    </div>
</div>


@section scripts {
    <script>
        const plannerModel = @Html.Raw(Model.Serialize());
    </script>
    <script src="~/lib/jquery-chosen/jquery-chosen.js"></script>
    <script src="~/js/handsontable.js"></script>
    <script src="~/js/handsontable-chosen-editor.js"></script>
    <script src="@Url.Content("~/js/views/planner/gad/utils/index.js")" type="text/javascript"></script>

    <script>
        const localizedStrings = {
            noDataMessage: "@localizer[Lang.NoDataMessage]",
            @* errorLoadingData: "@localizer[Lang.ErrorLoadingData]" *@
        };
    </script>

    <script src="@Url.Content("~/js/views/planner/gad/male.js")" type="text/javascript"></script>
}

<ignite-load plugins="select2"></ignite-load>