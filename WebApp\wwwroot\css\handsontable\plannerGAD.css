#planner-tables-container {
    display: flex;
    flex-wrap: nowrap; /* <PERSON><PERSON> quebrar linha, forçar tabelas lado a lado */
    overflow-x: auto!important; /* Adicionar scroll horizontal no container */
    width: 100%; /* Garantir que o container ocupe toda a largura */
    box-sizing: border-box; /* Incluir padding na largura total */
}

/* #planner-tables-container > table {
    min-width: 825px;
} */

/* colunas da tabela */
.ht_clone_top {
    z-index: 2;
}

#btn-filter-planner:disabled {
    background:#6772e5;
    border-color:#6772e5;
    pointer-events: none;
    /* cursor: not-allowed; Cursor indicando que não é clicável */
    opacity: 0.5; /* Transparência para reforçar o estado desabilitado */
}