@model WebApp.Models.TenantConfigurationViewModel
@using Binit.Framework
@using Microsoft.Extensions.Localization
@using WebApp.WebTools
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Tenant.Configure
@inject IStringLocalizer<SharedResources> localizer

@{
    var action = ViewData["Action"] as string;
    var submitLabel = localizer[Lang.BtnConfigure];
    var genetics = ViewData["Genetics"] as List<SelectListItem>;
}

    <nav>
        <div class="nav nav-tabs" id="nav-tab" role="tablist">
            <a class="nav-item nav-link active" id="nav-classification-report-excel-tab" data-toggle="tab" href="#nav-classification-report-excel" role="tab" aria-controls="nav-classification-report-excel" aria-selected="true">@localizer[Lang.ClassificationReportExcel]</a>
            <a class="nav-item nav-link" id="nav-logo-tab" data-toggle="tab" href="#nav-logo" role="tab" aria-controls="nav-logo" aria-selected="false">@localizer[Lang.LogoButtonTitle]</a>
            <a class="nav-item nav-link" id="nav-logo-small-tab" data-toggle="tab" href="#nav-logo-small" role="tab" aria-controls="nav-logo-small" aria-selected="false">@localizer[Lang.LogoSmallButtonTitle]</a>
            <a class="nav-item nav-link" id="nav-login-image-tab" data-toggle="tab" href="#nav-login-image" role="tab" aria-controls="nav-login-image" aria-selected="false">@localizer[Lang.LoginImageButtonTitle]</a>
            <a class="nav-item nav-link" id="nav-certifications-tab" data-toggle="tab" href="#nav-certifications" role="tab" aria-controls="nav-certifications" aria-selected="false">@localizer[Lang.CertificationsLabel]</a>
        </div>
    </nav>
    <div class="tab-content" id="nav-tabContent">
        <div class="tab-pane show active" id="nav-classification-report-excel" role="tabpanel" aria-labelledby="nav-classification-report-excel-tab">
            @await Html.PartialAsync("_FileManager", Model.ClassificationReportExcel,
            new FileManagerOptions(ViewData, localizer)
            {
                AcceptedMimeTypes = ".xlsx,.xls",
                UploadEnabled = true,
                MaxFiles = 1,
                ParentFormId = "tenant-form",
                PropertyName = "ClassificationReportExcelId",
                Title = string.Empty
            }
            )
        </div>
 
        <div class="tab-pane" id="nav-logo" role="tabpanel" aria-labelledby="nav-logo-tab">
            @await Html.PartialAsync("_FileManager", Model.Logo,
            new FileManagerOptions(ViewData, localizer)
            {
                AcceptedMimeTypes = "image/jpeg,image/jpeg,image/png",
                UploadEnabled = true,
                MaxFiles = 1,
                ParentFormId = "tenant-form",
                PropertyName = "LogoId",
                Title= string.Empty
            })
        </div>

        <div class="tab-pane" id="nav-logo-small" role="tabpanel" aria-labelledby="nav-logo-small-tab">
            @await Html.PartialAsync("_FileManager", Model.LogoSmall,
            new FileManagerOptions(ViewData, localizer)
            {
                UploadEnabled = true,
                MaxFiles = 1,
                ParentFormId = "tenant-form",
                PropertyName = "LogoSmallId",
                AcceptedMimeTypes = "image/jpeg,image/jpeg,image/png",
                Title = string.Empty
            })
        </div>

        <div class="tab-pane" id="nav-login-image" role="tabpanel" aria-labelledby="nav-login-image-tab">
            @await Html.PartialAsync("_FileManager", Model.LoginImage,
            new FileManagerOptions(ViewData, localizer)
            {
                UploadEnabled = true,
                MaxFiles = 1,
                ParentFormId = "tenant-form",
                PropertyName = "LoginImageId",
                AcceptedMimeTypes = "image/jpeg,image/jpeg,image/png",
                Title = string.Empty
            })
        </div>

        <div class="tab-pane" id="nav-certifications" role="tabpanel" aria-labelledby="nav-certifications-tab">
            @await Html.PartialAsync("_FileManager", Model.Certifications,
            new FileManagerOptions(ViewData, localizer)
            {
                UploadEnabled = true,
                MaxFiles = 5,
                ParentFormId = "tenant-form",
                PropertyName = "CertificationsIds",
                AcceptedMimeTypes = "image/jpeg,image/jpeg,image/png",
                Title = string.Empty
            })
        </div>
    </div>


    <form id="tenant-form" class="floating-labels" method="POST" action="@action">
        <input type="hidden" asp-for="TenantId" />

        <div class="row align-items-end">
            <div class="col-md-2">
                <ignite-checkbox for-property="DepopulateBreeding" color="#29313e"></ignite-checkbox>
            </div>
            <div class="col-md-2">
                <ignite-checkbox for-property="DepopulateLaying" color="#29313e"></ignite-checkbox>
            </div>
            <div class="col-md-2">
                <ignite-checkbox for-property="ToCageToFloorBreeding" color="#29313e"></ignite-checkbox>
            </div>
            <div class="col-md-2">
                <ignite-checkbox for-property="ToCageToFloorLaying" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="AutomaticallySentEggs" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="ClassificationProducesStock" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="FoodOrdering" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="HasAutomaticApprovalInconsistencies" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="HasFeedFactory" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="HasSectors" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="HasClusters" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="HasVehicles" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="HasHenBatchCategories" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="ReportsHenWarehouseAndParentBatchWeightMeasurement" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="GroupLayingEggsByMaterialTypeCommercial" color="#29313e" css-classes="groupLayingEggsByMaterialTypeCommercial-checkbox"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="GroupLayingEggsByMaterialTypeHatching" color="#29313e" css-classes="groupLayingEggsByMaterialTypeHatching-checkbox"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="AllowToReceiveBirdsBeforeStartDate" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="AllowToRecibeNotifications" color="#29313e"></ignite-checkbox>
            </div>

            <div class="col-md-2">
                <ignite-checkbox for-property="HasSerology" color="#29313e"></ignite-checkbox>
            </div>
        </div>

        <h5>@localizer[Lang.SwitchesLabel]</h5>
        <div class="row align-items-end">

            <div class="col-md-2">
                <ignite-checkbox for-property="ApproveInconsistencyClassification" color="#29313e" css-classes="inconsistency-checkbox"></ignite-checkbox>
            </div>
            <div class="col-md-2">
                <ignite-checkbox for-property="ApproveInconsistencyMaterialReception" color="#29313e" css-classes="inconsistency-checkbox"></ignite-checkbox>
            </div>
            <div class="col-md-2">
                <ignite-checkbox for-property="ApproveInconsistencyMaterialExit" color="#29313e" css-classes="inconsistency-checkbox"></ignite-checkbox>
            </div>
            <div class="col-md-2">
                <ignite-checkbox for-property="ApproveInconsistencyPacking" color="#29313e" css-classes="inconsistency-checkbox"></ignite-checkbox>
            </div>

        </div>

        <div class="row d-flex align-items-end">
            <div class="col-6">
                <ignite-dropdown for-property="@Model.GeneticsIds"
                                 items="@genetics"
                                 multiple="true">
                </ignite-dropdown>
            </div>
        </div>
        <button type="button" class="btn btn-secondary mr-2"
                onclick="window.location.href='@Url.Action("Index","Tenant")'">
            @(localizer[Lang.BtnCancel])
        </button>
        <button type="submit" class="btn btn-themecolor">@(submitLabel)</button>
    </form>

@section Scripts{
    <script>
        var hasPendingReviews = '@Model.HasPendingReviews';
        var TenantConfigureResources = @Json.Serialize(ViewData["TenantConfigureResources"]);
    </script>
    <script src="~/js/views/tenant/configure.js"></script>
}
<ignite-load plugins="dropzone,select2,switchery"></ignite-load>



