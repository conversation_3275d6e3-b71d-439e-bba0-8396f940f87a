using System;
using System.ComponentModel.DataAnnotations;

public class HenBatchConsumptionDTO
{
    [Required]
    public Guid HenBatchId { get; set; }

    [Required]
    public Guid MaterialId { get; set; }

    [Required]
    public int WeekNumber { get; set; }

    [Required]
    public DateTime Date { get; set; }

    [Required]
    public decimal TotalConsumption { get; set; }

    public decimal DailyConsumption { get; set; }

    [Required]
    public int DaysWithMovement { get; set; }
}