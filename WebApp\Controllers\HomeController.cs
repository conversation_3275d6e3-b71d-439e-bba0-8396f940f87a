﻿using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.HomeController;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Net;
using WebApp.Models;
using Binit.Framework.Interfaces.ExceptionHandling;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace WebApp.Controllers
{
    [Authorize]
    public class HomeController : DashboardController
    {
        public HomeController(
            IExceptionManager exceptionManager,
            IHenBatchBusinessLogic henBatchBusinessLogic,
            IHenBatchService henBatchService,
            IFarmService farmService,
            IStringLocalizer<SharedResources> localizer,
            IMessageBusinessLogic messageBusinessLogic,
            IStatisticsBusinessLogic statisticsBusinessLogic,
            IHolidayService holidayService,
            IContainerService<Container> containerService,
            ITaskEntityService taskEntityService,
            IUserService<ApplicationUser> userService,
            IServiceTenantDependent<TenantDependentEntityFile> fileService,
            IHappeningBusinessLogic happeningBusinessLogic,
            IMaterialTypeService materialTypeService,
            IOperationContext operationContext,
            ILogger logger)
            : base(
                  exceptionManager,
                  henBatchBusinessLogic,
                  henBatchService,
                  farmService,
                  localizer,
                  messageBusinessLogic,
                  statisticsBusinessLogic,
                  holidayService,
                  containerService,
                  taskEntityService,
                  userService,
                  fileService,
                  happeningBusinessLogic,
                  materialTypeService,
                  operationContext,
                  logger)
        {
        }

        #region Index
        public IActionResult Index()
        {
            ViewData["HomeIndexResources"] = JsLocalizer.GetLocalizedResources(JsLang.HomeIndex, this.localizer);
            // Users
            List<string> roles = operationContext.GetUserRoles().ToList();
            Guid userId = operationContext.GetUserId();
            // Areas
            List<AreaEnum> areas = statisticsBusinessLogic.GetAuthorizedAreas(roles);

            ViewData["Areas"] = areas;

            ViewData["IsHomeDashboard"] = true;

            ViewData["UserIsAdmin"] = this.operationContext.UserIsInAnyRole(Roles.BackofficeTaskAdministrator,
                                                                            Roles.BackofficeTaskUser,
                                                                            Roles.BackofficeSuperAdministrator).ToString();

            // Messages
            List<MessageViewModel> messages = GetMessages(areas, userId, all: true, 0);
            ViewData["MessagesData"] = messages;
            // Calendar
            InitCalendar(areas, true);


            var user = userService.GetAll(filterByTenant: true).Where(u => u.Id == userId).FirstOrDefault();
            ViewData["UserLanguage"] = user.Language;

            return View();
        }
        #endregion

        #region Privacy
        public IActionResult Privacy()
        {
            ViewData["Title"] = this.localizer[Lang.PrivacyTitle];

            return View();
        }
        #endregion

        #region Get
        private List<CardsEnum> GetCardsByArea(List<string> roles, string area)
        {
            List<CardsEnum> cards = new List<CardsEnum>();

            if (roles.Any(rol => rol.Contains("Happening") || rol.Contains(area + "Administrator")) || roles.Contains(Roles.BackofficeSuperAdministrator))
                cards.Add(CardsEnum.Happening);
            if (roles.Any(rol => rol.Contains("Inconsistencies") || rol.Contains("Inconsistency") || rol.Contains(area + "Administrator")) || roles.Contains(Roles.BackofficeSuperAdministrator))
                cards.Add(CardsEnum.Inconsistencies);

            return cards;
        }

        private string GetAllUsers(Guid userId)
        {
            List<SelectListItem> users = userService.GetAll(asNoTracking: true, filterByTenant: true).Where(u => u.Id != userId).OrderBy(u => u.LastName).Select(u => new SelectListItem { Value = u.Id.ToString(), Text = u.LastName + ", " + u.Name }).ToList();
            string usershtml = "<select id = 'dropd' class='form-control' multiple='multiple'>";
            foreach (SelectListItem user in users)
            {
                usershtml = usershtml + "<option id=" + user.Value + ">" + user.Text + "</option>";
            }
            usershtml += "</select>";

            return usershtml;
        }
        #endregion

        #region Auxiliar methods

        // Update tasks card
        [HttpPost]
        public IActionResult RefreshTasksCardData(string areaString)
        {
            AreaEnum? area = null;
            if (!string.IsNullOrEmpty(areaString))
                area = EnumHelper<AreaEnum>.Parse(areaString);

            CardDTO cardDTO = this.statisticsBusinessLogic.GetCardsData(new List<CardsEnum>() { CardsEnum.TaskAlerts }, area).CardList.Where(card => card.CardEnum == CardsEnum.TaskAlerts).FirstOrDefault();
            if (cardDTO != null)
                return new JsonResult(new { cardDTO });

            this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return new JsonResult(new { message = this.localizer[Lang.TasksCardDataRefreshError] });
        }


        #endregion

        #region Private methods
        private List<SelectListItem> GetRelevances() =>
            EnumUtil.GetValues<RelevanceEnum>().OrderByDescending(r => (int)r)
                .Select(r => new SelectListItem()
                {
                    Value = r.ToString(),
                    Text = EnumHelper<RelevanceEnum>.GetDisplayName(r, localizer)
                }).ToList();
        #endregion
    }
}
