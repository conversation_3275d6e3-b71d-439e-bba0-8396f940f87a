using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Localization;
using Lang = Binit.Framework.Localization.LocalizationConstants.BinitFramework.Attributes.FutureDatesNotAllowed;

namespace Binit.Framework.Attributes
{
    public class FutureDatesNotAllowedAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var localizer = (IStringLocalizer<SharedResources>)validationContext.GetService(typeof(IStringLocalizer<SharedResources>));

            if (value is null)
            {
                return ValidationResult.Success;
            }

            if (value is Guid)
            {
                return ValidationResult.Success;
            }

            DateTime date = value is string ? DateTime.Parse((string)value) : (DateTime)value;
            if (date < DateTime.UtcNow)
            {
                return ValidationResult.Success;
            }

            return new ValidationResult(ErrorMessage is null ? localizer[Lang.FutureDateError] : localizer[ErrorMessage]);
        }

    }
}