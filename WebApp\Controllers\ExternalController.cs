using Domain.Entities.Model;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Binit.Framework.Helpers;
using Domain.Logic.Interfaces;
using System.Security.Claims;
using Binit.Framework.Helpers.Jwt;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;

namespace WebApp.Controllers
{
    /// <summary>
    /// Controller that handles external users operations.
    /// </summary>
    [AllowAnonymous]
    public class ExternalController : Controller
    {
        private readonly JWTHelper jwtHelper;
        private readonly SignInManager<ApplicationUser> signInManager;
        private readonly IAccountService accountService;

        public ExternalController(J<PERSON><PERSON>elper jwtHelper, SignInManager<ApplicationUser> signInManager, IAccountService accountService)
        {
            this.jwtHelper = jwtHelper;
            this.signInManager = signInManager;
            this.accountService = accountService;
        }

        /// <summary>
        /// Validate token, sign-in external user and redirect to the specified page.
        /// </summary>
        /// <param name="token">External user website token</param>
        /// <param name="redirectUrl">URL to a local controller/action (relative url)</param>
        /// <param name="callback">Where should the user be taken after succesfully finishing an operation (absolute url)</param>
        [HttpGet("/External/Redirect/{token}")]
        public async Task<IActionResult> RedirectTo([FromRoute]string token, string redirectUrl = null, string callback = null)
        {
            ClaimsPrincipal principal = jwtHelper.GetPrincipalFromToken(token);
            var email = principal.FindFirstValue(ClaimTypes.Name);

            var externalUser = await accountService.GetUser(email);
            await this.signInManager.SignInAsync(externalUser, new AuthenticationProperties());

            if (string.IsNullOrEmpty(redirectUrl))
                redirectUrl = "/Home";

            if (!string.IsNullOrEmpty(callback))
            {
                if (!redirectUrl.Contains("?"))
                    redirectUrl += $"?callback={callback}";
                else
                    redirectUrl += $"&callback={callback}";
            }

            return Redirect(redirectUrl);
        }

        /// <summary>
        /// Log off an external user.
        /// </summary>
        /// <param name="callback">Where should the user be taken after being succesfully logged off</param>
        [HttpGet]
        public async Task<IActionResult> Logout(string callback = null)
        {
            await this.HttpContext.SignOutAsync();

            await this.accountService.Logout();

            if (callback != null)
            {
                return Redirect(callback);
            }
            else
            {
                return LocalRedirect("/Identity/Account/Logout");
            }
        }

        /// <summary>
        /// Temporary page displayed before a callback.
        /// </summary>
        [HttpGet]
        public IActionResult Callback()
        {
            return View();
        }
    }
}