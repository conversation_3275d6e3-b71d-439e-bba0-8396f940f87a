// Data label formatter for column stacks
const dashboardBreedingGenericChartPlotOptions = {
  spline: {
    dashStyle: "Solid",
  },
  series: {
    dashStyle: "ShortDot",
    lineWidth: 2,
    shadow: true,
    marker: {
      enabled: false,
      symbol: "circle",
      states: {
        hover: {
          enabled: true,
        },
      },
    },
  },
};

// Tooltip formatter to show hen day
var dashboardBreedingGenericChartToolTipFormatter = function () {
  // Header with title and week number
  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

  // Body with percentage
  var body = `<span style="color: ${this.color}">\u25CF</span>${this.series.name}: <b>${this.y} ${this.point.series.userOptions.toolTip.valueSuffix}</b><br/>`;

  // Append body to header and return
  return header + body;
};
