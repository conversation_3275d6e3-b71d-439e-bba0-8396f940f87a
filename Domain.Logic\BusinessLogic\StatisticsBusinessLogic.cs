﻿using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.Extensions;
using Binit.Framework.Helpers;
using Binit.Framework.Helpers.Excel;
using Binit.Framework.Interfaces.DAL;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.StatisticsBusinessLogic;

namespace Domain.Logic.BusinessLogic
{
    public class StatisticsBusinessLogic : IStatisticsBusinessLogic
    {
        private readonly IHenReportService henReportService;
        private readonly IHenBatchService henBatchService;
        private readonly ICapacityUnitService capacityUnitService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IHappeningService happeningService;
        private readonly IFormulaService formulaService;
        private readonly IFeedProductionReportService feedProductionReportService;
        private readonly IContainerService<Container> containerService;
        private readonly IClassificationReportService classificationReportService;
        private readonly IStorageWarehouseService storageWarehouseService;
        private readonly IClassificationWarehouseService classificationWarehouseService;
        private readonly IPackingReportService packingReportService;
        private readonly IOrderPreparationReportService orderPreparationReportService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly ITaskEntityService taskEntityService;
        private readonly ICommonChartsBusinessLogic commonChartsBusinessLogic;
        private readonly IHenStageChartsBusinessLogic henStageChartsBusinessLogic;
        private readonly ILayingChartsBusinessLogic layingChartsBusinessLogic;
        private readonly IStockChartsBusinessLogic stockChartsBusinessLogic;
        private readonly IFeedFactoryChartsBusinessLogic feedFactoryChartsBusinessLogic;
        private readonly IClassificationChartsBusinessLogic classificationChartsBusinessLogic;
        private readonly IPackingChartsBusinessLogic packingChartsBusinessLogic;
        private readonly IUserService<ApplicationUser> userService;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IOperationContext operationContext;
        private readonly IMaterialReceptionReportService materialReceptionReportService;
        private readonly ITrackingChartsBusinessLogic trackingChartsBusinessLogic;
        private readonly IReturnAndDispositionChartsBusinessLogic returnAndDispositionChartsBusinessLogic;
        private readonly ILineService lineService;
        private readonly IEggsTrackingChartsBusinessLogic eggsTrackingChartsBusinessLogic;
        private readonly IHenBatchChartsBusinessLogic henBatchChartsBusinessLogic;
        private readonly IHenWarehouseService henWarehouseService;
        private readonly IShippingNoteBusinessLogic shippingNoteBusinessLogic;
        private readonly IConfiguration configuration;
        private readonly IMaterialService materialService;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly IEggQualityReportService eggQualityReportService;
        private readonly ISerologyBusinessLogic serologyBusinessLogic;

        public StatisticsBusinessLogic(
            IHenReportService henReportService,
            IHenBatchService henBatchService,
            ICapacityUnitService capacityUnitService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IHappeningService happeningService,
            IFormulaService formulaService,
            IFeedProductionReportService feedProductionReportService,
            IContainerService<Container> containerService,
            IClassificationReportService classificationReportService,
            IStorageWarehouseService storageWarehouseService,
            IClassificationWarehouseService classificationWarehouseService,
            IPackingReportService packingReportService,
            IOrderPreparationReportService orderPreparationReportService,
            IStringLocalizer<SharedResources> localizer,
            ITaskEntityService taskEntityService,
            ICommonChartsBusinessLogic commonChartsBusinessLogic,
            IHenStageChartsBusinessLogic henStageChartsBusinessLogic,
            ILayingChartsBusinessLogic layingChartsBusinessLogic,
            IStockChartsBusinessLogic stockChartsBusinessLogic,
            IFeedFactoryChartsBusinessLogic feedFactoryChartsBusinessLogic,
            IClassificationChartsBusinessLogic classificationChartsBusinessLogic,
            IPackingChartsBusinessLogic packingChartsBusinessLogic,
            IUserService<ApplicationUser> userService,
            IService<TenantConfiguration> tenantConfigurationService,
            IOperationContext operationContext,
            IMaterialReceptionReportService materialReceptionReportService,
            ITrackingChartsBusinessLogic trackingChartsBusinessLogic,
            IReturnAndDispositionChartsBusinessLogic returnAndDispositionChartsBusinessLogic,
            ILineService lineService,
            IEggsTrackingChartsBusinessLogic eggsTrackingChartsBusinessLogic,
            IHenBatchChartsBusinessLogic henBatchChartsBusinessLogic,
            IHenWarehouseService henWarehouseService,
            IShippingNoteBusinessLogic shippingNoteBusinessLogic,
            IConfiguration configuration,
            IMaterialService materialService,
            IGeneticsParameterService geneticsParameterService,
            IEggQualityReportService eggQualityReportService,
            ISerologyBusinessLogic serologyBusinessLogic)
        {
            this.henReportService = henReportService;
            this.henBatchService = henBatchService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.happeningService = happeningService;
            this.formulaService = formulaService;
            this.feedProductionReportService = feedProductionReportService;
            this.containerService = containerService;
            this.classificationReportService = classificationReportService;
            this.capacityUnitService = capacityUnitService;
            this.storageWarehouseService = storageWarehouseService;
            this.classificationWarehouseService = classificationWarehouseService;
            this.packingReportService = packingReportService;
            this.orderPreparationReportService = orderPreparationReportService;
            this.localizer = localizer;
            this.taskEntityService = taskEntityService;
            this.commonChartsBusinessLogic = commonChartsBusinessLogic;
            this.henStageChartsBusinessLogic = henStageChartsBusinessLogic;
            this.stockChartsBusinessLogic = stockChartsBusinessLogic;
            this.layingChartsBusinessLogic = layingChartsBusinessLogic;
            this.feedFactoryChartsBusinessLogic = feedFactoryChartsBusinessLogic;
            this.classificationChartsBusinessLogic = classificationChartsBusinessLogic;
            this.packingChartsBusinessLogic = packingChartsBusinessLogic;
            this.userService = userService;
            this.tenantConfigurationService = tenantConfigurationService;
            this.operationContext = operationContext;
            this.materialReceptionReportService = materialReceptionReportService;
            this.trackingChartsBusinessLogic = trackingChartsBusinessLogic;
            this.returnAndDispositionChartsBusinessLogic = returnAndDispositionChartsBusinessLogic;
            this.lineService = lineService;
            this.eggsTrackingChartsBusinessLogic = eggsTrackingChartsBusinessLogic;
            this.henBatchChartsBusinessLogic = henBatchChartsBusinessLogic;
            this.henWarehouseService = henWarehouseService;
            this.shippingNoteBusinessLogic = shippingNoteBusinessLogic;
            this.configuration = configuration;
            this.materialService = materialService;
            this.geneticsParameterService = geneticsParameterService;
            this.eggQualityReportService = eggQualityReportService;
            this.serologyBusinessLogic = serologyBusinessLogic;
        }

        #region Cards
        #region Manager

        /// <summary>
        /// Build dashboardInformativeCardsDTO
        /// </summary>
        public DashboardInformativeCardsDTO GetCardsHenBatchData(DashboardInformativeCardsDTO card, IQueryable<HenBatchPerformance> henBatchPerformance, IQueryable<GeneticsParametersReference> geneticsParameter, HenStage henStage)
        {
            //Get HenBatches and HenReport for Female and Male Informative Cards
            IQueryable<HenBatch> henBatches = henBatchService.GetAll()
                .Where(hb => card.HenBatchIds.Contains(hb.Id) || card.HenBatchIds.Contains(hb.ParentId.Value));
            var performanceAccumulated = henBatchPerformance;
            // it is avoided to bring HenBatchPerformance that is not related to at least one HenReport
            henBatchPerformance = henBatchPerformance.Where(hbp => hbp.LastHenReportDate.HasValue && hbp.LastHenReportDate != default(DateTime));
            if (!henBatchPerformance.Any())
            {
                henBatchPerformance = henBatchPerformanceService
                .GetAll()
                .Where(hbp => hbp.LastHenReportDate.HasValue && hbp.LastHenReportDate != default(DateTime))
                .Where(hbp => card.HenBatchIds.Contains(hbp.HenBatchId));
            }

            List<HenBatchPerformance> lastHBP = new List<HenBatchPerformance>();

            if (henBatchPerformance.Any()) // Verifica si hay elementos en la lista
            {
                // Obtiene los valores distintos de WeekNumber con DistinctDayCount > 6
                var distinctWeekNumbers = henBatchPerformance
                                            .Where(hbp => hbp.DistinctDayCount > 6)
                                            .Select(hbp => hbp.WeekNumber)
                                            .Distinct();

                // Encuentra el número de semana más alto entre los valores distintos
                var maxWeekNumber = distinctWeekNumbers.Any() ? distinctWeekNumbers.Max() : 0;

                // Filtra solo los registros que pertenecen a ese número de semana
                lastHBP = henBatchPerformance
                                .Where(hbp => hbp.WeekNumber == maxWeekNumber && hbp.ParentId == null)
                                .ToList();
            }
            List<HenReport> allHenReports = new List<HenReport>();
            List<HenReport> henReports = new List<HenReport>();
            int henBatchesCount = 0;
            int deadAndDepopulatedFemaleAccumSinceFirstProduction = 0;
            int hatchedEggsAccum = 0;
            List<HenBatch> henBatchList = henBatches
                .Where(hb => card.HenBatchIds.Contains(hb.Id))
                .ToList();
            int initialFemales = 0;
            DateTime? firstProductionDate = null;
            if (henBatchList != null)
            {
                // Calcular la suma de InitialHenAmountFemale
                initialFemales = henBatchList.Sum(hb => hb.InitialHenAmountFemale);
                // Obtener la fecha más antigua de CapitalizationDate
                firstProductionDate = henBatchList.Min(hb => hb.CapitalizationDate);
            }

            if (lastHBP != null && lastHBP.All(hbp => hbp.LastHenReportDate != null))
            {
                List<Guid> henBatchesIds = henBatches.Select(hb => hb.Id)
                                                .ToList();

                IQueryable<HenReport> henReportsQuery = henReportService.GetAll().Where(hr => henBatchesIds.Any(hb => hb == hr.HenBatchId) && hr.RectificationHenReportId == null);

                // only for gad
                if (henReportsQuery.Any())
                {
                    var lastHBPList = lastHBP.Where(hbp => hbp.LastHenReportDate.HasValue).ToList(); // Traer `lastHBP` a memoria

                    henReports = henReportsQuery
                        .AsEnumerable() // Convierte la consulta a memoria (evita problemas de traducción de LINQ a SQL)
                        .Where(hr => lastHBPList.Any(hbp => hr.Date.Date == hbp.LastHenReportDate.Value.Date))
                        .ToList();
                }
                else
                {
                    // Maneja el caso cuando henReportsQuery está vacío
                    henReports = new List<HenReport>(); // O cualquier valor por defecto que desees
                }

                // other card use
                allHenReports = henReportsQuery.Where(hr => hr.Date.Date >= (firstProductionDate.HasValue ? firstProductionDate.Value.Date : firstProductionDate)).ToList();

                henBatchesCount = henBatches.Where(hb => !card.HenBatchIds.Contains(hb.Id)).Count();

                deadAndDepopulatedFemaleAccumSinceFirstProduction = allHenReports.Select(hr => hr.DeadFemale).Sum() + allHenReports.Select(hr => hr.DepopulateFemale).Sum();
                hatchedEggsAccum = allHenReports.Select(hr => Convert.ToInt32(hr.HatchableEggs)).Sum();
            }

            //Get Cards
            var latestWeekHBP = GetLatestWeekHenBatchPerformances(henBatchPerformance);
            if (henStage == HenStage.Laying)
            {
                GetEggsStock(card, henBatchList);
                GetHenBatchMortality(card, latestWeekHBP, geneticsParameter);
                GetActualWeek(card, henBatchPerformance, geneticsParameter);
                GetOIFC(card, henBatchPerformance, geneticsParameter, hatchedEggsAccum);
                GetHenBatchViability(card, latestWeekHBP, geneticsParameter);
                GetAccumulatedHatching(card, performanceAccumulated, geneticsParameter);
            }
            else
            {
                GetHenBatchViability(card, latestWeekHBP, geneticsParameter);
                GetHenBatchMortality(card, latestWeekHBP, geneticsParameter);
                GetActualWeek(card, henBatchPerformance, geneticsParameter);
            }
            GetFemaleInformativeCard(card, henBatchPerformance, geneticsParameter, henBatchList, henReports, henBatchesCount);
            GetMaleInformativeCard(card, henBatchPerformance, geneticsParameter, henBatchList, henReports, henBatchesCount);
            if (henStage == HenStage.Laying)
                GetMQOInformativeCard(card, henBatchList);
            GetFarmStructure(card, henBatchList);
            return card;
        }

        /// <summary>
        /// Generate a list of cards for Seara breeding dashboard
        /// </summary>
        public DashboardInformativeCardsDTO GetSearaBreedingCardsData(List<Guid> henBatchIds, DateTime from, DateTime to, int startWeek, int endWeek)
        {
            IQueryable<HenBatchPerformance> henBatchPerformances = henBatchPerformanceService
                .GetAll()
                .Where(hbp => hbp.LastHenReportDate.HasValue && hbp.LastHenReportDate != default(DateTime)) // ensure that HenBatchPerformances are not related to at least one HenReport
                .Where(hbp => henBatchIds.Contains(hbp.HenBatchId) || henBatchIds.Contains(hbp.HenBatch.ParentId.Value))
                .Where(hbp => hbp.Date >= from && hbp.Date <= to);

            IQueryable<GeneticsParametersReference> geneticsParameter = geneticsParameterService.GetAll()
                .Include(g => g.Genetics)
                .Where(g => g.Genetics.HenBatch.Any(hb => henBatchIds.Contains(hb.Id)));

            // Get Cards
            DashboardInformativeCardsDTO card = new DashboardInformativeCardsDTO();

            var latestWeekHBP = GetLatestWeekHenBatchPerformances(henBatchPerformances);
            GetHenBatchViability(card, latestWeekHBP, geneticsParameter);
            GetHenBatchMortality(card, latestWeekHBP, geneticsParameter);
            GetCurrentWeekAverage(card, henBatchIds);
            GetMaleToFemalePercentage(card, latestWeekHBP);
            return card;
        }

        /// <summary>
        /// Generate a list of cards for Seara breeding dashboard
        /// </summary>
        public DashboardInformativeCardsDTO GetSearaLayingCardsData(List<Guid> henBatchIds, DateTime from, DateTime to, int startWeek, int endWeek)
        {
            IQueryable<HenBatchPerformance> henBatchPerformances = henBatchPerformanceService
                .GetAll()
                .Where(hbp => hbp.LastHenReportDate.HasValue && hbp.LastHenReportDate != default(DateTime)) // ensure that HenBatchPerformances are not related to at least one HenReport
                .Where(hbp => henBatchIds.Contains(hbp.HenBatchId) || henBatchIds.Contains(hbp.HenBatch.ParentId.Value));

            if (endWeek != 0)
            {
                henBatchPerformances = henBatchPerformances.Where(hbp => hbp.WeekNumber >= startWeek && hbp.WeekNumber <= endWeek);
            }
            else
            {
                henBatchPerformances = henBatchPerformances.Where(hbp => hbp.Date >= from && hbp.Date <= to);
            }

            IQueryable<GeneticsParametersReference> geneticsParameter = geneticsParameterService.GetAll()
                .Include(g => g.Genetics)
                .Where(g => g.Genetics.HenBatch.Any(hb => henBatchIds.Contains(hb.Id)));

            // Get henBatchIds associated with the filtered henBatchPerformances
            var validHenBatchIds = henBatchPerformances.Select(hbp => hbp.HenBatchId).Distinct().ToList();

            // Get Cards
            DashboardInformativeCardsDTO card = new DashboardInformativeCardsDTO();

            var latestWeekHBP = GetLatestWeekHenBatchPerformances(henBatchPerformances);
            GetHenBatchViability(card, latestWeekHBP, geneticsParameter);
            GetHenBatchMortality(card, latestWeekHBP, geneticsParameter);
            GetCurrentWeekAverage(card, validHenBatchIds);
            GetMaleToFemalePercentage(card, latestWeekHBP);
            return card;
        }

        private List<HenBatchPerformance> GetLatestWeekHenBatchPerformances(IQueryable<HenBatchPerformance> henBatchPerformance)
        {
            var latestHBP = new List<HenBatchPerformance>();
            if (henBatchPerformance != null && henBatchPerformance.Any(hbp => !hbp.ParentId.HasValue && hbp.DistinctDayCount > 6))
            {
                int maxWeek = henBatchPerformance
                    .Where(hbp => !hbp.ParentId.HasValue && hbp.DistinctDayCount > 6)
                    .Max(hbp => hbp.WeekNumber); // get latest week number

                latestHBP = henBatchPerformance
                    .Where(hbp => !hbp.ParentId.HasValue && hbp.DistinctDayCount > 6 && hbp.WeekNumber == maxWeek)
                    .ToList(); // get all records of the latest week
            }
            return latestHBP;
        }

        /// <summary>
        /// Female Viability
        /// </summary>
        private void GetHenBatchViability(DashboardInformativeCardsDTO card, List<HenBatchPerformance> latestWeekHBP, IQueryable<GeneticsParametersReference> geneticsParameter)
        {
            SimpleDashboardInformativeCardDTO simpleCard = default;
            if (latestWeekHBP != null && latestWeekHBP.Any())
            {
                decimal viability = Math.Round(latestWeekHBP.Average(hbp => hbp.HenBatchLifeTimeFemaleViability), 2);

                string value = viability.ToString();
                int week = latestWeekHBP.FirstOrDefault().WeekNumber;
                string weekNumber = week.ToString();
                decimal viableStandard = Math.Round(
                    geneticsParameter
                        .Where(gp => gp.TimePeriodValue == week)
                        .Select(gp => gp.ViableFemalesPercentage)
                        .ToList() // Se ejecuta en memoria, evitando problemas de traducción en EF Core
                        .DefaultIfEmpty(0) // Si la lista está vacía, agrega un 0
                        .Average(),
                    2);

                string subtitle = weekNumber != "-" ? decimal.Round(viableStandard, 2).ToString() + " %" : "-";

                simpleCard = new SimpleDashboardInformativeCardDTO()
                {
                    Title = this.localizer[Lang.HenBatchViabilityCardTitle],
                    Value = weekNumber != "-" ? value + " %" : "",
                    NumberValue = weekNumber != "-" ? viability : 0,
                    SubtitleBold = this.localizer[Lang.HenBatchViabilityCardSubtitle] + subtitle,
                    SubtitleSimple = weekNumber == "-" ? this.localizer[Lang.HenBatchViabilityCardSubtitleValidation] : this.localizer[Lang.HenBatchViabilityCardSubtitleSimple] + weekNumber,
                    ValueColor = viability >= viableStandard ? "LightGreen" : "red"
                };
            }
            else
                simpleCard = new SimpleDashboardInformativeCardDTO()
                {
                    Title = this.localizer[Lang.HenBatchViabilityCardTitle],
                    SubtitleBold = this.localizer[Lang.HenBatchViabilityCardSubtitle],
                    SubtitleSimple = this.localizer[Lang.HenBatchViabilityCardSubtitleValidation]
                };

            card.SimpleCardDTOs.Add(simpleCard);
        }

        /// <summary>
        /// Male to Female Percentage
        /// </summary>
        private void GetMaleToFemalePercentage(DashboardInformativeCardsDTO card, List<HenBatchPerformance> latestWeekHBP)
        {
            decimal totalMales = latestWeekHBP.Any() ? latestWeekHBP.LastOrDefault().HenAmountMale : 0;
            decimal totalFemales = latestWeekHBP.Any() ? latestWeekHBP.LastOrDefault().HenAmountFemale : 0;

            decimal percentage = totalFemales > 0
                ? Math.Round(100 * totalMales / totalFemales, 2)
                : 0;

            SimpleDashboardInformativeCardDTO simpleCard = new SimpleDashboardInformativeCardDTO()
            {
                Title = "% M/F",
                Value = percentage.ToString() + " %",
                NumberValue = percentage
            };

            card.SimpleCardDTOs.Add(simpleCard);
        }

        /// <summary>
        /// Female Mortality
        /// </summary>
        private void GetHenBatchMortality(DashboardInformativeCardsDTO card, List<HenBatchPerformance> latestWeekHBP, IQueryable<GeneticsParametersReference> geneticsParameter)
        {
            decimal totalDead = latestWeekHBP.Sum(hbp => hbp.DeadFemale + hbp.DepopulateFemale);
            decimal totalInitial = latestWeekHBP.Sum(hbp => hbp.WeekInitialHenAmountFemale);

            decimal mortality = totalInitial > 0
                ? Math.Round(100 * (decimal)totalDead / totalInitial, 2)
                : 0;

            string mortalityValue = decimal.Round(mortality, 2).ToString() + "%";

            int week = latestWeekHBP.FirstOrDefault()?.WeekNumber ?? 0;
            string weekValue = latestWeekHBP != null ? week.ToString() : "-";

            decimal standardMortality = Math.Round(
                    geneticsParameter
                        .Where(gp => gp.TimePeriodValue == week)
                        .Select(gp => gp.GeneralMortalityFemaleWeek)
                        .ToList() // Se ejecuta en memoria, evitando problemas de traducción en EF Core
                        .DefaultIfEmpty(0) // Evita errores si no hay elementos
                        .Average(),
                    2);

            SimpleDashboardInformativeCardDTO simpleCard = new SimpleDashboardInformativeCardDTO()
            {
                Title = this.localizer[Lang.HenBatchMortalityCardTitle],
                Value = weekValue != "-" ? mortalityValue : "",
                NumberValue = weekValue != "-" ? decimal.Round(mortality, 2) : 0,
                SubtitleSimple = weekValue != "-" ? this.localizer[Lang.HenBatchMortalityCardSubtitle] + weekValue : this.localizer[Lang.HenBatchMortalityCardSubtitleValidation],
                ValueColor = mortality <= standardMortality ? "LightGreen" : "red",
                IconColor = weekValue != "-" && mortality * 1.2m > standardMortality ? "orange" : "",
                Icon = weekValue != "-" && mortality > standardMortality * 1.2m ? "mdi mdi-alert-circle-outline fa-4x" : "",
            };

            card.SimpleCardDTOs.Add(simpleCard);

        }

        /// <summary>
        /// Female Informative Card
        /// </summary>
        public void GetFemaleInformativeCard(DashboardInformativeCardsDTO card, IQueryable<HenBatchPerformance> henBatchPerformance,
            IQueryable<GeneticsParametersReference> geneticsParameter, List<HenBatch> henBatches, List<HenReport> henReports, int henBatchesCount)
        {
            InformativeCardDTO informativeCard = new InformativeCardDTO()
            {
                Title = this.localizer[Lang.FemaleInformativeCardTitle],
            };

            //Stock
            decimal henBatchValue = henBatches.Sum(hb => hb.HenAmountFemale);
            informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
            {
                Text = this.localizer[Lang.FemaleMaleStockCardText],
                IsSubtitle = false,
                Value = henBatchValue.ToString(),
                NumberValue = henBatchValue
            });

            // Paso 1: Obtener la semana más alta directamente desde la base de datos
            var maxWeekNumber = henBatchPerformance
                .Where(hbp => hbp.AvgVariationCoefficientFemale != 0 && hbp.AvgUniformityFemale != 0 && hbp.ParentId == null)
                .Max(hbp => (int?)hbp.WeekNumber) ?? 0; // Usamos (int?) para manejar el caso en que no haya registros

            // Paso 2: Filtrar los registros de la semana más alta
            var result = henBatchPerformance
                .Where(hbp => hbp.WeekNumber == maxWeekNumber &&
                              hbp.AvgVariationCoefficientFemale != 0 &&
                              hbp.AvgUniformityFemale != 0 &&
                              hbp.ParentId == null)
                .Select(hbp => new
                {
                    WeekNumber = hbp.WeekNumber,
                    AvgUniformityFemale = hbp.AvgUniformityFemale,
                    AvgVariationCoefficientFemale = hbp.AvgVariationCoefficientFemale
                })
                .ToList(); // Traer los datos a memoria

            //Weight
            var henStage = henBatches.FirstOrDefault()?.HenStage;
            if (henStage == HenStage.Breeding)
            {
                //Week
                string weekNumber = result.LastOrDefault()?.WeekNumber.ToString() ?? "-";
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    IsSubtitle = true,
                    Text = this.localizer[Lang.FemaleMaleWeekCardText] + weekNumber
                });

                //Uniformity
                decimal uniformityStandard = Math.Round(geneticsParameter
                    .Where(gp => result.Select(hbp => hbp.WeekNumber).Contains(gp.TimePeriodValue))
                    .Average(gp => (decimal?)gp.Uniformity) ?? 0,
                    2);

                decimal uniformityFemale = result.Any()
                    ? result.Average(hbp => hbp.AvgUniformityFemale)
                    : 0M; // Valor por defecto si la lista está vacía
                string uniformityFemaleValue = uniformityFemale.ToString("F2") + "%";
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = this.localizer[Lang.FemaleMaleUniformityCardText],
                    IsSubtitle = false,
                    Value = uniformityFemaleValue,
                    NumberValue = uniformityFemale,
                    ValueColor = uniformityFemale >= uniformityStandard ? "LightGreen" : "red"
                });

                //Variance Coefficient
                decimal vcStandard = Math.Round(geneticsParameter
                    .Where(gp => result.Select(hbp => hbp.WeekNumber).Contains(gp.TimePeriodValue))
                    .Average(gp => (decimal?)gp.VarianceCoefficient) ?? 0,
                    2);

                decimal vcFemale = result.Any()
                    ? result.Average(hbp => hbp.AvgVariationCoefficientFemale)
                    : 0M; // Devuelve 0 si la lista está vacía
                string vcFemaleValue = vcFemale.ToString("F2") + "%";
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = this.localizer[Lang.FemaleMaleVarianceCoefficientCardText],
                    IsSubtitle = false,
                    Value = vcFemaleValue,
                    NumberValue = vcFemale,
                    ValueColor = vcFemale <= vcStandard ? "LightGreen" : "red"
                });
            }

            //FeedIntake
            var lastHBP = henBatchPerformance.Any() // Verifica si hay elementos en la colección
                ? henBatchPerformance
                    .Where(hbp => !hbp.ParentId.HasValue && hbp.DistinctDayCount > 6)
                    .OrderByDescending(hbp => hbp.WeekNumber) // Ordena por WeekNumber de forma descendente
                    .AsEnumerable() // Trae los datos a memoria
                    .GroupBy(hbp => hbp.WeekNumber) // Agrupa por WeekNumber en memoria
                    .OrderByDescending(g => g.Key) // Ordena los grupos por WeekNumber de forma descendente
                    .FirstOrDefault()? // Selecciona el grupo con la semana más alta
                    .ToList() // Convierte el grupo en una lista
                : new List<HenBatchPerformance>(); // Si la colección está vacía, devuelve una lista vacía

            if (lastHBP != null && lastHBP.Any())
            {
                // LastHenReportDate
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = lastHBP.FirstOrDefault().LastHenReportDate.Value.ToString("dd/MM/yyyy"),
                    IsSubtitle = true
                });

                //TotalFeedIntake
                decimal totalFeedIntake = henReports.Sum(hr => hr.FeedIntakeFemale);
                string toolTip = henReports.GroupBy(hr => hr.HenBatchId).Count() < henBatchesCount ? "(*)" : "";
                string valueTooltipText = toolTip == "(*)" ? this.localizer[Lang.FemaleMaleTootlTipText] : "";
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = this.localizer[Lang.FemaleMaleTotalFeedIntakeCardText],
                    IsSubtitle = false,
                    Value = totalFeedIntake.ToString("N0", new CultureInfo("is-IS")) + toolTip,
                    NumberValue = totalFeedIntake,
                    ValueTooltipText = valueTooltipText
                });

                //GAD
                var totalHenAmount = henReports.Sum(hr => hr.HenAmountFemale + hr.DeadFemale + hr.DepopulateFemale);

                int GAD = totalHenAmount != 0 ? Convert.ToInt32(1000 * totalFeedIntake / totalHenAmount) : 0;
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = this.localizer[Lang.FemaleMaleGADCardText],
                    IsSubtitle = false,
                    Value = GAD.ToString(),
                    NumberValue = GAD
                });
            }


            card.ListCardDTOs.Add(informativeCard);
        }


        /// <summary>
        /// Male Informative Card
        /// </summary>
        public void GetMaleInformativeCard(DashboardInformativeCardsDTO card, IQueryable<HenBatchPerformance> henBatchPerformance,
    IQueryable<GeneticsParametersReference> geneticsParameter, List<HenBatch> henBatches, List<HenReport> henReports, int henBatchesCount)
        {
            InformativeCardDTO informativeCard = new InformativeCardDTO()
            {
                Title = this.localizer[Lang.MaleInformativeCardTitle],
            };

            // Sumar HenAmountMale de todos los lotes
            int totalHenAmountMale = henBatches.Sum(hb => hb.HenAmountMale);
            string henBatchValue = totalHenAmountMale > 0 ? totalHenAmountMale.ToString() : "-";

            // Stock
            informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
            {
                Text = this.localizer[Lang.FemaleMaleStockCardText],
                IsSubtitle = false,
                Value = henBatchValue,
                NumberValue = totalHenAmountMale
            });

            // Agrupar henBatchPerformance por WeekNumber y obtener el promedio
            // Paso 1: Obtener la semana más alta
            var maxWeekNumber = henBatchPerformance
                .Where(hbp => hbp.AvgVariationCoefficientMale != 0 && hbp.AvgUniformityMale != 0)
                .Max(hbp => (int?)hbp.WeekNumber) ?? 0; // Usamos (int?) para manejar el caso en que no haya registros

            // Paso 2: Filtrar todos los registros de la semana más alta
            var henBPGrouped = henBatchPerformance
                .Where(hbp => hbp.WeekNumber == maxWeekNumber &&
                              hbp.AvgVariationCoefficientMale != 0 &&
                              hbp.AvgUniformityMale != 0 && hbp.ParentId == null)
                .Select(hbp => new
                {
                    WeekNumber = hbp.WeekNumber,
                    AvgUniformityMale = hbp.AvgUniformityMale,
                    AvgVariationCoefficientMale = hbp.AvgVariationCoefficientMale
                })
                .ToList(); // Convertir a lista

            var highestWeek = henBPGrouped.FirstOrDefault(); // La semana más alta

            // Verificar HenStage de un lote (primer lote encontrado)
            HenBatch firstBatch = henBatches.FirstOrDefault();
            if (firstBatch != null && firstBatch.HenStage == HenStage.Breeding && highestWeek != null)
            {
                // Semana más alta
                string weekNumber = highestWeek.WeekNumber.ToString();
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    IsSubtitle = true,
                    Text = this.localizer[Lang.FemaleMaleWeekCardText] + weekNumber
                });

                // Uniformity
                // Paso 1: Obtener los valores de Uniformity desde la base de datos
                var uniformityValues = geneticsParameter
                    .Where(gp => gp.TimePeriodValue == highestWeek.WeekNumber)
                    .Select(gp => gp.Uniformity)
                    .ToList(); // Traer los datos a memoria

                // Paso 2: Calcular el promedio en memoria
                decimal uniformityStandard = Math.Round(uniformityValues.Any()
                    ? uniformityValues.Average()
                    : 0,
                    2); // Si no hay valores, devolver 0

                string uniformityMaleValue = highestWeek.AvgUniformityMale.ToString("F2") + "%";
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = this.localizer[Lang.FemaleMaleUniformityCardText],
                    IsSubtitle = false,
                    Value = uniformityMaleValue,
                    NumberValue = highestWeek.AvgUniformityMale,
                    ValueColor = highestWeek.AvgUniformityMale >= uniformityStandard ? "LightGreen" : "red"
                });

                // Variance Coefficient
                decimal vcStandard = Math.Round(geneticsParameter
                    .Where(gp => gp.TimePeriodValue == highestWeek.WeekNumber)
                    .AsEnumerable() // Convierte la consulta a ejecución en memoria
                    .Select(gp => gp.VarianceCoefficient)
                    .DefaultIfEmpty(0)
                    .Average(),
                    2);

                string vcMaleValue = highestWeek.AvgVariationCoefficientMale.ToString("F2") + "%";
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = this.localizer[Lang.FemaleMaleVarianceCoefficientCardText],
                    IsSubtitle = false,
                    Value = vcMaleValue,
                    NumberValue = highestWeek.AvgVariationCoefficientMale,
                    ValueColor = highestWeek.AvgVariationCoefficientMale <= vcStandard ? "LightGreen" : "red"
                });
            }

            //FeedIntake
            var lastHBP = henBatchPerformance.Any() // Verifica si hay elementos en la colección
                ? henBatchPerformance
                    .Where(hbp => !hbp.ParentId.HasValue && hbp.DistinctDayCount > 6) // Filtra los elementos con LastHenReportDate no nulo
                    .OrderByDescending(hbp => hbp.WeekNumber) // Ordena por WeekNumber de forma descendente
                    .AsEnumerable() // Trae los datos a memoria
                    .GroupBy(hbp => hbp.WeekNumber) // Agrupa por WeekNumber en memoria
                    .OrderByDescending(g => g.Key) // Ordena los grupos por WeekNumber de forma descendente
                    .FirstOrDefault()? // Selecciona el grupo con la semana más alta
                    .ToList() // Convierte el grupo en una lista
                : new List<HenBatchPerformance>(); // Si la colección está vacía, devuelve una lista vacía

            if (lastHBP != null && lastHBP.Any())
            {
                // LastHenReportDate
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = lastHBP.FirstOrDefault().LastHenReportDate.Value.ToString("dd/MM/yyyy"),
                    IsSubtitle = true
                });

                //TotalFeedIntake
                decimal totalFeedIntake = henReports.Sum(hr => hr.FeedIntakeMale);
                string toolTip = henReports.GroupBy(hr => hr.HenBatchId).Count() < henBatchesCount ? "(*)" : "";
                string valueTooltipText = toolTip == "(*)" ? this.localizer[Lang.FemaleMaleTootlTipText] : "";
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = this.localizer[Lang.FemaleMaleTotalFeedIntakeCardText],
                    IsSubtitle = false,
                    Value = totalFeedIntake.ToString("N0", new CultureInfo("is-IS")) + toolTip,
                    NumberValue = totalFeedIntake,
                    ValueTooltipText = valueTooltipText
                });

                //GAD
                var totalHenAmount = henReports.Sum(hr => hr.HenAmountMale + hr.DeadMale + hr.DepopulateMale);

                int GAD = totalHenAmount != 0 ? Convert.ToInt32(1000 * totalFeedIntake / totalHenAmount) : 0;
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = this.localizer[Lang.FemaleMaleGADCardText],
                    IsSubtitle = false,
                    Value = GAD.ToString(),
                    NumberValue = GAD
                });
            }

            card.ListCardDTOs.Add(informativeCard);
        }

        /// <summary>
        /// MQO Informative Card
        /// </summary>
        public void GetMQOInformativeCard(DashboardInformativeCardsDTO card, List<HenBatch> henBatch)
        {
            InformativeCardDTO informativeCard = new InformativeCardDTO()
            {
                Title = this.localizer[Lang.MQOInformativeCardTitle],
            };

            IOrderedQueryable<EggQualityReport> mqoQuery = eggQualityReportService.GetAll()
                .Where(eqr => henBatch.Select(hb => hb.Id).Contains(eqr.HenBatchId))  // Filtra los EggQualityReports por los HenBatchId de la lista
                .OrderByDescending(eqr => eqr.ProductionDate);
            if (mqoQuery.Any())
            {
                DateTime mqoLastDate = mqoQuery.Select(eqr => eqr.ProductionDate).FirstOrDefault();
                if (mqoLastDate != DateTime.MinValue)
                {
                    DateTime weekStart = mqoLastDate.GetPastSelectedDay(DayOfWeek.Sunday);
                    List<EggQualityReport> mqo = mqoQuery.Where(eqr => eqr.ProductionDate > weekStart && eqr.ProductionDate <= mqoLastDate)
                                                         .ToList();
                    IEnumerable<IGrouping<Guid, EggQualityReport>> mqoGroupped = mqo.GroupBy(eqr => eqr.EggTypeId);
                    decimal eggPercentage = 0;
                    IQueryable<Material> materials = materialService.GetAll()
                                                        .Include(m => m.Standards);
                    List<(string, string)> materialList = new List<(string, string)>();
                    foreach (IGrouping<Guid, EggQualityReport> mqoMaterial in mqoGroupped)
                    {
                        eggPercentage = mqoMaterial.Sum(eqr => eqr.Samples) > 0
                            ? Math.Round(100 * ((decimal)mqoMaterial.Sum(eqr => eqr.InconsistentQuantity) / mqoMaterial.Sum(eqr => eqr.Samples)), 2)
                            : 0;
                        Material material = materials.Where(m => m.Id == mqoMaterial.Key).FirstOrDefault();
                        decimal standard = material.Standards.Any() ? material.Standards.FirstOrDefault().OptimalIntervalValueMax : 0;
                        string materialName = material.Name;
                        if (eggPercentage > standard)
                            materialList.Add((materialName, eggPercentage.ToString(DashboardDecimalPrecision.TwoDecimal)));
                    }
                    Func<HenBatch, DateTime, int> getBatchWeekNumber = (henBatch, mqoLastDate) => henBatch.BatchWeekNumber + (mqoLastDate - henBatch.DateStart.Value.GetPastSelectedDay(henBatch.Farm.DayOfWeek)).Days / 7;

                    foreach (var batch in henBatch)
                    {
                        string week = batch != null ? getBatchWeekNumber(batch, mqoLastDate).ToString() : "-";
                        informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                        {
                            Text = this.localizer[Lang.WeekNumberMQOCardText] + week,
                            IsSubtitle = true,
                        });
                    }
                    if (materialList.Count != 0)
                    {
                        informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                        {
                            Text = this.localizer[Lang.AboveStandardMQOCardText],
                            IsSubtitle = true,
                        });
                        foreach ((string, string) mat in materialList)
                        {
                            informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                            {
                                Text = mat.Item1,
                                IsSubtitle = false,
                                Value = mat.Item2 + "%",
                                NumberValue = decimal.Parse(mat.Item2),
                                ValueColor = "red"
                            });
                        }
                    }
                    else
                    {
                        informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                        {
                            Text = this.localizer[Lang.BelowStandardMQOCardText],
                            TextColor = "LightGreen",
                            IsSubtitle = true,
                        });
                    }
                }
                else
                {
                    informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                    {
                        Text = this.localizer[Lang.NoReportsMQOCardText],
                        IsSubtitle = true,
                    });
                }

            }
            else
            {
                informativeCard.InformativeCards.Add(new InformativeCardListItemDTO()
                {
                    Text = this.localizer[Lang.NoReportsMQOCardText],
                    IsSubtitle = true,
                });
            }
            card.ListCardDTOs.Add(informativeCard);
        }

        /// <summary>
        /// Egg Stock Card
        /// </summary>
        public void GetEggsStock(DashboardInformativeCardsDTO card, List<HenBatch> henBatch)
        {
            // Obtener los FarmIds de los HenBatch
            var farmIds = henBatch.Select(hb => hb.FarmId).Distinct().ToList();

            // Filtrar los StorageWarehouses que pertenezcan a esos FarmIds
            IQueryable<StorageWarehouse> storageWarehouses = storageWarehouseService.GetAll()
                .Include(sw => sw.MaterialContainers)
                    .ThenInclude(mc => mc.Material)
                    .Include(sw => sw.AcceptedMaterialType)
                    .Where(sw => farmIds.Contains(sw.FarmId) &&
                             sw.MaterialContainers.Any(mc =>
                                 mc.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificar) &&
                                 mc.Material.Quantity > 0));

            bool showCard = storageWarehouses.Any() &&
                storageWarehouses
                    .SelectMany(sw => sw.AcceptedMaterialType)
                    .ToList() // Ejecuta la consulta en la base de datos
                    .Any(amt => amt.ActionEnum == ActionsEnum.Store);


            if (showCard)
            {
                decimal hatchable = 0;
                decimal commercial = 0;

                IQueryable<MaterialContainer> materialHatchable = storageWarehouses.SelectMany(sw => sw.MaterialContainers.Where(mc => mc.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubable)));
                if (materialHatchable.Any())
                    hatchable = materialHatchable.Select(mi => mi.Quantity).Sum();

                IQueryable<MaterialBatchContainer> materialCommercial = storageWarehouses
                    .SelectMany(sw => sw.MaterialContainers.Where(mc => mc.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercial)))
                    .SelectMany(mc => mc.MaterialBatches.Where(mb => mb.Quantity > 0));

                decimal quantityCommercialEggsFromStorageWarehouse = storageWarehouses
                    .SelectMany(sw => sw.MaterialContainers.Where(mc => mc.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercial)))
                    .Sum(mc => mc.Quantity);

                if (materialCommercial.Any())
                    commercial = quantityCommercialEggsFromStorageWarehouse > 0 ? materialCommercial.Select(mi => mi.Quantity).Sum() : 0;

                SimpleDashboardInformativeCardDTO simpleCard = new SimpleDashboardInformativeCardDTO()
                {
                    Title = this.localizer[Lang.EggStockCardTitle],
                    SubtitleBold = this.localizer[Lang.EggStockHatchableCard] + hatchable.ToString("N0", new CultureInfo("is-IS")),
                    SubtitleSimple = this.localizer[Lang.EggStockCommercialCard] + commercial.ToString("N0", new CultureInfo("is-IS"))
                };
                card.SimpleCardDTOs.Add(simpleCard);
            }
        }

        /// <summary>
        /// Egg Stock Card
        /// </summary>
        public void GetOIFC(DashboardInformativeCardsDTO card, IQueryable<HenBatchPerformance> henBatchPerformance, IQueryable<GeneticsParametersReference> geneticsParameter, int hatchedEggsAccum)
        {
            var maxWeek = -1;
            var lastHBP = new List<HenBatchPerformance>(); // Lista vacía para evitar errores
            if (henBatchPerformance != null && henBatchPerformance.Any(hbp => !hbp.ParentId.HasValue && hbp.DistinctDayCount > 6))
            {
                maxWeek = henBatchPerformance
                    .Where(hbp => !hbp.ParentId.HasValue && hbp.DistinctDayCount > 6)
                    .Max(hbp => hbp.WeekNumber); // Obtiene la semana más alta disponible

                lastHBP = henBatchPerformance
                    .Where(hbp => !hbp.ParentId.HasValue && hbp.DistinctDayCount > 6 && hbp.WeekNumber == maxWeek)
                    .ToList(); // Devuelve todos los registros de esa semana
            }
            decimal OIFC = lastHBP != null && lastHBP.Any() && lastHBP.Sum(hbp => hbp.FemalesOnFirstProductionDate) != 0
                ? Math.Round((decimal)hatchedEggsAccum / lastHBP.Sum(hbp => hbp.FemalesOnFirstProductionDate), 2)
                : 0;
            string value = OIFC.ToString(DashboardDecimalPrecision.TwoDecimal);
            string weekNumber = lastHBP != null && lastHBP.Any() ? lastHBP.FirstOrDefault()?.WeekNumber.ToString() : "-";
            // Primero, obtenemos el WeekNumber fuera de la consulta LINQ
            int weekOIFC = lastHBP?.FirstOrDefault()?.WeekNumber ?? 0;
            decimal standardOIFC = weekOIFC != 0
                ? Math.Round(
                    geneticsParameter
                        .Where(gp => gp.TimePeriodValue == weekOIFC)
                        .Select(gp => gp.CumulativeIncubatedEggsByHen)
                        .AsEnumerable() // Realiza la operación en memoria
                        .DefaultIfEmpty(0) // En caso de que no haya elementos, usa 0
                        .Average(), 2)
                : 0;



            string IEP = standardOIFC != 0 ? (100 * (OIFC / standardOIFC)).ToString(DashboardDecimalPrecision.TwoDecimal) + "%" : "-";
            SimpleDashboardInformativeCardDTO simpleCard = new SimpleDashboardInformativeCardDTO()
            {
                Title = this.localizer[Lang.OIFCCardTitle],
                Value = value,
                NumberValue = OIFC,
                ValueColor = OIFC >= standardOIFC ? "LightGreen" : "red",
                SubtitleBold = this.localizer[Lang.IEPCardSubtitle] + IEP,
                SubtitleBoldColor = OIFC >= standardOIFC ? "LightGreen" : "red",
                SubtitleSimple = this.localizer[Lang.OIFCardWeek] + weekNumber
            };
            card.SimpleCardDTOs.Add(simpleCard);
        }
        /// <summary>
        /// Acumulated Hatching Card
        /// </summary>
        public void GetAccumulatedHatching(DashboardInformativeCardsDTO card,
            IQueryable<HenBatchPerformance> henBatchPerformance,
            IQueryable<GeneticsParametersReference> geneticsParameter)
        {
            var lastHBP = new List<HenBatchPerformance>();
            var query = henBatchPerformance;
            int henBatchPerformanceQueryCount = query.Count();

            if (henBatchPerformanceQueryCount == 0)
            {
                query = henBatchPerformanceService.GetAll()
                    .Where(hbp => card.HenBatchIds.Contains(hbp.HenBatchId));
                henBatchPerformanceQueryCount = query.Count();
            }

            // Query principal: registros com DistinctDayCount > 6 e HatchedEggs != 0
            var filteredWeeks = query
                .Where(hbp => !hbp.ParentId.HasValue &&
                    hbp.DistinctDayCount > 6 &&
                    hbp.HatchedEggs != 0)
                .Select(hbp => hbp.WeekNumber)
                .ToList();

            bool usedFallback = false;
            // Se não encontrar registros na query principal, tenta o fallback sem a condição de DistinctDayCount > 6
            if (!filteredWeeks.Any())
            {
                var fallbackQuery = henBatchPerformanceService.GetAll()
                    .Where(hbp => hbp.HatchedEggs > 0 &&
                        !hbp.ParentId.HasValue &&
                        card.HenBatchIds.Contains(hbp.HenBatchId));
                int fallbackCount = fallbackQuery.Count();

                if (fallbackCount > 0)
                {
                    filteredWeeks = fallbackQuery.Select(hbp => hbp.WeekNumber).ToList();
                    query = fallbackQuery;
                    usedFallback = true;
                }
            }

            // Se ainda não encontrou nenhuma semana, adiciona o card zerado e retorna
            if (!filteredWeeks.Any())
            {
                AddAccumulatedHatchingCard(card, 0, "-", 0);

                return;
            }

            int maxWeek = filteredWeeks.Max();

            // Se o fallback foi usado, não aplica o filtro de DistinctDayCount > 6
            if (usedFallback)
            {
                lastHBP = query
                    .Where(hbp => !hbp.ParentId.HasValue &&
                        hbp.WeekNumber == maxWeek)
                    .ToList();
            }
            else
            {
                lastHBP = query
                    .Where(hbp => !hbp.ParentId.HasValue &&
                        hbp.DistinctDayCount > 6 &&
                        hbp.WeekNumber == maxWeek)
                    .ToList();
            }

            decimal accumulatedHatching = 0;
            if (lastHBP.Any() && lastHBP.Sum(hbp => hbp.IncubatedEggsAccum) != 0)
            {
                accumulatedHatching = Math.Round(
                    100 * ((decimal)lastHBP.Sum(hbp => hbp.HatchedEggsAccum) /
                    lastHBP.Sum(hbp => hbp.IncubatedEggsAccum)), 2);
            }

            string weekNumber = lastHBP.Any() ? lastHBP.First().WeekNumber.ToString() : "-";

            IQueryable<GeneticsParametersReference> hatchingWeekly = null;
            if (lastHBP.Any())
            {
                int currentWeek = lastHBP.First().WeekNumber;
                hatchingWeekly = geneticsParameter?
                    .Where(gp => gp.TimePeriodValue <= currentWeek &&
                        gp.HatchingWeeklyPercentage != 0);
            }

            decimal standardAH = 0;
            if (hatchingWeekly != null && hatchingWeekly.Any())
            {
                var hatchingValues = hatchingWeekly.Select(hw => hw.HatchingWeeklyPercentage).ToList();
                if (hatchingValues.Any())
                {
                    standardAH = Math.Round(hatchingValues.Average(), 2);
                }
            }

            string IEE = standardAH != 0
                ? (100 * (accumulatedHatching / standardAH)).ToString(DashboardDecimalPrecision.TwoDecimal) + "%"
                : "-";

            AddAccumulatedHatchingCard(card, accumulatedHatching, weekNumber, standardAH, IEE);
        }

        private void AddAccumulatedHatchingCard(
            DashboardInformativeCardsDTO card,
            decimal accumulatedHatching,
            string weekNumber,
            decimal standardAH = 0,
            string IEE = "-")
        {
            string value = accumulatedHatching.ToString(DashboardDecimalPrecision.TwoDecimal) + "%";
            SimpleDashboardInformativeCardDTO simpleCard = new SimpleDashboardInformativeCardDTO()
            {
                Title = this.localizer[Lang.AccumulatedHatchingCardTitle],
                Value = value,
                NumberValue = accumulatedHatching,
                ValueColor = accumulatedHatching >= standardAH ? "LightGreen" : "red",
                SubtitleBold = this.localizer[Lang.IEECardSubtitle] + IEE,
                SubtitleBoldColor = accumulatedHatching >= standardAH ? "LightGreen" : "red",
                SubtitleSimple = this.localizer[Lang.AccumulatedHatchingWeek] + weekNumber
            };
            card.SimpleCardDTOs.Add(simpleCard);
        }

        /// <summary>
        /// Farm Structure Card
        /// </summary>
        public void GetFarmStructure(DashboardInformativeCardsDTO card, List<HenBatch> henBatches)
        {
            if (henBatches == null || !henBatches.Any())
            {
                // Si no hay elementos en la lista, retornamos un WidthCardDTO vacío
                card.WidthCardDTOs.Add(new WidthCardDTO
                {
                    Title = this.localizer[Lang.FarmStructureCardTitle],
                    WidthCards = new List<WidthCardListItemDTO>()
                });
                return;
            }

            // Tomamos el primer elemento para obtener el área
            HenBatch henBatch = henBatches.FirstOrDefault();
            // Obtener todos los FarmId de la lista de henBatches
            var farmIds = henBatches.Select(hb => hb.FarmId).ToList();
            // Filtrar los contenedores donde el FarmId esté en la lista de farmIds
            IQueryable<Container> containers = containerService.GetAll().Where(c => farmIds.Contains(c.FarmId));
            AreaEnum? area = henBatch != null ? (henBatch.HenStage == HenStage.Breeding ? AreaEnum.Breeding : AreaEnum.Laying) : default;

            // Si hay más de un henBatch, el título debe ser vacío
            string title = henBatches.Count > 1
                ? string.Empty
                : henBatch != null
                    ? (henBatch.HenStage == HenStage.Breeding ? this.localizer[Lang.FarmStructureBreedingCardTitle] : this.localizer[Lang.FarmStructureLayingCardTitle])
                    : this.localizer[Lang.FarmStructureCardTitle];

            WidthCardDTO widthCard = new WidthCardDTO()
            {
                Title = title,
            };

            if (containers.Any())
            {
                IQueryable<Container> henWarehouse = containers.Where(c => c.ContainerType == ContainerTypes.HenWarehouse && c.AreaContainers.Any(ac => ac.AreaEnum == area));
                int henWarehousesCount = henWarehouse.Count();
                widthCard.WidthCards.Add(new WidthCardListItemDTO()
                {
                    Text = this.localizer[Lang.HenWarehousesCountCardText],
                    Value = henWarehousesCount.ToString()
                });

                IQueryable<Line> lines = henWarehouse.Any() ? lineService.GetAll().Where(l => henWarehouse.Any(w => w.Id == l.WarehouseId)) : null;
                int linesCount = lines != null && lines.Any() ? lines.Count() : 0;
                widthCard.WidthCards.Add(new WidthCardListItemDTO()
                {
                    Text = this.localizer[Lang.LinesCountCardText],
                    Value = linesCount.ToString()
                });

                IQueryable<Container> silos = containers.Where(c => c.ContainerType == ContainerTypes.Silo && c.AreaContainers.Any(ac => ac.AreaEnum == area));
                int siloCount = silos.Count();
                widthCard.WidthCards.Add(new WidthCardListItemDTO()
                {
                    Text = this.localizer[Lang.SilosCountCardText],
                    Value = siloCount.ToString()
                });

                if (area == AreaEnum.Laying)
                {
                    IQueryable<Container> storageWarehouses = containers.Where(c => c.ContainerType == ContainerTypes.StorageWarehouse && c.AcceptedMaterialType.Any(amt => amt.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevo)));
                    int storageWarehousesCount = storageWarehouses.Any() ? storageWarehouses.SelectMany(sw => sw.AcceptedMaterialType.Where(amt => amt.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevo))).Count() : 0;
                    widthCard.WidthCards.Add(new WidthCardListItemDTO()
                    {
                        Text = this.localizer[Lang.StorageWarehousesCountCardText],
                        Value = storageWarehousesCount.ToString()
                    });
                }

                IQueryable<Container> activeHenBatch = containers.Where(c => c.ContainerType == ContainerTypes.HenBatch && c.Active);
                int activeParentHenBatch = activeHenBatch.Any() ? henBatchService.GetAll().Where(hb => activeHenBatch.Any(ahb => ahb.Id == hb.Id) && hb.ParentId == null).Count() : 0;
                widthCard.WidthCards.Add(new WidthCardListItemDTO()
                {
                    Text = this.localizer[Lang.ActiveParentHenBatchCardText],
                    Value = activeParentHenBatch.ToString()
                });
            }
            else
            {
                // Datos en blanco
                AddEmptyWidthCards(widthCard, area);
            }

            card.WidthCardDTOs.Add(widthCard);
        }

        private void AddEmptyWidthCards(WidthCardDTO widthCard, AreaEnum? area)
        {
            widthCard.WidthCards.Add(new WidthCardListItemDTO()
            {
                Text = this.localizer[Lang.HenWarehousesCountCardText],
                Value = "0"
            });
            widthCard.WidthCards.Add(new WidthCardListItemDTO()
            {
                Text = this.localizer[Lang.LinesCountCardText],
                Value = "0"
            });
            widthCard.WidthCards.Add(new WidthCardListItemDTO()
            {
                Text = this.localizer[Lang.SilosCountCardText],
                Value = "0"
            });
            if (area == AreaEnum.Laying)
            {
                widthCard.WidthCards.Add(new WidthCardListItemDTO()
                {
                    Text = this.localizer[Lang.StorageWarehousesCountCardText],
                    Value = "0"
                });
            }
            widthCard.WidthCards.Add(new WidthCardListItemDTO()
            {
                Text = this.localizer[Lang.ActiveParentHenBatchCardText],
                Value = "0"
            });
        }


        /// <summary>
        /// Build dashboard cards DTO given by CardEnum List and Area
        /// </summary>
        public DashboardCardsDTO GetCardsData(List<CardsEnum> cards, AreaEnum? area, DateTime? date = null)
        {
            if (!date.HasValue)
                date = DateTime.Today.Date;

            DashboardCardsDTO dto = new DashboardCardsDTO
            {
                CardList = new List<CardDTO>()
            };

            List<Guid> userFarmIds = operationContext.GetUserSiteIds();

            IQueryable<HenBatch> query = henBatchService.GetAll(true).Where(hb => hb.Active);

            if (userFarmIds != null)
                query = query.Where(hb => hb.FarmId.HasValue && userFarmIds.Contains(hb.FarmId.Value));

            List<HenBatchStatusDTO> henBatches = query.Where(hb => hb.LineId.HasValue)
               .Select(hb => new HenBatchStatusDTO()
               {
                   WarehouseId = hb.Line.WarehouseId,
                   GeneticId = hb.GeneticId,
                   HenStage = hb.HenStage,
                   HenReports = hb.Reports.Select(hr => new HenReportDTO()
                   {
                       DeadFemale = hr.DeadFemale,
                       HenAmountFemale = hr.HenAmountFemale,
                       WaterConsumptionLastHR = hr.WaterConsumption,
                       WaterConsumptionBeforeLastHR = hr.WaterConsumption,
                       EggQuantityLastHR = hr.TotalEggs,
                       EggQuantityBeforeLastHR = hr.TotalEggs,
                       HenBatchPerformanceWeekNumber = hr.HenBatchPerformance.WeekNumber,
                       LastDate = hr.Date,
                       ReportEnumNew = hr.ReportEnum == ReportEnum.New
                   })
               }).ToList();

            if (area.HasValue)
                GetSpecificAreaCardsData(dto, cards, area.Value, date.Value, henBatches);

            GetNonSpecificAreaCardsData(dto, cards, area, henBatches);

            return dto;
        }

        /// <summary>
        /// Build dashboard cards DTO given by Areas
        /// </summary>
        public List<DashboardCardsDTO> GetCardsDataForHome(List<AreaEnum> areas)
        {
            DateTime date = DateTime.Today.Date;

            List<string> roles = this.operationContext.GetUserRoles().ToList();

            List<Guid> userFarmIds = operationContext.GetUserSiteIds();

            IQueryable<HenBatch> allHenBatches = henBatchService.GetAll(true).Where(hb => hb.Active);

            if (userFarmIds != null)
                allHenBatches = allHenBatches.Where(hb => hb.FarmId.HasValue && userFarmIds.Contains(hb.FarmId.Value));

            List<HenBatchStatusDTO> henBatches = allHenBatches.Where(hb => hb.LineId.HasValue)
                .Select(hb => new HenBatchStatusDTO()
                {
                    WarehouseId = hb.Line.WarehouseId,
                    GeneticId = hb.GeneticId,
                    HenStage = hb.HenStage,
                    HenReports = hb.Reports.Select(hr => new HenReportDTO()
                    {
                        DeadFemale = hr.DeadFemale,
                        HenAmountFemale = hr.HenAmountFemale,
                        WaterConsumptionLastHR = hr.WaterConsumption,
                        WaterConsumptionBeforeLastHR = hr.WaterConsumption,
                        EggQuantityLastHR = hr.TotalEggs,
                        EggQuantityBeforeLastHR = hr.TotalEggs,
                        HenBatchPerformanceWeekNumber = hr.HenBatchPerformance.WeekNumber,
                        LastDate = hr.Date,
                        ReportEnumNew = hr.ReportEnum == ReportEnum.New
                    })
                }).ToList();

            // Cards
            List<DashboardCardsDTO> cardsData = new List<DashboardCardsDTO>();
            foreach (AreaEnum area in areas)
            {
                List<CardsEnum> cards = GetCards(roles, area.ToString());

                DashboardCardsDTO dto = new DashboardCardsDTO
                {
                    CardList = new List<CardDTO>()
                };

                GetSpecificAreaCardsData(dto, cards, area, date, henBatches);

                GetNonSpecificAreaCardsData(dto, cards, area, henBatches);

                cardsData.Add(dto);
            }

            // Add task alerts card. Default to accumulated data.
            // If more than one area is available for the user, the
            // filtered data is fetched when needed from the view
            List<CardsEnum> alertCrads = GetCards(roles, null, true);

            DashboardCardsDTO alertDto = new DashboardCardsDTO
            {
                CardList = new List<CardDTO>()
            };

            GetNonSpecificAreaCardsData(alertDto, alertCrads, null, henBatches);

            cardsData.Add(alertDto);

            return cardsData;
        }

        private List<CardsEnum> GetCards(List<string> roles, string area, bool alertCards = false)
        {
            List<CardsEnum> cards = new List<CardsEnum>();

            if (alertCards)
                cards = new List<CardsEnum>() { CardsEnum.TaskAlerts, CardsEnum.RunningOutMaterialAlert, CardsEnum.MortalityWarning, CardsEnum.WaterConsumption, CardsEnum.EggProduction };
            else
            {
                if (roles.Any(rol => rol.Contains("Happening") || rol.Contains(area + "Administrator")) || roles.Contains(Roles.BackofficeSuperAdministrator))
                    cards.Add(CardsEnum.Happening);
                if (roles.Any(rol => rol.Contains("Inconsistencies") || rol.Contains("Inconsistency") || rol.Contains(area + "Administrator")) || roles.Contains(Roles.BackofficeSuperAdministrator))
                    cards.Add(CardsEnum.Inconsistencies);
            }

            return cards;
        }

        /// <summary>
        /// HenBatch Week
        /// </summary>
        public void GetActualWeek(DashboardInformativeCardsDTO card, IQueryable<HenBatchPerformance> henBatchPerformance, IQueryable<GeneticsParametersReference> geneticsParameter)
        {
            // Función para calcular la semana del lote.
            Func<HenBatch, int> getBatchWeekNumber = henBatch => henBatch.BatchWeekNumber + (DateTime.Today.Date - henBatch.DateStart.Value.GetPastSelectedDay(henBatch.Farm.DayOfWeek)).Days / 7;

            // Obtener los lotes relacionados con los IDs proporcionados en card.HenBatchId.
            List<HenBatch> henBatches = henBatchService.GetAll()
                .Include(hb => hb.Farm)
                .Where(hb => card.HenBatchIds.Contains(hb.Id))
                .ToList();

            // Calcular el número de semana más alto.
            int? highestWeekNumber = henBatches.Any()
                ? henBatches.Max(hb => getBatchWeekNumber(hb))
                : (int?)null;

            string week = highestWeekNumber.HasValue ? highestWeekNumber.Value.ToString() : "-";

            SimpleDashboardInformativeCardDTO simpleCard = new SimpleDashboardInformativeCardDTO()
            {
                Title = this.localizer[Lang.HenBatchWeekCardTitle],
                Value = week,
                NumberValue = highestWeekNumber ?? 0
            };

            card.SimpleCardDTOs.Add(simpleCard);
        }

        /// <summary>
        /// HenBatch Week
        /// </summary>
        public void GetCurrentWeekAverage(DashboardInformativeCardsDTO card, List<Guid> henBatchIds)
        {
            // Función para calcular la semana del lote.
            Func<HenBatch, int> getBatchWeekNumber = henBatch => henBatch.BatchWeekNumber + (DateTime.Today.Date - henBatch.DateStart.Value.GetPastSelectedDay(henBatch.Farm.DayOfWeek)).Days / 7;

            // Obtener los lotes relacionados con los IDs proporcionados en card.HenBatchId.
            List<HenBatch> henBatches = henBatchService.GetAll()
                .Include(hb => hb.Farm)
                .Where(hb => henBatchIds.Contains(hb.Id) && hb.ParentId == null)
                .ToList();

            decimal? averageWeekNumber = henBatches.Any()
                ? (decimal)henBatches.Average(hb => getBatchWeekNumber(hb))
                : (decimal?)null;

            string week = averageWeekNumber.HasValue ? averageWeekNumber.Value.ToString() : "-";

            SimpleDashboardInformativeCardDTO simpleCard = new SimpleDashboardInformativeCardDTO()
            {
                Title = this.localizer[Lang.HenBatchWeekCardTitle],
                Value = week,
                NumberValue = averageWeekNumber ?? 0
            };

            card.SimpleCardDTOs.Add(simpleCard);
        }

        /// <summary>
        /// Add blanc dashboard cards DTO
        /// </summary>
        public DashboardCardsDTO GetBlancCards(DashboardCardsDTO dto, List<CardsEnum> cards)
        {
            foreach (CardsEnum c in cards)
            {
                if (!dto.CardList.Any(cl => cl.CardEnum == c))
                {
                    CardDTO card = new CardDTO
                    {
                        CardEnum = c
                    };

                    if (c == CardsEnum.MortalityWarning)
                        card.CardTitle = this.localizer[Lang.MortalityCardTitle];
                    else if (c == CardsEnum.WaterConsumption)
                        card.CardTitle = this.localizer[Lang.WaterConsumptionCardTitle];
                    else if (c == CardsEnum.EggProduction)
                        card.CardTitle = this.localizer[Lang.EggProductionCardTitle];

                    card.CardData = new List<CardDataDTO>
                    {
                        new CardDataDTO()
                    };
                    card.CardData[0].Value = "0";
                    card.CardData[0].Label = "0%";

                    card.CardData.Add(new CardDataDTO());
                    card.CardData.Last().Label = "";

                    dto.CardList.Add(card);
                }
            }
            return dto;
        }

        private DashboardCardsDTO GetNonSpecificAreaCardsData(DashboardCardsDTO dto, List<CardsEnum> cards, AreaEnum? area, List<HenBatchStatusDTO> henBatches)
        {
            if (cards.Contains(CardsEnum.TaskAlerts))
                GetTaskAlertsCard(dto, area);

            if (cards.Contains(CardsEnum.RunningOutMaterialAlert))
                GetRunningOutMaterialRowCard(dto, area);

            if (cards.Contains(CardsEnum.MortalityWarning))
                GetMortalityWarningCard(dto, area, henBatches);

            if (cards.Contains(CardsEnum.WaterConsumption))
                GetWaterConsumptionCard(dto, area, henBatches);

            if (cards.Contains(CardsEnum.EggProduction))
                GetEggProductionCard(dto, henBatches);

            if (cards.Contains(CardsEnum.SerologyNumberOfPendingReports) && cards.Contains(CardsEnum.SerologyPercentagesFromTheLastWeek))
                GetSerologyCards(dto);

            return dto;
        }

        /// <summary>
        /// Build dashboard cards DTO given by CardEnum List and Area
        /// </summary>
        private DashboardCardsDTO GetSpecificAreaCardsData(DashboardCardsDTO dto, List<CardsEnum> cards, AreaEnum area, DateTime date, List<HenBatchStatusDTO> henBatches)
        {
            dto.Area = area.ToString();
            dto.LocalizedArea = EnumHelper<AreaEnum>.GetDisplayName(area, localizer);

            HenStage henStage = HenStage.Laying;
            if (area == AreaEnum.Breeding)
                henStage = HenStage.Breeding;

            if (cards.Contains(CardsEnum.YearWeek))
                GetWeekOfYearCardData(dto);

            if (cards.Contains(CardsEnum.HenBatchesWeek))
                GetHenBatchesWeekCardData(dto, henStage);

            if (cards.Contains(CardsEnum.Production))
                GetProductionCardData(dto);

            if (cards.Contains(CardsEnum.FeedIntakePerHen))
                GetFeedIntakePerHenCardData(dto, henStage);

            if (cards.Contains(CardsEnum.HenDay))
                GetHenDayCardData(dto);

            if (cards.Contains(CardsEnum.Happening))
                GetHappeningCardData(dto, area);

            if (cards.Contains(CardsEnum.Inconsistencies))
                GetInconsistencyCardData(dto, area);

            if (cards.Contains(CardsEnum.Stocks))
                GetMaterialStockCardData(dto, area);

            if (cards.Contains(CardsEnum.ToClassifyCommon))
                GetToClassifyCommonCardData(dto);

            if (cards.Contains(CardsEnum.ToClassifySpecial))
                GetToClassifySpecialCardData(dto);

            if (cards.Contains(CardsEnum.ProcessedEggs))
                GetProcessedEggsCardData(dto, AreaEnum.Classification, henBatches);

            if (cards.Contains(CardsEnum.ActiveFormulas))
                GetActiveFormulaCardData(dto);

            if (cards.Contains(CardsEnum.HousedHens))
                GetHousedHensCardData(dto, henBatches);

            if (cards.Contains(CardsEnum.FoodBalance))
                GetFoodBalanceCardData(dto);

            if (cards.Contains(CardsEnum.PackedSKU))
                GetPackedSKUCardData(dto);

            if (cards.Contains(CardsEnum.MostPackedSKU))
                GetMostPackedSKU(dto);

            if (cards.Contains(CardsEnum.PreparedOrders))
                GetPreparedOrders(dto);

            if (cards.Contains(CardsEnum.ExpiredTasks))
                GetExpiredTasksCard(dto, area);

            if (cards.Contains(CardsEnum.CriticEvents))
                GetCriticEventsRowCard(dto, area);

            // Handle Inconsistencies and Happenings card combination
            if (dto.CardList.Any(c => c.CardEnum == CardsEnum.Inconsistencies) && dto.CardList.Any(c => c.CardEnum == CardsEnum.Happening))
                dto.CardList.Where(c => c.CardEnum == CardsEnum.Happening).FirstOrDefault().CardTitle = this.localizer[Lang.Alerts];

            return dto;
        }
        #endregion

        #region Gets
        /// <summary>
        /// Gets week of year card.
        /// </summary>
        private void GetWeekOfYearCardData(DashboardCardsDTO dto)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.WeekOfYear],
                CardEnum = CardsEnum.YearWeek,
                CardData = new List<CardDataDTO>
            {
                // week of year
                new CardDataDTO()
            }
            };
            card.CardData.Last().Label = this.localizer[Lang.WeekNumberLabel];

            try
            {
                card.CardData[0].Value = this.commonChartsBusinessLogic.GetWeekOfYear(this.localizer[Lang.CultureName], DateTime.Today).ToString();
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Gets hen batch week data (min, max and weighted average) card.
        /// </summary>
        private void GetHenBatchesWeekCardData(DashboardCardsDTO dto, HenStage henStage)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.WeekCardLabel],
                CardEnum = CardsEnum.HenBatchesWeek,
                CardData = new List<CardDataDTO>
            {
                // Average week
                new CardDataDTO()
            }
            };
            card.CardData.Last().Label = this.localizer[Lang.BatchWeightedAverageWeekLabel];
            // Min week
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.Min];
            // Max week
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.Max];

            try
            {
                List<HenBatchStatusDTO> henBatches = this.commonChartsBusinessLogic.GetHenBatchesStatusData(henStage: henStage, onlyParents: true).OrderBy(dto => dto.Age).ToList();
                bool anyHenBatches = henBatches != null && henBatches.Any();

                // Min batch week
                decimal batchMinWeek = anyHenBatches ? henBatches.FirstOrDefault().Age : 0;

                // Max batch week
                decimal batchMaxWeek = anyHenBatches ? henBatches.LastOrDefault().Age : 0;

                // Weighted average

                decimal totalHens = henBatches.Sum(s => s.HenAmount);
                decimal batchWeightedAverageWeek = 0;
                foreach (HenBatchStatusDTO henBatch in henBatches)
                    batchWeightedAverageWeek += henBatch.HenAmount * henBatch.Age / totalHens;
                // Round decimal number
                batchWeightedAverageWeek = decimal.Round(batchWeightedAverageWeek, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero);

                card.CardData[0].Value = batchWeightedAverageWeek.ToString(DashboardDecimalPrecision.TwoDecimal);
                card.CardData[1].Value = batchMinWeek.ToString();
                card.CardData[2].Value = batchMaxWeek.ToString();
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get last day's production
        /// </summary>
        private void GetProductionCardData(DashboardCardsDTO dto)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.BoxesLabel],
                CardEnum = CardsEnum.Production,
                CardData = new List<CardDataDTO>()
            };

            string cultureName = this.localizer[Lang.CultureName];
            int week = this.commonChartsBusinessLogic.GetWeekOfYear(cultureName, DateTime.Today);

            // Last production
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.Yesterday];
            // Last week production
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.Average] + ' ' + this.localizer[Lang.Week] + ' ' + (week - 1);

            // Variables to convert units to boxes
            CapacityUnit eggCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Box);
            decimal eggRelativeValue = Convert.ToDecimal(eggCapacityUnit.RelativeValue);
            try
            {
                // Yesterday's production
                // Get data
                long eggs = this.henReportService.GetAll().Where(hr => hr.Date == DateTime.Today.AddDays(-1) && hr.HenBatch.HenStage == HenStage.Laying && hr.ReportEnum == ReportEnum.New).Sum(hr => hr.TotalEggs);

                if (eggs != 0)
                {
                    decimal lastProduction = eggs / eggRelativeValue;
                    card.CardData[0].Value = (decimal.Round(lastProduction, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero)).ToString(DashboardDecimalPrecision.TwoDecimal) + " " + this.localizer[Lang.EggCapacityUnitBoxesPluralSymbol];
                }
                else
                {
                    card.CardData[0].Value = this.localizer[Lang.ErrorMessage];
                }

                // Last week's average production
                //Date limits
                DateTime today = DateTime.Today;
                int currentDays = this.commonChartsBusinessLogic.DaysFromStartOfWeekForThisFarm(today.DayOfWeek);
                DateTime maxDate = today.AddDays(currentDays);
                DateTime minDate = maxDate.AddDays(-7);
                // Get hen batch performances
                long lastWeekPerformance = this.henBatchPerformanceService.GetAll().Where(hbp => minDate <= hbp.Date && hbp.Date < maxDate && hbp.HenBatch.HenStage == HenStage.Laying).Sum(hbp => hbp.TotalEggs);
                if (lastWeekPerformance != 0)
                {
                    decimal lastWeekProduction = lastWeekPerformance / eggRelativeValue / 7;
                    card.CardData[1].Value = decimal.Round(lastWeekProduction, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero).ToString(DashboardDecimalPrecision.TwoDecimal) + " " + this.localizer[Lang.EggCapacityUnitBoxesPluralSymbol];
                }
                else
                {
                    card.CardData[1].Value = this.localizer[Lang.ErrorMessage];
                }
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get last week's and current hen day
        /// </summary>
        private void GetHenDayCardData(DashboardCardsDTO dto)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.HenDayCardTitle],
                CardEnum = CardsEnum.HenDay,
                CardData = new List<CardDataDTO>()
            };

            string cultureName = this.localizer[Lang.CultureName];
            int week = this.commonChartsBusinessLogic.GetWeekOfYear(cultureName, DateTime.Today);

            // Last production
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.Yesterday];
            // Last week production
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.Average] + ' ' + this.localizer[Lang.Week] + ' ' + (week - 1);

            try
            {
                //Date limits
                DateTime today = DateTime.Today;
                int currentDays = this.commonChartsBusinessLogic.DaysFromStartOfWeekForThisFarm(today.DayOfWeek);
                DateTime maxDate = today.AddDays(currentDays);
                DateTime minDate = maxDate.AddDays(-7);

                // Yesterday's hen day
                // Get data
                IQueryable<HenBatchPerformance> performances = henBatchPerformanceService.GetAll(true).Where(hbp => minDate <= hbp.Date && hbp.HenBatch.HenStage == HenStage.Laying);
                IQueryable<HenBatchPerformance> currentTweekPerformance = performances.Where(hbp => hbp.Date >= maxDate);
                IQueryable<HenReport> henReports = henReportService.GetAll(true).Where(hr => hr.Date == DateTime.Today.AddDays(-1) && hr.HenBatch.HenStage == HenStage.Laying && hr.ReportEnum == ReportEnum.New);

                if (henReports.Any() && currentTweekPerformance.Any())
                {
                    decimal yesterdayHenDay = 100.0M * henReports.Sum(hr => hr.TotalEggs) / (decimal)(currentTweekPerformance.Sum(hbp => hbp.HenAmountFemale) - currentTweekPerformance.Sum(hbp => hbp.DepopulateFemaleAccum));
                    card.CardData[0].Value = (decimal.Round(yesterdayHenDay, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero)).ToString(DashboardDecimalPrecision.TwoDecimal) + " %";
                }
                else
                {
                    card.CardData[0].Value = this.localizer[Lang.ErrorMessage];
                }

                // Last week's average hen day
                // Get data
                IQueryable<HenBatchPerformance> lastWeekPerformance = performances.Where(hbp => hbp.Date < maxDate);
                if (lastWeekPerformance.Any())
                {
                    decimal lastweekHenDay = 100.0M * lastWeekPerformance.Sum(hbp => hbp.TotalEggs) / (lastWeekPerformance.Sum(hbp => hbp.HenAmountFemale) - lastWeekPerformance.Sum(hbp => hbp.DepopulateFemaleAccum)) / (decimal)7;
                    card.CardData[1].Value = decimal.Round(lastweekHenDay, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero).ToString(DashboardDecimalPrecision.TwoDecimal) + " %";
                }
                else
                {
                    card.CardData[1].Value = this.localizer[Lang.ErrorMessage];
                }
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get last week's feed intake per hen in grams
        /// </summary>
        private void GetFeedIntakePerHenCardData(DashboardCardsDTO dto, HenStage henStage)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.FeedIntakeLabel],
                CardEnum = CardsEnum.FeedIntakePerHen,
                CardData = new List<CardDataDTO>()
            };

            string cultureName = this.localizer[Lang.CultureName];
            int week = this.commonChartsBusinessLogic.GetWeekOfYear(cultureName, DateTime.Today);

            // Last intake
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.Yesterday];
            // Last week intake
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.Average] + ' ' + this.localizer[Lang.Week] + ' ' + (week - 1);

            // Units to convert to grams
            CapacityUnit feedIntakeCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Grams);
            decimal feedIntakeRelativeValue = Convert.ToDecimal(feedIntakeCapacityUnit.RelativeValue);
            string feedIntakeUnitSymbol = feedIntakeCapacityUnit.Symbol;

            try
            {
                // Date limits
                DateTime today = DateTime.Today;
                int currentDays = this.commonChartsBusinessLogic.DaysFromStartOfWeekForThisFarm(today.DayOfWeek);
                DateTime maxDate = today.AddDays(currentDays);
                DateTime minDate = maxDate.AddDays(-7);

                // Yesterday's feed intake
                // Get data
                IQueryable<HenReport> henReports = this.henReportService.GetAll().Where(hr => hr.Date == DateTime.Today.AddDays(-1) && hr.HenBatch.HenStage == henStage && hr.ReportEnum == ReportEnum.New);
                List<Guid> henBatchIds = henReports.Select(hr => hr.HenBatchId).ToList();

                IQueryable<HenBatchPerformance> performances = this.henBatchPerformanceService.GetAll()
                    .Where(hbp => minDate <= hbp.Date &&
                    hbp.HenBatch.HenStage == henStage &&
                    henBatchIds.Contains(hbp.HenBatchId));

                List<HenBatchPerformance> currenTweekPerformance = performances.Where(hbp => hbp.Date >= maxDate).ToList();

                if (henReports.Any() && currenTweekPerformance.Any())
                {
                    decimal yesterdayFeedIntakePerHen = henReports.Sum(hr => hr.FeedIntakeFemale) / (currenTweekPerformance.Sum(hbp => hbp.HenAmountFemale) - currenTweekPerformance.Sum(hbp => hbp.DepopulateFemaleAccum)) / feedIntakeRelativeValue;
                    card.CardData[0].Value = decimal.Round(Convert.ToDecimal(yesterdayFeedIntakePerHen), DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero).ToString(DashboardDecimalPrecision.TwoDecimal) + " " + this.localizer[Lang.GramsPerHenPerDay];
                }
                else
                {
                    card.CardData[0].Value = this.localizer[Lang.ErrorMessage];
                }

                // Last week's feed intake
                // Get data
                IQueryable<HenBatchPerformance> lastWeekPerformance = performances.Where(hbp => hbp.Date < maxDate);

                if (lastWeekPerformance.Any())
                {
                    decimal lastWeekFeedIntakePerHen = lastWeekPerformance.Sum(a => a.FeedIntakeFemale) / (lastWeekPerformance.Sum(a => a.HenAmountFemale) - lastWeekPerformance.Sum(a => a.DepopulateFemaleAccum)) / (decimal)7 / feedIntakeRelativeValue;

                    card.CardData[1].Value = decimal.Round(lastWeekFeedIntakePerHen, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero).ToString(DashboardDecimalPrecision.TwoDecimal) + " " + this.localizer[Lang.GramsPerHenPerDay];
                }
                else
                {
                    card.CardData[1].Value = this.localizer[Lang.ErrorMessage];
                }
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Gets name and amount of happenings from an area.
        /// </summary>
        private void GetHappeningCardData(DashboardCardsDTO dto, AreaEnum area, int days = 2)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.ActiveHappenings],
                CardEnum = CardsEnum.Happening,
                CardData = new List<CardDataDTO>()
            };

            // Happenings
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.ActiveHappenings];

            try
            {
                card.CardData.Last().Value = this.happeningService.GetOpenFromArea(area, days).Count().ToString();
            }
            catch
            {
                card.CardData.Last().Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get inconsistencies card
        /// </summary>
        private void GetInconsistencyCardData(DashboardCardsDTO dto, AreaEnum area)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.ActiveHappenings],
                CardEnum = CardsEnum.Inconsistencies,
                CardData = new List<CardDataDTO>()
            };

            // Inconsistencies
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.ActiveInconsistencies];

            try
            {
                IQueryable<Container> containers = this.containerService.GetAllFullFiltered(area.ToString());

                // Inconsistencies in pending count for origins
                IQueryable<InconsistencyReport> inconsistencies = containers.SelectMany(c => c.OriginInconsistencyReports.Where(ir => ir.Status == InconsistencyReportStatusEnum.PendingReview));
                // Inconsistencies in pending count for destinations
                inconsistencies = inconsistencies.Concat(containers.SelectMany(c => c.DestinationInconsistencyReports.Where(ir => ir.Status == InconsistencyReportStatusEnum.PendingReview))).Distinct();
                card.CardData.Last().Value = inconsistencies.Count().ToString();
            }
            catch
            {
                card.CardData.Last().Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get stock cards
        /// </summary>
        private void GetMaterialStockCardData(DashboardCardsDTO dto, AreaEnum area)
        {
            dto.StockCardsRemainingDaysLabel = this.localizer[Lang.Days];

            // Get containers
            IQueryable<MaterialContainer> materialContainers = containerService.GetAll(true)
                .Where(c => c.AreaContainers.Any(ac => ac.AreaEnum == area))
                .SelectMany(c => c.MaterialContainers);

            List<MaterialStockDTO> materialConsumptionDTO = this.commonChartsBusinessLogic
                .GetMaterialConsumptionDTO(materialContainers)
                .ToList();

            foreach (MaterialStockDTO item in materialConsumptionDTO)
            {
                if (item.RemainingDays == -1)
                {
                    item.NoData = this.localizer[Lang.NoConsumption];
                    item.StatusColor = StockStatusColors.NoConsumption;
                    item.StockStatus = StockStatusEnum.NoConsumption;
                }
                else if (0 <= item.RemainingDays && item.RemainingDays < item.Material.MinimalStock)
                {
                    item.StatusColor = StockStatusColors.Critic;
                    item.StockStatus = StockStatusEnum.Critic;
                }
                else if (item.Material.MinimalStock <= item.RemainingDays && item.RemainingDays < item.Material.OptimalStock)
                {
                    item.StatusColor = StockStatusColors.Alert;
                    item.StockStatus = StockStatusEnum.Alert;
                }
                else if (item.Material.OptimalStock <= item.RemainingDays)
                {
                    item.StatusColor = StockStatusColors.Normal;
                    item.StockStatus = StockStatusEnum.Normal;
                }
            }

            dto.StockCards = materialConsumptionDTO;
        }

        /// <summary>
        /// Get data of common eggs 
        /// </summary>
        private void GetToClassifyCommonCardData(DashboardCardsDTO dto)
        {
            Guid currentTenantId = this.userService.Get(this.operationContext.GetUserId()).TenantId;
            AreaEnum storageWarehouseArea = this.tenantConfigurationService.GetAll().Where(conf => conf.TenantId == currentTenantId && conf.TenantConfigurationEnum == TenantConfigurationEnum.ClassificationProducesStock).Any() ? AreaEnum.Classification : AreaEnum.Laying;

            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.ToClassifyLabelCommon],
                CardEnum = CardsEnum.ToClassifyCommon,
                CardData = new List<CardDataDTO>()
            };

            // Variables to convert units to boxes
            CapacityUnit eggCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Box);
            decimal eggRelativeValue = Convert.ToDecimal(eggCapacityUnit.RelativeValue);
            string eggUnitSymbol = this.localizer[Lang.EggCapacityUnitBoxesPluralSymbol];

            // Total
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = eggUnitSymbol + " | " + this.localizer[Lang.TotalToClassifyLabelCommon];
            // White
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = eggUnitSymbol + " | " + this.localizer[Lang.WhiteToClassifyLabelCommon];
            // Color
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = eggUnitSymbol + " | " + this.localizer[Lang.ColorToClassifyLabelCommon];

            try
            {

                // Get ids of storage warehouses thar deliver to classification warehouses
                Guid[] originContainerIds = this.classificationWarehouseService.GetAll(true)
                    .SelectMany(cw => cw.OriginContainers)
                    .Select(oc => oc.OriginId)
                    .ToArray();

                // Get stock of unclassified eggs in storage warehouses of Laying area
                // which deliver to classification warehouses.
                List<MaterialStockDTO> stocks = this.storageWarehouseService.GetAll(true)
                    .Where(sw => sw.AreaContainers.Any(ac => ac.AreaEnum == storageWarehouseArea) && originContainerIds.Contains(sw.Id))
                    .SelectMany(sw => sw.MaterialContainers)
                    .Where(mc => mc.Material.MaterialType.Path.EndsWith(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificar))
                    .Select(mc => new
                    {
                        mc.Material.MaterialTypeId,
                        mc.Material
                    })
                    .ToList()
                    .GroupBy(mc => mc.MaterialTypeId)
                    .Select(g => new MaterialStockDTO
                    {
                        Material = g.Max(mc => mc.Material),
                        Stock = g.Sum(mc => mc.Material.Quantity)
                    }).ToList();

                MaterialStockDTO materialStockDTO = stocks.FirstOrDefault(s => s.Material.MaterialType.Path.EndsWith(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubable));

                card.CardData[1].Value = ((int)((materialStockDTO != null ? materialStockDTO.Stock : 0) / eggRelativeValue)).ToString();

                materialStockDTO = stocks.FirstOrDefault(s => s.Material.MaterialType.Path.StartsWith(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercial));
                card.CardData[2].Value = ((int)((materialStockDTO != null ? materialStockDTO.Stock : 0) / eggRelativeValue)).ToString();

                card.CardData[0].Value = (int.Parse(card.CardData[1].Value) + int.Parse(card.CardData[2].Value)).ToString(DecimalPrecision.NoDecimal);
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get data of special eggs 
        /// </summary>
        private void GetToClassifySpecialCardData(DashboardCardsDTO dto)
        {
            Guid? currentTenantId = operationContext.GetUserTenantId();
            AreaEnum storageWarehouseArea = this.tenantConfigurationService.GetAll().Where(conf => conf.TenantId == currentTenantId && conf.TenantConfigurationEnum == TenantConfigurationEnum.ClassificationProducesStock).Any() ? AreaEnum.Classification : AreaEnum.Laying;

            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.ToClassifyLabelSpecial],
                CardEnum = CardsEnum.ToClassifySpecial,
                CardData = new List<CardDataDTO>()
            };

            // Variables to convert units to boxes
            CapacityUnit eggCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Box);
            decimal eggRelativeValue = Convert.ToDecimal(eggCapacityUnit.RelativeValue);
            string eggUnitSymbol = this.localizer[Lang.EggCapacityUnitBoxesPluralSymbol];

            try
            {
                // Get ids of storage warehouses thar deliver to classification warehouses
                List<Guid> originContainerIds = this.classificationWarehouseService.GetAll()
                    .SelectMany(cw => cw.OriginContainers)
                    .Select(oc => oc.OriginId)
                    .ToList();

                // Get stock of unclassified eggs in storage warehouses of Laying area
                // which deliver to classification warehouses.
                List<MaterialStockDTO> stocks = this.storageWarehouseService.GetAllFullForDashboard()
                    .Where(sw => sw.AreaContainers.Any(ac => ac.AreaEnum == storageWarehouseArea) && originContainerIds.Contains(sw.Id))
                    .SelectMany(sw => sw.MaterialContainers)
                    .Where(mc => mc.Material.MaterialType.Path.StartsWith(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificar) &&
                    !mc.Material.MaterialType.Path.EndsWith(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercial) &&
                    !mc.Material.MaterialType.Path.EndsWith(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubable))
                    .Select(mc => mc.Material)
                    .ToList()
                    .GroupBy(mc => mc)
                    .Select(g => new MaterialStockDTO
                    {
                        Material = g.Key,
                        Stock = g.Sum(mc => mc.Quantity)
                    })
                    .ToList();

                if (stocks.Count != 0)
                {
                    // Total
                    card.CardData.Add(new CardDataDTO());
                    card.CardData.Last().Label = eggUnitSymbol + " | " + this.localizer[Lang.TotalToClassifyLabelCommon];
                    int total = 0;

                    //Rest of specials
                    foreach (MaterialStockDTO stock in stocks)
                    {
                        card.CardData.Add(new CardDataDTO());
                        card.CardData.Last().Label = eggUnitSymbol + " | "
                            + stock.Material.MaterialType.Parent.Name + " | "
                            + stock.Material.MaterialType.Name;
                        card.CardData.Last().Value = ((int)(stock.Stock / eggRelativeValue)).ToString();
                        total += int.Parse(card.CardData.Last().Value);
                    }
                    card.CardData[0].Value = total.ToString();
                }
                else
                {
                    card.CardData.Add(new CardDataDTO());
                    card.CardData.Last().Label = "";
                    card.CardData.Last().Value = this.localizer[Lang.ErrorMessage];
                }
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get today's number of classified eggs and average of collected batches
        /// </summary>
        private void GetProcessedEggsCardData(DashboardCardsDTO dto, AreaEnum area, List<HenBatchStatusDTO> henBatches)
        {
            Guid? currentTenantId = operationContext.GetUserTenantId();
            AreaEnum storageWarehouseArea = this.tenantConfigurationService.GetAll().Where(conf => conf.TenantId == currentTenantId && conf.TenantConfigurationEnum == TenantConfigurationEnum.ClassificationProducesStock).Any() ? AreaEnum.Classification : AreaEnum.Laying;

            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.ProcessedEggsCardTitle],
                CardEnum = CardsEnum.ProcessedEggs,
                CardData = new List<CardDataDTO>()
            };

            // Variables to convert units to boxes
            CapacityUnit eggCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Box);
            decimal eggRelativeValue = Convert.ToDecimal(eggCapacityUnit.RelativeValue);
            string eggUnitSymbol = this.localizer[Lang.EggCapacityUnitBoxesPluralSymbol];

            // Collected
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.CollectedHenBatchesLabel];
            card.CardData.Last().DataTitle = this.localizer[Lang.Collected];
            // Classified
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = eggUnitSymbol + " | " + this.localizer[Lang.ClassifiedLabel];
            card.CardData.Last().DataTitle = this.localizer[Lang.ClassifiedLabel];

            try
            {
                // Get active laying hen batches data
                List<HenBatchStatusDTO> henBatchesStatus = henBatches
                    .Where(hb => hb.HenStage == HenStage.Laying && hb.DateEnd == null)
                    .Select(hb => new HenBatchStatusDTO { Id = hb.Id, HenAmount = hb.HenAmountFemale })
                    .ToList();

                List<Guid> collectedHenBatchIds = new List<Guid>();

                if (storageWarehouseArea == AreaEnum.Classification)
                {
                    // Get collected hen batch ids from material reception reports
                    collectedHenBatchIds = this.materialReceptionReportService.GetAll(true)
                        .Where(mr => mr.Date.Date == DateTime.Today.Date && mr.OriginId != null)
                        .Select(mr => mr.OriginId.Value)
                        .Distinct()
                        .ToList();
                }
                else
                {
                    // Get collected hen batch ids from hen reports
                    collectedHenBatchIds = this.henReportService.GetAllFullForDashboard()
                        .Where(hr => hr.Date == DateTime.Today &&
                                        hr.ReportEnum == ReportEnum.New &&
                                        hr.HenBatch.HenStage == HenStage.Laying &&
                                        hr.ShippingNotes != null &&
                                        hr.HenBatch != null)
                        .Select(hr => hr.HenBatchId)
                        .Distinct()
                        .ToList();
                }

                // Calculate weighted average = ( sum of collected hen batch hen amounts ) / ( sum of hen batch hen amounts )
                decimal numerator = 0;
                decimal denominator = henBatchesStatus.Sum(hb => hb.HenAmount);
                foreach (HenBatchStatusDTO henBatch in henBatchesStatus)
                    numerator += collectedHenBatchIds.Contains(henBatch.Id) ? henBatch.HenAmount : 0;
                card.CardData[0].Value = (numerator / denominator * 100).ToString(DashboardDecimalPrecision.TwoDecimal) + " %";

                //Get today reports
                IQueryable<ClassificationReport> reports = this.classificationReportService.GetAll(true).Where(cr => cr.Date == DateTime.Today);
                card.CardData[1].Value = ((int)(reports.SelectMany(cr => cr.ClassificationSKUs).Sum(csku => csku.Quantity) / eggRelativeValue)).ToString();
            }
            catch
            {
                card.CardData.Last().Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get amount of active and unassigned formulas
        /// </summary>
        private void GetActiveFormulaCardData(DashboardCardsDTO dto)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.ActiveFormulasCardLabel],
                CardEnum = CardsEnum.ActiveFormulas,
                CardData = new List<CardDataDTO>()
            };

            // Active formulas
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.ActiveFormulasLabel];
            // Unassigned formulas
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.UnassignedFormulasLabel];

            // Get data
            try
            {
                IQueryable<Formula> activeFormulas = this.formulaService.GetAllActive();
                card.CardData[0].Value = activeFormulas.Select(f => f.Id).ToList().Count().ToString();
                List<Guid> assignedFormulas = this.henBatchService.GetAll().Where(hb => hb.DateEnd == null && hb.FormulasConsumed.Any()).SelectMany(hb => hb.FormulasConsumed.Select(f => f.FormulaId)).Distinct().ToList();
                card.CardData[1].Value = activeFormulas.Select(f => f.OutputId).ToList().Except(assignedFormulas).Count().ToString();
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get amount of hens in laying, breeding areas.
        /// </summary>
        private void GetHousedHensCardData(DashboardCardsDTO dto, List<HenBatchStatusDTO> henBatches)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.HousedHensCardLabel],
                CardEnum = CardsEnum.HousedHens,
                CardData = new List<CardDataDTO>()
            };

            // Laying
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.HousedHensLayingLabel];
            // Breeding
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.HousedHensBreedingLabel];

            // Get data
            try
            {
                IEnumerable<HenBatchStatusDTO> henbatches = henBatches.Where(hb => hb.DateEnd == null);
                card.CardData[0].Value = henbatches.Where(hb => hb.HenStage == HenStage.Laying).Sum(hb => hb.HenAmountFemale + hb.HenAmountMale).ToString(DashboardDecimalPrecision.NoDecimal);
                card.CardData[1].Value = henbatches.Where(hb => hb.HenStage == HenStage.Breeding).Sum(hb => hb.HenAmountFemale + hb.HenAmountMale).ToString(DashboardDecimalPrecision.NoDecimal);
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get produced and consumed food from yesterday
        /// </summary>
        private void GetFoodBalanceCardData(DashboardCardsDTO dto)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.FoodBalanceCardLabel],
                CardEnum = CardsEnum.FoodBalance,
                CardData = new List<CardDataDTO>()
            };

            // Variables to convert from Kg to tonnes
            CapacityUnit feedIntakeCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Tonnes);
            decimal feedIntakeRelativeValue = Convert.ToDecimal(feedIntakeCapacityUnit.RelativeValue);
            string feedIntakeUnitSymbol = feedIntakeCapacityUnit.Symbol;

            // Produced food
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = string.Format(this.localizer[Lang.ProducedFoodLabel].Value, feedIntakeUnitSymbol);
            // Consumed food
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = string.Format(this.localizer[Lang.ConsumedFoodLabel].Value, feedIntakeUnitSymbol);

            DateTime yesterday = DateTime.Today.AddDays(-1);
            try
            {

                card.CardData[0].Value = (this.feedProductionReportService.GetAll(true)
                                        .Where(fpr => fpr.Date.Date == yesterday)
                                        .Sum(fpr => fpr.Quantity) / feedIntakeRelativeValue)
                                        .ToString(DashboardDecimalPrecision.TwoDecimal);
                card.CardData[1].Value = (this.henReportService.GetAll(true)
                                        .Where(hr => hr.HenBatch.DateEnd == null && hr.Date.Date == yesterday)
                                        .Sum(hr => hr.FeedIntakeFemale) / feedIntakeRelativeValue)
                                        .ToString(DashboardDecimalPrecision.TwoDecimal);
                card.CardData[2].Value = (this.henReportService.GetAll(true)
                                        .Where(hr => hr.HenBatch.DateEnd == null && hr.Date.Date == yesterday)
                                        .Sum(hr => hr.FeedIntakeMale) / feedIntakeRelativeValue)
                                        .ToString(DashboardDecimalPrecision.TwoDecimal);
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get amount of SKUs packed today, yesterday and the average of the past 7 days
        /// </summary>
        private void GetPackedSKUCardData(DashboardCardsDTO dto)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.PackedSKUCardTitle],
                CardEnum = CardsEnum.PackedSKU,
                CardData = new List<CardDataDTO>()
            };

            // Packed todaydata
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.PackedSKUCardDataLabel1];
            // Packed yesterday data
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.PackedSKUCardDataLabel2];
            // Last 7 days packed average data
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.PackedSKUCardDataLabel3];

            try
            {
                // Get packing reports
                DateTime today = DateTime.Today.Date;
                IQueryable<PackingReport> reports = this.packingReportService
                    .GetAll(true)
                    .Where(pr => pr.Date >= today.AddDays(-8) && pr.PackingSKUs != null && pr.PackingSKUs.Any());

                // Make groups
                decimal todayPacking = reports.Where(pr => pr.Date.Date == today).SelectMany(pr => pr.PackingSKUs).Sum(p => p.Quantity);
                decimal yesterdayPacking = reports.Where(pr => pr.Date.Date == today.AddDays(-1)).SelectMany(pr => pr.PackingSKUs).Sum(p => p.Quantity);
                decimal lastWeekPacking = reports.Where(pr => pr.Date.Date < today).SelectMany(pr => pr.PackingSKUs).Sum(p => p.Quantity);

                card.CardData[0].Value = todayPacking.ToString(DashboardDecimalPrecision.NoDecimal) + " " + this.localizer[Lang.PackedSKUCardDataUnitSymbol];

                card.CardData[1].Value = yesterdayPacking.ToString(DashboardDecimalPrecision.NoDecimal) + " " + this.localizer[Lang.PackedSKUCardDataUnitSymbol];

                card.CardData[2].Value = decimal.Round(lastWeekPacking / 7.0M, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero).ToString(DashboardDecimalPrecision.TwoDecimal) + " " + this.localizer[Lang.PackedSKUCardDataUnitSymbol];
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get packed quantities of the 3 most packed SKUs of the past 7 days
        /// </summary>
        private void GetMostPackedSKU(DashboardCardsDTO dto)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.MostPackedSKUCardTitle],
                CardEnum = CardsEnum.MostPackedSKU,
                CardIcon = "",
                CardData = new List<CardDataDTO>()
            };

            // 1st SKU
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.MostPackedSKUCardDataLabel1];
            card.CardData.Last().Value = "--";
            // 2nd SKU
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.MostPackedSKUCardDataLabel2];
            card.CardData.Last().Value = "--";
            // 3rd SKU
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.MostPackedSKUCardDataLabel3];
            card.CardData.Last().Value = "--";

            try
            {
                DateTime today = DateTime.Today.Date;
                List<PackingSKU> lastWeekPacking = this.packingReportService
                    .GetAll(true)
                    .Where(pr => pr.Date >= today.AddDays(-8) && pr.Date < today && pr.PackingSKUs != null && pr.PackingSKUs.Any())
                    .SelectMany(pr => pr.PackingSKUs)
                    .Select(pr => new
                    {
                        pr.SKUId,
                        pr.SKU,
                        pr.Quantity
                    })
                    .ToList()
                    .GroupBy(pr => pr.SKUId)
                    .Select(g => new PackingSKU()
                    {
                        SKU = g.FirstOrDefault().SKU,
                        SKUId = g.FirstOrDefault().SKUId,
                        Quantity = g.Sum(psku => psku.Quantity),
                    })
                    .OrderByDescending(psku => psku.Quantity)
                    .ToList();

                for (int i = 0; lastWeekPacking.Any() && i < 3; i++)
                {
                    PackingSKU packingSKU = lastWeekPacking.FirstOrDefault();
                    card.CardData[i].Value = packingSKU.Quantity.ToString(DashboardDecimalPrecision.NoDecimal) + " " + this.localizer[Lang.MostPackedSKUCardDataUnitSymbol];
                    card.CardData[i].Label += " " + packingSKU.SKU.Name;
                    lastWeekPacking.RemoveAt(0);
                }
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get preparation orders for today: total, prepared and pending
        /// </summary>
        private void GetPreparedOrders(DashboardCardsDTO dto)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.PreparedOrdersCardTitle],
                CardEnum = CardsEnum.PreparedOrders,
                CardData = new List<CardDataDTO>()
            };

            // Total
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.PreparedOrdersCardDataLabel1];
            card.CardData.Last().Value = "--";
            // Prepared
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.PreparedOrdersCardDataLabel2];
            card.CardData.Last().Value = "--";
            // Remaining
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.PreparedOrdersCardDataLabel3];
            card.CardData.Last().Value = "--";

            try
            {
                DateTime today = DateTime.Today.Date;
                IQueryable<OrderPreparationReport> ordersForToday = this.orderPreparationReportService.GetAll(true).Where(op => op.SalesOrder.DeliveryDate.Date == today);
                card.CardData[0].Value = ordersForToday.Count().ToString(DashboardDecimalPrecision.NoDecimal);
                card.CardData[1].Value = ordersForToday.Where(op => op.SalesOrder.OrderStatusEnum >= OrderStatusEnum.Done).Count().ToString(DashboardDecimalPrecision.NoDecimal);
                card.CardData[2].Value = ordersForToday.Where(op => op.SalesOrder.OrderStatusEnum < OrderStatusEnum.Done).Count().ToString(DashboardDecimalPrecision.NoDecimal);
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get the expired tasks from the given area
        /// </summary>
        private void GetExpiredTasksCard(DashboardCardsDTO dto, AreaEnum area)
        {
            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.ExpiredTasksCardTitle],
                CardEnum = CardsEnum.ExpiredTasks,
                CardData = new List<CardDataDTO>()
            };

            // Expired tasks
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.ExpiredTasksCardDataLabel];
            card.CardData.Last().Value = "--";

            try
            {
                List<Guid> userFarmIds = operationContext.GetUserSiteIds() ?? new List<Guid>();

                List<Guid> henbatchId = this.henBatchService.GetAll(true)
                    .Where(w => userFarmIds.Contains(w.FarmId.Value) && w.LineId.HasValue).Select(s => s.Id).ToListAsync().GetAwaiter().GetResult();

                int tasks = this.taskEntityService.GetAll(true)
                    .Where(t => henbatchId.Contains(t.ContainerId) && t.Container.AreaContainers.Any(ac => ac.AreaEnum == area) && t.TaskStatus == TaskStatusEnum.Expired).CountAsync().GetAwaiter().GetResult();

                DateTime today = DateTime.Today.Date;
                // Expired tasks
                card.CardData[0].Value = tasks.ToString();
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Get  high relevance open events up to the next two weeks and the high relevance
        /// tasks which have expired or which are in their alert period
        /// </summary>
        private void GetCriticEventsRowCard(DashboardCardsDTO dto, AreaEnum area)
        {
            DateTime today = DateTime.Today.Date;
            DateTime maxDate = today.AddDays(ChartTimeSpan.DaySpan);

            // High relevance events which are open and start up to the next two weeks
            List<RowCardColumnItemDTO> events = this.happeningService
                .GetOpenFromArea(area: area)
                .Where(h => h.Relevance == RelevanceEnum.High && h.DateStart.Date < maxDate.Date)
                .OrderBy(h => h.DateStart)
                .Select(h => new RowCardColumnItemDTO
                {
                    BoldText = h.DateStart.ToString("d") + " - " + h.Name,
                    LightText = string.IsNullOrEmpty(h.Description) ? this.localizer[Lang.RowCardItemWithNoDescriptionMessage] : h.Description
                })
                .ToList();

            // High relevance tasks which have expired or are pending in alert period
            List<RowCardColumnItemDTO> tasks = this.taskEntityService
                .GetAll(true)
                .Where(t =>
                    t.Container.AreaContainers.Any(ac => ac.AreaEnum == area) &&
                    t.Relevance == RelevanceEnum.High && (t.TaskStatus == TaskStatusEnum.Expired ||
                    (t.TaskStatus == TaskStatusEnum.Pending && t.AlertDate.Date <= today)))
                .OrderBy(t => t.StartDate)
                .Select(t => new RowCardColumnItemDTO
                {
                    BoldText = t.StartDate.ToString("d") + " - " + t.Name,
                    LightText = string.IsNullOrEmpty(t.Description) ? this.localizer[Lang.RowCardItemWithNoDescriptionMessage] : t.Description
                })
                .ToList();

            if (events.Any())
            {
                RowCardDTO criticEventsRowCard = new RowCardDTO() { CardEnum = CardsEnum.CriticEvents, Title = this.localizer[Lang.CriticEventsTitle], Columns = new List<RowCardColumnDTO>(), ColorStyle = "danger" };
                criticEventsRowCard.Columns.Add(new RowCardColumnDTO() { Items = events });
                dto.RowCards.Add(criticEventsRowCard);
            }

            if (tasks.Any())
            {
                RowCardDTO criticTasksRowCard = new RowCardDTO() { CardEnum = CardsEnum.CriticEvents, Title = this.localizer[Lang.CriticTasksTitle], Columns = new List<RowCardColumnDTO>(), ColorStyle = "danger" };
                criticTasksRowCard.Columns.Add(new RowCardColumnDTO() { Items = tasks });
                dto.RowCards.Add(criticTasksRowCard);
            }

        }

        /// <summary>
        /// Percentage of henbatches that are greater than 0,20% of female mortality in the last week
        /// </summary>
        public void GetMortalityWarningCard(DashboardCardsDTO dto, AreaEnum? area, List<HenBatchStatusDTO> henBatches)
        {
            if (area.HasValue)
            {
                switch (area)
                {
                    case AreaEnum.Laying:
                        henBatches = henBatches.Where(hb => hb.HenStage == HenStage.Laying).ToList();
                        break;
                    case AreaEnum.Breeding:
                        henBatches = henBatches.Where(hb => hb.HenStage == HenStage.Breeding).ToList();
                        break;
                }
            }

            decimal mortality = 0;
            int mortalityCountLimit = 0;
            decimal mortalityPercentage = 0;
            decimal percentage = 0;
            int totalWarehouses = 0;
            if (henBatches.Any())
            {
                List<HenBatchStatusDTO> henbatches = henBatches.GroupBy(hb => hb.WarehouseId)
                                           .Select(hw => new HenBatchStatusDTO()
                                           {
                                               GeneticId = hw.Select(g => g.GeneticId).First(),
                                               HenReports = hw.Where(hr => hr.HenReports.Any())
                                                         .Select(hr => hr.HenReports)
                                                         .Select(hr => new HenReportDTO(hr, DashboardCardsName.MortalityWarning))
                                           }).ToList();
                IQueryable<GeneticsParametersReference> geneticsparameters = geneticsParameterService.GetAll();
                foreach (HenBatchStatusDTO hw in henbatches)
                {
                    Guid geneticId = hw.GeneticId;
                    if (hw.HenReports.Sum(hr => hr.HenAmountFemale) != 0)
                    {
                        // weighted average for the batchWeekNumber
                        int batchWeekNumber = (int)hw.HenReports.Sum(r => r.HenAmountFemale * r.HenBatchPerformanceWeekNumber) / hw.HenReports.Sum(r => r.HenAmountFemale);
                        decimal generalMortalityFemaleWeek = geneticsparameters.Where(gp => gp.GeneticsId == geneticId && gp.TimePeriodValue == batchWeekNumber)
                                                                      .Select(gp => gp.GeneralMortalityFemaleWeek)
                                                                      .FirstOrDefault();
                        decimal generalMortalityFemaleDaily = generalMortalityFemaleWeek / 7;
                        decimal henAmoutFemale = (decimal)hw.HenReports.Sum(hr => hr.HenAmountFemale);
                        mortality = (decimal)hw.HenReports.Sum(r => r.DeadFemale) / (decimal)hw.HenReports.Sum(r => r.HenAmountFemale + r.DeadFemale);
                        percentage = decimal.Round(generalMortalityFemaleDaily * 1.2m, 2);
                        mortalityCountLimit = mortality > percentage ? mortalityCountLimit + 1 : mortalityCountLimit;
                    }
                }

                totalWarehouses = henbatches.Count();
                mortalityPercentage = 100 * mortalityCountLimit / totalWarehouses;

                // Initialize card
                CardDTO card = new CardDTO
                {
                    CardTitle = this.localizer[Lang.MortalityCardTitle],
                    CardEnum = CardsEnum.MortalityWarning,
                    CardData = new List<CardDataDTO>()
                };

                card.CardData.Add(new CardDataDTO());
                card.CardData[0].Value = mortalityCountLimit.ToString();
                card.CardData[0].Label = "(" + mortalityPercentage.ToString() + ")%";

                card.CardData.Add(new CardDataDTO());
                card.CardData.Last().Label = this.localizer[Lang.WarehouseLimit];

                // Add card to DTO
                if (mortalityCountLimit != 0)
                    dto.CardList.Add(card);
            }
        }

        private void GetSerologyCards(DashboardCardsDTO dto) => this.serologyBusinessLogic.GetDataForCards(dto);

        /// <summary>
        /// Percentage of henbatches where the last waterconsumption is at least 30% less than the water consumption of the previous henreport
        /// </summary>
        public void GetWaterConsumptionCard(DashboardCardsDTO dto, AreaEnum? area, List<HenBatchStatusDTO> henBatches)
        {
            if (area.HasValue)
            {
                switch (area)
                {
                    case AreaEnum.Laying:
                        henBatches = henBatches.Where(hb => hb.HenStage == HenStage.Laying).ToList();
                        break;
                    case AreaEnum.Breeding:
                        henBatches = henBatches.Where(hb => hb.HenStage == HenStage.Breeding).ToList();
                        break;
                }
            }

            if (henBatches.Any())
            {
                int waterReductionCount = 0;
                List<HenBatchStatusDTO> henbatches = henBatches.GroupBy(hb => hb.WarehouseId)
                                          .Select(hw => new HenBatchStatusDTO()
                                          {
                                              HenReports = hw.Where(hr => hr.HenReports.Any()).Select(hr => hr.HenReports)
                                                        .Select(hr => new HenReportDTO(hr, DashboardCardsName.WaterConsumption))
                                          }).ToList();
                decimal totalHenWarehouses = 0;
                foreach (HenBatchStatusDTO hw in henbatches)
                {
                    if (hw.HenReports.Any(hr => hr.BeforeLastDate.HasValue) && hw.HenReports.Select(r => r.LastDate).FirstOrDefault().Date == hw.HenReports.Where(hr => hr.BeforeLastDate.HasValue).Select(r => r.BeforeLastDate.Value.AddDays(1)).FirstOrDefault().Date)
                    {
                        decimal lastHenReportWC = hw.HenReports.Sum(r => r.WaterConsumptionLastHR);
                        decimal beforeLastHenReportWC = hw.HenReports.Sum(r => r.WaterConsumptionBeforeLastHR);
                        waterReductionCount = lastHenReportWC < beforeLastHenReportWC * 0.7m ? waterReductionCount + 1 : waterReductionCount;
                        totalHenWarehouses = totalHenWarehouses + 1;
                    }
                }

                decimal percentageWaterConsumption = totalHenWarehouses != 0 ? decimal.Round(100 * (decimal)waterReductionCount / totalHenWarehouses, 2) : 0;

                // Initialize card
                CardDTO card = new CardDTO
                {
                    CardTitle = this.localizer[Lang.WaterConsumptionCardTitle],
                    CardEnum = CardsEnum.WaterConsumption,
                    CardData = new List<CardDataDTO>()
                };

                card.CardData.Add(new CardDataDTO());
                card.CardData[0].Value = waterReductionCount.ToString();
                card.CardData[0].Label = "(" + percentageWaterConsumption.ToString() + ")%";

                card.CardData.Add(new CardDataDTO());
                card.CardData.Last().Label = this.localizer[Lang.WarehouseWaterConsumption];

                // Add card to DTO
                if (waterReductionCount != 0)
                    dto.CardList.Add(card);
            }
        }

        /// <summary>
        /// Percentage of henbatches where the last count of eggs is at least 4% less than the count of eggs in the previous henreport
        /// </summary>
        public void GetEggProductionCard(DashboardCardsDTO dto, List<HenBatchStatusDTO> henBatches)
        {
            henBatches = henBatches.Where(hb => hb.HenStage == HenStage.Laying).ToList();

            if (henBatches.Any())
            {
                int eggQuantityCount = 0;
                List<HenBatchStatusDTO> henbatches = henBatches.GroupBy(hb => hb.WarehouseId)
                                          .Select(hw => new HenBatchStatusDTO()
                                          {
                                              HenReports = hw.Where(hr => hr.HenReports.Any()).Select(hr => hr.HenReports)
                                                        .Select(hr => new HenReportDTO(hr, DashboardCardsName.EggProduction))
                                          }).ToList();
                int henWarehouses = 0;
                foreach (HenBatchStatusDTO hw in henbatches)
                {
                    if (hw.HenReports.Any(hr => hr.BeforeLastDate.HasValue) && hw.HenReports.Select(r => r.LastDate).FirstOrDefault() == hw.HenReports.Where(hr => hr.BeforeLastDate.HasValue).Select(r => r.BeforeLastDate.Value.AddDays(1)).FirstOrDefault())
                    {
                        decimal lastHenReportEQ = hw.HenReports.Sum(r => r.EggQuantityLastHR);
                        decimal beforeLastHenReportEQ = hw.HenReports.Sum(r => r.EggQuantityBeforeLastHR);
                        eggQuantityCount = lastHenReportEQ < beforeLastHenReportEQ * 0.96m ? eggQuantityCount + 1 : eggQuantityCount;
                        henWarehouses = henWarehouses + 1;
                    }

                }

                decimal percentageEggQuantity = henWarehouses != 0 ? decimal.Round(100 * (decimal)eggQuantityCount / henWarehouses, 2) : 0;

                // Initialize card
                CardDTO card = new CardDTO
                {
                    CardTitle = this.localizer[Lang.EggProductionCardTitle],
                    CardEnum = CardsEnum.EggProduction,
                    CardData = new List<CardDataDTO>()
                };

                card.CardData.Add(new CardDataDTO());
                card.CardData[0].Value = eggQuantityCount.ToString();
                card.CardData[0].Label = "(" + percentageEggQuantity.ToString() + ")%";

                card.CardData.Add(new CardDataDTO());
                card.CardData.Last().Label = this.localizer[Lang.WarehouseEggQuantity];

                // Add card to DTO
                if (eggQuantityCount != 0)
                    dto.CardList.Add(card);
            }

        }

        /// <summary>
        /// 
        /// </summary>
        private void GetTaskAlertsCard(DashboardCardsDTO dto, AreaEnum? area)
        {

            // Initialize card
            CardDTO card = new CardDTO
            {
                CardTitle = this.localizer[Lang.TaskAlertsCardTitle],
                CardEnum = CardsEnum.TaskAlerts,
                CardData = new List<CardDataDTO>()
            };

            // Expired tasks
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.TaskAlertsCardExpiredLabel];
            card.CardData.Last().Value = "--";

            // Today tasks
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.TaskAlertsCardTodayLabel];
            card.CardData.Last().Value = "--";

            // 7 days tasks
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.TaskAlertsCard7DaysLabel];
            card.CardData.Last().Value = "--";

            // 30 days tasks
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.TaskAlertsCard30DaysLabel];
            card.CardData.Last().Value = "--";

            // 60 days tasks
            card.CardData.Add(new CardDataDTO());
            card.CardData.Last().Label = this.localizer[Lang.TaskAlertsCard60DaysLabel];
            card.CardData.Last().Value = "--";

            try
            {
                List<Guid> userFarmIds = operationContext.GetUserSiteIds() ?? new List<Guid>();

                List<Guid> henbatchId = this.henBatchService.GetAll(true)
                    .Where(w => userFarmIds.Contains(w.FarmId.Value) && w.LineId.HasValue).Select(s => s.Id).ToListAsync().GetAwaiter().GetResult();

                IQueryable<TaskEntity> areasTasks = this.taskEntityService.GetAll(true)
                    .Where(t => henbatchId.Contains(t.ContainerId) && t.Container.AreaContainers.Any(ac => ac.AreaEnum == area));

                // Expired tasks (no matter time span)
                card.CardData[0].Value = areasTasks.Where(t => t.TaskStatus == TaskStatusEnum.Expired).Count().ToString();

                // 2nd DB call to get only tasks from today up to 60 days ahead to make sure we don't bring excesive data
                DateTime today = DateTime.Today.Date;
                List<TaskEntity> tasks = areasTasks.Where(t => t.StartDate.Date >= today.Date && t.StartDate.Date <= today.AddDays(60).Date).ToList();
                // Today tasks
                card.CardData[1].Value = tasks.Where(t => t.StartDate.Date == today.Date).Count().ToString();
                // 7 days tasks
                card.CardData[2].Value = tasks.Where(t => t.StartDate.Date > today.Date && t.StartDate.Date <= today.AddDays(7).Date).Count().ToString();
                // 30 days tasks
                card.CardData[3].Value = tasks.Where(t => t.StartDate.Date > today.AddDays(7).Date && t.StartDate.Date <= today.AddDays(30).Date).Count().ToString();
                // 60 days tasks
                card.CardData[4].Value = tasks.Where(t => t.StartDate.Date > today.AddDays(30).Date && t.StartDate.Date <= today.AddDays(60).Date).Count().ToString();
            }
            catch
            {
                foreach (CardDataDTO data in card.CardData)
                    data.Value = this.localizer[Lang.ErrorMessage];
            }

            // Add card to DTO
            dto.CardList.Add(card);
        }

        /// <summary>
        /// Gets hen warehouses that are close to running out of consumable material for the birds
        /// </summary>
        private void GetRunningOutMaterialRowCard(DashboardCardsDTO dto, AreaEnum? area)
        {
            DateTime pastMonday = DateTime.Today.GetPastSelectedDay(DayOfWeek.Monday);
            DateTime mondayBeforePastMonday = pastMonday.AddDays(-7);
            CapacityUnit feedIntakeCapacityUnit = capacityUnitService.Get(CapacityUnits.Tonnes);
            decimal feedIntakeRelativeValue = Convert.ToDecimal(feedIntakeCapacityUnit.RelativeValue);
            Guid[] warehousesWithActiveHenBatches = area switch
            {
                AreaEnum.Laying => this.henBatchService.GetAll(true).Where(hb => hb.DateEnd == null && hb.HenStage == HenStage.Laying).Select(hb => hb.Line.WarehouseId).ToArray(),
                AreaEnum.Breeding => this.henBatchService.GetAll(true).Where(hb => hb.DateEnd == null && hb.HenStage == HenStage.Breeding).Select(hb => hb.Line.WarehouseId).ToArray(),
                _ => this.henBatchService.GetAll(true).Where(hb => hb.DateEnd == null).Select(hb => hb.Line.WarehouseId).ToArray(),
            };
            WarehouseConsumptionDTO[] henWarehouses = this.henWarehouseService.GetAll(true)
                .Where(hw => hw.Lines.Any(l => l.HenBatches.Any(hb => !hb.DateEnd.HasValue)))
                .Select(hw => new WarehouseConsumptionDTO()
                {
                    Name = hw.DetailedName,
                    Consumption = hw.Consumption,
                    Stock = hw.Stock
                })
                .ToArray();

            RowCardDTO runningOutMaterialRowCard = new RowCardDTO()
            {
                CardEnum = CardsEnum.RunningOutMaterialAlert,
                Title = localizer[Lang.RunningOutMaterialTitle],
                Columns = new List<RowCardColumnDTO>(),
                ColorStyle = "danger"
            };

            List<RowCardColumnItemDTO> runningOutMaterialHenWarehouses = new List<RowCardColumnItemDTO>();

            foreach (WarehouseConsumptionDTO henWarehouse in henWarehouses)
            {
                if (henWarehouse.Stock < henWarehouse.Consumption)
                    runningOutMaterialHenWarehouses.Add(new RowCardColumnItemDTO
                    {
                        BoldText = henWarehouse.Name,
                        LightText =
                        $"{localizer[Lang.RunningOutMaterialCurrentStock]}: {Math.Round(henWarehouse.Stock, 2)} {feedIntakeCapacityUnit.Symbol}"
                    });
            }
            if (runningOutMaterialHenWarehouses.Any())
            {

                int listCount = runningOutMaterialHenWarehouses.Count();

                if (area == AreaEnum.Laying || area == AreaEnum.Breeding)
                {
                    runningOutMaterialRowCard.Columns.Add(new RowCardColumnDTO() { Items = runningOutMaterialHenWarehouses });
                }
                else
                {
                    if (listCount == 1)
                    {
                        runningOutMaterialRowCard.Columns.Add(new RowCardColumnDTO() { Items = runningOutMaterialHenWarehouses });
                    }
                    else if (listCount == 2)
                    {
                        int halfList = (listCount / 2);
                        runningOutMaterialRowCard.Columns.Add(new RowCardColumnDTO() { Items = runningOutMaterialHenWarehouses.GetRange(0, halfList) });
                        runningOutMaterialRowCard.Columns.Add(new RowCardColumnDTO() { Items = runningOutMaterialHenWarehouses.GetRange(halfList, listCount - halfList) });
                    }
                    else
                    {
                        RowCardColumnDTO firstColumn = new RowCardColumnDTO() { Items = new List<RowCardColumnItemDTO>() };
                        RowCardColumnDTO secondColumn = new RowCardColumnDTO() { Items = new List<RowCardColumnItemDTO>() };
                        RowCardColumnDTO thirdColumn = new RowCardColumnDTO() { Items = new List<RowCardColumnItemDTO>() };
                        for (int i = 0; i < listCount; i++)
                        {
                            if (((i + 1) % 3) == 1)
                            {
                                firstColumn.Items.Add(runningOutMaterialHenWarehouses.ElementAt<RowCardColumnItemDTO>(i));

                            }
                            else if (((i + 1) % 3) == 2)
                            {
                                secondColumn.Items.Add(runningOutMaterialHenWarehouses.ElementAt<RowCardColumnItemDTO>(i));
                            }
                            else
                            {
                                thirdColumn.Items.Add(runningOutMaterialHenWarehouses.ElementAt<RowCardColumnItemDTO>(i));
                            }
                        }
                        runningOutMaterialRowCard.Columns.Add(firstColumn);
                        runningOutMaterialRowCard.Columns.Add(secondColumn);
                        runningOutMaterialRowCard.Columns.Add(thirdColumn);
                    }
                }
            }

            dto.RowCards.Add(runningOutMaterialRowCard);
        }

        internal class WarehouseConsumptionDTO
        {
            public string Name { get; set; }
            internal decimal Consumption { get; set; }
            internal decimal Stock { get; set; }
        }

        private DateTime GetNextMondayDate()
        {
            DateTime today = DateTime.Today.Date;
            int dayDifference = today.DayOfWeek - DayOfWeek.Monday;
            return today.AddDays((dayDifference >= 0 ? 7 : 0) - dayDifference);
        }

        #endregion

        #region Home dashboard card helpers
        /// <summary>
        /// Translate an AreaEnum to its corresponding string in Spanish
        /// </summary>
        public string TranslateAreaToSpanish(AreaEnum area)
        {
            return area switch
            {
                AreaEnum.Laying => "Postura",
                AreaEnum.Breeding => "Recría",
                AreaEnum.Classification => "Clasificacion",
                AreaEnum.Dispatch => "Despacho",
                AreaEnum.FeedFactory => "Alimentos",
                AreaEnum.Packing => "Empaque",
                AreaEnum.HealthCare => "Sanidad",
                _ => "",
            };
        }

        /// <summary>
        /// returns a list of areas according to the role 
        /// </summary>
        public List<AreaEnum> GetAuthorizedAreas(List<string> roles)
        {
            List<AreaEnum> authorizedAreas = new List<AreaEnum>();

            if (roles.Any(rol => rol.Contains(AreaEnum.Laying.ToString())))
                authorizedAreas.Add(AreaEnum.Laying);

            if (roles.Any(rol => rol.Contains(AreaEnum.Breeding.ToString())))
                authorizedAreas.Add(AreaEnum.Breeding);

            if (roles.Any(rol => rol.Contains(AreaEnum.Classification.ToString())))
                authorizedAreas.Add(AreaEnum.Classification);

            if (roles.Any(rol => rol.Contains(AreaEnum.Dispatch.ToString())))
                authorizedAreas.Add(AreaEnum.Dispatch);

            if (roles.Any(rol => rol.Contains(AreaEnum.FeedFactory.ToString())))
                authorizedAreas.Add(AreaEnum.FeedFactory);

            if (roles.Any(rol => rol.Contains(AreaEnum.Packing.ToString())))
                authorizedAreas.Add(AreaEnum.Packing);

            if (roles.Any(rol => rol.Contains(AreaEnum.HealthCare.ToString())))
                authorizedAreas.Add(AreaEnum.HealthCare);

            if (roles.Any(rol => rol.Contains(AreaEnum.Maintenance.ToString())))
                authorizedAreas.Add(AreaEnum.HealthCare);

            if (roles.Any(rol => rol.Contains(AreaEnum.Returns.ToString())))
                authorizedAreas.Add(AreaEnum.HealthCare);

            if (!authorizedAreas.Any()) // the roles that does not contains the are in their names are authorized for all the areas
                authorizedAreas = Enum.GetValues(typeof(AreaEnum)).Cast<AreaEnum>().ToList();
            return authorizedAreas;
        }
        #endregion

        #endregion

        #region Charts
        #region Hen stage charts

        /// <summary>
        /// Get male and female distribution in each hen batch and show it's influence on total egg count
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForMaleFemaleDistributionChart(FilterDataDTO filters, HenStage? henStage)
        {
            return this.henStageChartsBusinessLogic.MaleFemaleDistributionChart(filters, henStage);
        }

        /// <summary>
        /// Get deaths and different environment variables
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForMortalityChart(FilterDataDTO filters, HenStage? henStage)
        {
            return this.henStageChartsBusinessLogic.MortalityChart(filters, henStage);
        }

        ///<summary>
        /// Get the active and inactive lines capacity chart.
        ///</summary>
        public DashboardLineOrBarChartDTO GetDataForCapacityChart(HenStage henStage)
        {
            return this.henStageChartsBusinessLogic.LineCapacity(henStage);
        }

        ///<summary>
        /// Build DTO for Laying Dashboard hen genetic distribution bar chart.
        ///</summary>
        public PieChartDTO GetDataForGeneticsChart(HenStage henStage)
        {
            return this.henStageChartsBusinessLogic.HenGeneticDistribution(henStage);
        }

        ///<summary>
        /// Generate hen batch age histogram with 5 custom bins
        ///</summary>
        public DashboardLineOrBarChartDTO GetDataForHenBatchAgeChart(FilterDataDTO data, bool noStage = false)
        {
            return this.henStageChartsBusinessLogic.HenBatchesAge(data, noStage);
        }
        /// <summary>
        /// Build DTO for hen batch performance chart.
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForBatchPerformanceChart(HenStage henStage, DateTime minDate, DateTime maxDate, List<Guid> henBatchIds = null)
        {
            return this.henStageChartsBusinessLogic.HenBatchPerformance(henStage, minDate, maxDate, henBatchIds);
        }

        /// <summary>
        /// Get a DTO with chart configuration and a default data series and get a list of
        /// lists of hen reports for a hen batch so dynamic grouping can be done client side.
        /// Time filtering is server side
        /// </summary>
        public Tuple<List<List<HenBatchStatusDTO>>, DashboardLineOrBarChartDTO> GetDataForPerformanceChart(List<Guid> henBatchIds, DateTime minDate, DateTime maxDate, HenStage? henStage = null)
        {
            return this.henStageChartsBusinessLogic.PerformanceChart(henBatchIds, minDate, maxDate, henStage);
        }

        ///<summary>
        /// Get hen amount for every hen batch, genetic type, egg color and total
        ///</summary>
        public DashboardSunburstChartDTO GetDataForColorAndGeneticHenBatchChart(HenStage henStage)
        {
            return this.henStageChartsBusinessLogic.HenDistribution(henStage);
        }

        ///<summary>
        /// Get data for bird weight chart and uniformity chart
        ///</summary>
        public SampleCageChartsDTO GetDataForSampleCageCharts(FilterDataDTO filters)
        {
            return this.henStageChartsBusinessLogic.SampleCageCharts(filters);
        }

        ///<summary>
        /// Get data for uniformity chart
        ///</summary>
        public DashboardLineOrBarChartDTO GetBreedingCVUniformityChart(FilterDataDTO filters)
        {
            return this.henStageChartsBusinessLogic.GetBreedingCVUniformityChart(filters);
        }

        ///<summary>
        /// Get data for bird weight chart
        ///</summary>
        public DashboardLineOrBarChartDTO GetDataForBreedingBirdWeightChart(FilterDataDTO filters)
        {
            return this.henStageChartsBusinessLogic.GetDataForBreedingBirdWeightChart(filters);
        }
        #endregion
        #endregion



        #region Classification charts
        /// <summary>
        /// Get data for classification chart with or without last week average series
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForClassificationChart(bool withAverage)
        {
            return this.classificationChartsBusinessLogic.Classification(withAverage);
        }

        /// <summary>
        /// Show amount of classification and white and color eggs recolected per day.
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForClassificationPerDayChart(FilterDataDTO filterData)
        {
            return this.classificationChartsBusinessLogic.ClassificationPerDay(filterData);
        }

        ///<summary>
        /// Get classification sunburst chart. Grouping is focused on egg color/type
        ///</summary>
        public DashboardSunburstChartDTO GetDataForClassificationBySourceChart(FilterDataDTO filters)
        {
            return this.classificationChartsBusinessLogic.ClassificationBySourceSunburst(filters);
        }

        public DashboardLineOrBarChartDTO GetDataForClassificationPerformanceChart(DateTime minDate, DateTime maxDate)
        {
            return this.classificationChartsBusinessLogic.ClassificationPerformanceChart(minDate, maxDate);
        }
        #endregion

        #region Feed factory charts
        /// <summary>
        /// Get data for food chart
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForFoodChart()
        {
            return feedFactoryChartsBusinessLogic.GetDataForFoodChart();
        }
        #endregion

        #region Packing charts
        /// <summary>
        /// Get amount of orders to be prepared for the next 15 days
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForCommingOrdersChart()
        {
            return this.packingChartsBusinessLogic.CommingOrders();
        }

        /// <summary>
        /// Show amount of packed SKUs from today, yesterday and the last 7 days
        /// grouped by SKU
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForPackedSKUChart()
        {
            return this.packingChartsBusinessLogic.PackedSKU();
        }

        /// <summary>
        /// Show amount of SKUs packed and the packaging consumption
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForPackedSKUPackagingChart(DateTime minDate, DateTime maxDate, Guid? skuId, Guid? materialId)
        {
            return this.packingChartsBusinessLogic.PackedSKUPackaging(minDate, maxDate, skuId, materialId);
        }
        #endregion

        #region Stock charts
        /// <summary>
        /// Get data for materials stock chart
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForMaterialsStockChart(AreaEnum area)
        {
            return this.stockChartsBusinessLogic.MaterialsStock(area);
        }

        /// <summary>
        /// Get data for containers stock chart
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForContainersStockChart(AreaEnum area)
        {
            return this.stockChartsBusinessLogic.ContainersStock(area);
        }
        #endregion

        #region Laying charts
        /// <summary>
        /// Get data for feed intake and hen day chart
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForHenDayAndFeedIntakeChart(List<Guid> henBatchIds, DateTime minDate, DateTime maxDate)
        {
            return this.layingChartsBusinessLogic.HenDayAndFeedIntake(henBatchIds, minDate, maxDate);
        }
        #endregion

        #region Tracking charts

        /// <summary>
        /// Get material batches, shipping notes and reportId to track batches depending on another report
        /// </summary>
        public (IEnumerable<MaterialBatch> materialBatches, List<ShippingNote> shippingNotes, Guid reportId) GetShippingNotesAndBatchesFromReport(MaterialBatch materialBatch, List<ShippingNote> shippingNotes, bool packing)
        {
            return this.eggsTrackingChartsBusinessLogic.GetShippingNotesAndBatchesFromReport(materialBatch, shippingNotes, packing);
        }

        /// <summary>
        /// Get DTO to draw a snakey chart tracking batches from hen batch to client, passing through all the system's processes 
        /// (Material Reception, Classification, Packing, Order Preparation and Sales Order)
        /// </summary>
        public TrackingDTO GetDataForAllClients(FilterDataDTO dataFilters = null)
        {
            return this.trackingChartsBusinessLogic.GetDataForAllClients(dataFilters, true);
        }

        /// <summary>
        /// Get DTO to draw a snakey chart tracking batches from hen batch to client, relating these two entities directly
        /// </summary>
        public TrackingDTO GetDataWithoutIntermediates(FilterDataDTO dataFilters = null)
        {
            return this.trackingChartsBusinessLogic.GetDataForAllClients(dataFilters, false);
        }

        /// <summary>
        /// Get DTO to draw a snakey chart tracking batches from hen batch to client, passing through all the system's processes 
        /// (Material Reception, Classification, Packing, Order Preparation and Sales Order) but having a report of any stage as special filter 
        /// </summary>
        public TrackingDTO GetDataForReport(FilterDataDTO dataFilters)
        {
            return this.trackingChartsBusinessLogic.GetDataForReport(dataFilters);
        }

        /// <summary>
        /// Get DTO to draw a snakey chart tracking hen batches from bredding to laying
        /// </summary>
        public TrackingDTO GetDataForHenBatch(FilterDataDTO dataFilters)
        {
            return this.trackingChartsBusinessLogic.GetDataForHenBatch(dataFilters);
        }
        #endregion

        #region Return and Disposition
        /// <summary>
        /// Get DTO to draw a column chart showing the percentage of sales order returned grouped by return reason for every month
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForReturnRatioByReasonChart(FilterDataDTO filters)
        {
            return returnAndDispositionChartsBusinessLogic.ReturnRatioByReason(filters);
        }
        #endregion

        #region HenBatch detail

        /// <summary>
        /// Get HenBatch detail chart for temperature and mortality by week
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForHenBatchDetailTemperatureAndMortalityChart(List<Guid> henBatchIds1, List<Guid> henBatchIds2, int? minWeek, int? maxWeek)
        {
            return this.henBatchChartsBusinessLogic.TemperatureAndMortalityPerWeek(henBatchIds1, henBatchIds2, minWeek, maxWeek);
        }

        /// <summary>
        /// Get HenBatch detail chart for weight by week
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForHenBatchDetailWeightChart(List<Guid> henBatchIds1, List<Guid> henBatchIds2, int? minWeek, int? maxWeek)
        {
            return this.henBatchChartsBusinessLogic.WeightPerWeek(henBatchIds1, henBatchIds2, minWeek, maxWeek);
        }

        /// <summary>
        /// Get HenBatch detail chart for weight by week
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForHenBatchDetailComparisonChart(List<Guid> henBatchIds1, List<Guid> henBatchIds2)
        {
            return this.henBatchChartsBusinessLogic.ComparisonPerWeek(henBatchIds1, henBatchIds2);
        }

        /// <summary>
        /// Get options for selects of the comparison chart filters. Enabled options are the ones that do not
        /// contain other options from current filter being searched nor the other ones.
        /// </summary>
        public List<SelectListItem> GetComparisonFilterOptions(List<Guid> selectedFarms, List<Guid> selectedClusters, List<Guid> selectedWarehouses, List<Guid> selectedLines, List<Guid> selectedBatches, List<Guid> genetics, List<Guid> formulas, bool? active, HenStage? henStage, string term, string filterKey)
        {
            return this.henBatchChartsBusinessLogic.GetOptionsForComparisonSelects(selectedFarms, selectedClusters, selectedWarehouses, selectedLines, selectedBatches, genetics, formulas, active, henStage, term, filterKey);
        }

        /// <summary>
        /// Get filtered hen batches given the filter values from the comparison chart filters.
        /// </summary>
        public List<Guid> GetComparisonFilteredHenBatches(List<Guid> selectedFarms, List<Guid> selectedClusters, List<Guid> selectedWarehouses, List<Guid> selectedLines, List<Guid> selectedBatches, List<Guid> genetics, List<Guid> formulas, bool? active, HenStage? henStage)
        {
            return this.henBatchChartsBusinessLogic.GetHenBatchesFromComparisonFilters(selectedFarms, selectedClusters, selectedWarehouses, selectedLines, selectedBatches, genetics, formulas, active, henStage);
        }

        #endregion

        public async Task<ExportResult> ExcelExport(string searchTerm, Dictionary<string, string> data, Guid materialId, string area)
        {

            IQueryable<ShippingNote> materialMovements = (data == null && string.IsNullOrEmpty(area))
                            ? this.shippingNoteBusinessLogic.GetLastWeekMovementsByMaterial(materialId)
                            : this.shippingNoteBusinessLogic.GetAllMovementsByMaterialFiltered(materialId, data, area);

            // If search term is not empty...
            if (!string.IsNullOrEmpty(searchTerm))
            {
                materialMovements.Where(p =>
                    p.Name.Contains(searchTerm)
                );
            }
            List<string> headers = new List<string>()
            {
                localizer[Lang.ExcelColumnDate],
                localizer[Lang.ExcelColumnOrigin],
                localizer[Lang.ExcelColumnDestination],
                localizer[Lang.ExcelColumnIncome],
                localizer[Lang.ExcelColumnEgress],
                localizer[Lang.ExcelColumnCapacityUnit],
                localizer[Lang.ExcelColumnStock],
                localizer[Lang.ExcelColumnIdentifier]
            };

            CultureInfo culture = CultureInfo.CurrentCulture;

            string materials = materialService.GetAll(true).Where(m => m.Id == materialId).Select(m => m.Name).First();

            string fileDownloadName = string.Format(culture, localizer[Lang.ExcelExportFilename], DateTime.Now.ToString("yyyyMMdd"), materials);

            return await new ExcelHelper<ShippingNote>(localizer, configuration).Export(materialMovements.OrderByDescending(sn => sn.Date).AsAsyncQueryable(), EntityToExcelRow, headers, fileDownloadName);


        }
        private List<CellValue> EntityToExcelRow(ShippingNote shippingNote)
        {
            decimal quantity = Math.Round(shippingNote.MaterialsShipped.First().Quantity, 2);
            List<CellValue> cells = new List<CellValue>
            {
                new CellValue(shippingNote.Date,"yyyy/MM/dd"),
                new CellValue(shippingNote.Origin==null?(shippingNote.PersonOrigin == null ? "" : shippingNote.PersonOrigin.Name):shippingNote.Origin.DetailedName),
                new CellValue(shippingNote.Destination == null ? (shippingNote.PersonDestination == null ? "" : shippingNote.PersonDestination.Name) : shippingNote.Destination.DetailedName),
                new CellValue(shippingNote.Origin == null && shippingNote.Destination != null ? quantity : (decimal?)null),
                new CellValue(shippingNote.Origin != null ? quantity : (decimal?)null),
                new CellValue(shippingNote.MaterialsShipped.First().Material.CapacityUnit.Symbol),
                new CellValue(shippingNote.Waybill),
                new CellValue(shippingNote.Identifier)
            };
            return cells;
        }
    }
}