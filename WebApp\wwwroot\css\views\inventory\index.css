#filterContainer {
  padding: 0 0 25px 15px;
}

#filterContainer div > .filterGroup {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

/* cards ------------------------- */

#emptyDataMessage {
  color: red;
}

#stockCardsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

#stockCardsContainer .card {
  align-items: center;
  border-radius: 5px;
  padding: 10px;
  padding-bottom: 5px;
  margin-bottom: 10px;
  width: 228px;
}

#stockCardsContainer .card .material-name {
  text-align: center;
  height: 40px;
  font-weight: 500;
  margin-bottom: 5px;
}

#stockCardsContainer .card .producer-name {
  text-align: center;
  font-size: 0.9em;
  font-style: italic;
  color: #555;
  margin-bottom: 10px;
  font-weight: bold;
}

#stockCardsContainer .card .cardDays {
  font-size: 1.2em;
  font-weight: 500;
  margin-bottom: 10px;
}
