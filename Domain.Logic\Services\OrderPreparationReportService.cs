﻿using Binit.Framework;
using Binit.Framework.Entities;
using Binit.Framework.Helpers;
using Binit.Framework.Helpers.Pdf;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Binit.Shaper.Interfaces.Services;
using DAL.Interfaces;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs.OrderPreparation;
using Domain.Logic.Interfaces;
using Domain.Logic.Services.CoreServices;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using QRCoder;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.Services.OrderPreparationReportService;

namespace Domain.Logic.Services
{
    public class OrderPreparationReportService : SectorDependentService<OrderPreparationReport>, IOrderPreparationReportService
    {
        private readonly RazorViewRender razorViewRender;
        private readonly ISalesOrderService salesOrderService;
        private readonly ITenantService tenantService;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IFileManagerService fileManagerService;
        private readonly IService<IgniteFile> igniteFileService;

        public OrderPreparationReportService(
            IExceptionManager exceptionManager,
            ILogger logger,
            IOperationContext operationContext,
            IUnitOfWork unitOfWork,
            IStringLocalizer<SharedResources> localizer,
            IMediator mediator,
            RazorViewRender razorViewRender,
            ISalesOrderService salesOrderService,

            IAliasExtensionService aliasExtensionService,
            IFarmService farmService,
            ITenantService tenantService,
            IService<TenantConfiguration> tenantConfigurationService,
            IFileManagerService fileManagerService,
            IService<IgniteFile> igniteFileService
            )
            : base(aliasExtensionService, exceptionManager, logger, operationContext, unitOfWork, localizer, mediator, farmService)
        {
            this.razorViewRender = razorViewRender;
            this.salesOrderService = salesOrderService;
            this.tenantService = tenantService;
            this.tenantConfigurationService = tenantConfigurationService;
            this.fileManagerService = fileManagerService;
            this.igniteFileService = igniteFileService;
        }

        ///<summary>
        /// Asynchronously updates an order preparation when a sales order is set to status 'done'.
        ///</summary>
        public async Task UpdateAsync(OrderPreparationReport order, int bulks)
        {
            order.NumberOfBulks = bulks;
            await base.UpdateAsync(order);
        }


        /// <summary>
        /// Gets pdf document for an order preparation with a qr code for each bulk of the order.
        /// </summary>
        public async Task<ExportResult> GetQRAsync(Guid id)
        {
            QRDTO dto = await GetQRDTO(id);

            string html = await razorViewRender.RenderToStringAsync("_QR", dto);

            PdfHelper pdfHelper = new PdfHelper();

            string downloadFileName = $"{localizer[Lang.PdfFileName]}_{dto.Client}_{dto.DeliveryDate:yyyy/MM/dd}.pdf";

            return pdfHelper.Export(html, downloadFileName);
        }

        /// <summary>
        /// Generates an order preparation DTO for use it as model
        /// in a html to export to pdf.
        /// </summary>
        private async Task<QRDTO> GetQRDTO(Guid id)
        {
            QRDTO dto = await salesOrderService.GetAll(true)
                .Where(so => so.Id == id)
                .Select(so => new QRDTO()
                {
                    TenantId = so.TenantId,
                    Order = so.Id,
                    Client = so.Client.Name,
                    CurrentDate = DateTime.Now,
                    DeliveryDate = so.DeliveryDate,
                    DeliveryPoint = so.DeliveryPoint.Name,
                    Pallets = so.SKUs.Select(s =>
                        new PalletDTO()
                        {
                            Name = s.SKU.Name,
                            SKUNumber = s.SKU.SKUNumber,
                            SKUs = s.SKU.Inputs.Select(i =>
                                new SKUDTO()
                                {
                                    Name = i.Material.Name,
                                    Quantity = i.Quantity
                                }).ToList()
                        }).ToList(),
                })
                .FirstAsync();

            for (int i = 0; i < dto.Pallets.Count; i++)
            {
                string qrContent =
                    $"Order: {dto.Order} {Environment.NewLine}" +
                    $"Pallet: {i + 1}/{dto.Pallets.Count} ({dto.Pallets[i].SKUNumber}) {Environment.NewLine}";

                foreach (SKUDTO item in dto.Pallets[i].SKUs)
                    qrContent += $"({item.Name}) ({item.Quantity}) {Environment.NewLine}";

                using QRCodeGenerator qrGenerator = new QRCodeGenerator();
                QRCodeData qrCodeData = qrGenerator.CreateQrCode(qrContent, QRCodeGenerator.ECCLevel.Q);
                QRCode qrCode = new QRCode(qrCodeData);
                dto.Pallets[i].QR = BitmapToBytes(qrCode.GetGraphic(10));
                dto.Logo = File.ReadAllBytes("wwwroot/images/logo-promanager.png");
            }

            await SetTenantData(dto);

            return dto;
        }

        /// <summary>
        /// Converts bitmap images to bytes array.
        /// </summary>
        private static byte[] BitmapToBytes(Bitmap img)
        {
            using MemoryStream stream = new MemoryStream();
            img.Save(stream, System.Drawing.Imaging.ImageFormat.Png);
            return stream.ToArray();
        }

        private async Task SetTenantData(QRDTO dto)
        {
            dto.TenantName = await tenantService.GetAll(true).Where(t => t.Id == dto.TenantId).Select(t => t.Name).FirstAsync();

            TenantConfiguration logo = await tenantConfigurationService.GetAll(true).Where(c => c.TenantId == dto.TenantId && c.TenantConfigurationEnum == TenantConfigurationEnum.Logo).FirstOrDefaultAsync();

            if (logo != null)
            {
                IgniteFile file = await this.igniteFileService.GetAsync(new Guid(logo.Value));
                Microsoft.AspNetCore.Mvc.FileStreamResult image = await this.fileManagerService.Download(file);
                MemoryStream destination = new MemoryStream();
                await image.FileStream.CopyToAsync(destination);
                dto.TenantLogo = destination.ToArray();
            }

            List<TenantConfiguration> cetifications = tenantConfigurationService.GetAll(true).Where(c => c.TenantId == dto.TenantId && c.TenantConfigurationEnum == TenantConfigurationEnum.Certification).ToList();
            dto.Certifications = new List<byte[]>();

            if (cetifications.Any())
            {
                foreach (TenantConfiguration cert in cetifications)
                {
                    IgniteFile file = await this.igniteFileService.GetAsync(new Guid(cert.Value));
                    Microsoft.AspNetCore.Mvc.FileStreamResult image = await this.fileManagerService.Download(file);
                    MemoryStream destination = new MemoryStream();
                    await image.FileStream.CopyToAsync(destination);
                    dto.Certifications.Add(destination.ToArray());
                }
            }
        }

        /// <summary>
        /// Get all preparation order reports with it's relations
        /// </summary>
        public IQueryable<OrderPreparationReport> GetAllFull() =>
            base.GetAll()
            .Include(r => r.SalesOrder).ThenInclude(o => o.Client)
            .Include(r => r.SalesOrder).ThenInclude(o => o.Inputs).ThenInclude(i => i.Material).ThenInclude(m => m.CapacityUnit)
            .Include(r => r.ShippingNotes).ThenInclude(sn => sn.MaterialBatchesShipped).ThenInclude(mb => mb.MaterialBatch);

        /// <summary>
        /// Get all preparation order reports with its shipping notes and material batches included
        /// </summary>
        public IQueryable<OrderPreparationReport> GetAllWithSNAndMB() =>
             base.GetAll()
                .Include(op => op.ShippingNotes).ThenInclude(sn => sn.MaterialBatchesShipped).ThenInclude(mbs => mbs.MaterialBatch).ThenInclude(mb => mb.MaterialBatchContainers)
                .Include(op => op.ShippingNotes).ThenInclude(sn => sn.MaterialsShipped).ThenInclude(mbs => mbs.Material);
    }
}
