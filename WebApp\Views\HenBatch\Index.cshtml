﻿@using Binit.Framework;
@using Binit.Framework.Constants.SeedEntities;
@using Binit.Framework.Interfaces.DAL;
@using Binit.Framework.Interfaces.Configuration;
@using Binit.Framework.Constants.Authentication;
@using Microsoft.Extensions.Localization;
@using WebApp.WebTools.Tree;
@using WebApp.WebTools;
@using System.Globalization;
@using Domain.Entities.Model;
@using Binit.Framework.Helpers;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.HenBatch.Index;
@using RowLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Models.HenBatchRow;
@inject IStringLocalizer<SharedResources> localizer;
@inject IOperationContext operationContext;
@inject ISolutionConfiguration solutionConfiguration;

@{
    var dateFormatString = CultureInfo.CurrentCulture.DateTimeFormat.ShortDatePattern;
    string lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
    List<SelectListItem> genetics = ViewData["Genetics"] as List<SelectListItem>;
    List<SelectListItem> henStages = ViewData["HenStages"] as List<SelectListItem>;
    string validationError = ViewData["ValidationError"] as string;
    string henStage = ViewData["HenStage"] as string;
    HenStage? henStageEnum = string.IsNullOrEmpty(henStage) ? (HenStage?)null : EnumHelper<HenStage>.Parse(henStage);
    List<SelectListItem> farms = ViewData["Farms"] as List<SelectListItem>;
    List<SelectListItem> clusters = ViewData["Clusters"] as List<SelectListItem>;
    List<SelectListItem> warehouses = ViewData["Warehouses"] as List<SelectListItem>;
    List<SelectListItem> lines = ViewData["Lines"] as List<SelectListItem>;
    List<SelectListItem> formulas = ViewData["Formulas"] as List<SelectListItem>;
    bool moreThanOneFarm = farms.Count() > 1;
    var hasClusters = (bool)ViewData["HasClusters"];
    var hasSectors = (bool)ViewData["HasSectors"];
    bool moreThanOneCluster = clusters.Count() > 1 && hasClusters;
    bool moreThanOneHenWarehouse = warehouses.Count() > 1;
    bool moreThanOneLine = lines.Count() > 1;
    string noPlaceAvailable = ViewData["NoPlaceAvailable"] as string;
    var hasHenBatchCategory = (bool)ViewData["hasHenBatchCategory"];
    string url = ViewData["URL"] as string;
    string tableId = ViewData["TableId"] as string;

    bool isDevelopment = solutionConfiguration.Development;

    bool Authorize(HenStage? henStage, bool onlyAdministrators)
    {
        if (henStage.HasValue)
        {
            List<string>
    roles = new List<string>
        ();

            switch (henStage)
            {
                case HenStage.Laying:
                    roles.AddRange(new string[] { Roles.BackofficeSuperAdministrator, Roles.BackofficeLayingAdministrator, Roles.BackofficeLayingHenBatchAdministrator });
                    if (!onlyAdministrators) roles.AddRange(new string[] { Roles.BackofficeLayingUser, Roles.BackofficeLayingHenBatchUser, Roles.BackofficeLayingBirdMovement });
                    return operationContext.UserIsInAnyRole(roles.ToArray());

                case HenStage.Breeding:
                    roles.AddRange(new string[] { Roles.BackofficeSuperAdministrator, Roles.BackofficeBreedingAdministrator, Roles.BackofficeBreedingHenBatchAdministrator });
                    if (!onlyAdministrators) roles.AddRange(new string[] { Roles.BackofficeBreedingUser, Roles.BackofficeBreedingHenBatchUser, Roles.BackofficeBreedingBirdMovement });
                    return operationContext.UserIsInAnyRole(roles.ToArray());

                default:
                    return operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator);
            };
        }
        else
        {
            return operationContext.UserIsInAnyRole(
            Roles.BackofficeSuperAdministrator,
            Roles.BackofficeLayingAdministrator,
            Roles.BackofficeLayingHenBatchAdministrator,
            Roles.BackofficeBreedingAdministrator,
            Roles.BackofficeBreedingHenBatchAdministrator);
        }
    };

    bool CheckAuthorizationToSetFirstProductionDate(HenStage? henStage)
    {
        if (henStage == HenStage.Laying && this.operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeHenBatchFirstProductionDate))
            return true;
        return false;
    }

}

@section ViewStyles{
    <style>
        tr.dx-row.dx-data-row td.center-cell-content-vertically {
            vertical-align: middle;
        }
    </style>
}

@{
    @if (operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
   Roles.BackofficeLayingAdministrator, Roles.BackofficeLayingUser,
   Roles.BackofficeLayingHenBatchAdministrator, Roles.BackofficeBreedingAdministrator,
   Roles.BackofficeBreedingUser, Roles.BackofficeBreedingHenBatchAdministrator))
    {
        if (henStage != "" && henStage != null)
        {
            <div class="d-flex justify-content-end m-b-40" style="align-items: center">
                <button type="button" class="btn btn-themecolor mr-2" id="createHenBatchButton"
                        style="margin:5px">
                    <i class="fa fa-plus"></i>
                    @(localizer[Lang.BtnNew])
                </button>
            </div>
        }
        else
        {
            <div class="d-flex justify-content-end m-b-40" style="align-items: center">
                <button type="button" class="btn btn-themecolor mr-2" onclick="location.href='@Url.Action("Create", "HenBatch", new { henStage = "Breeding" })'"
                        style="margin:5px">
                    <i class="fa fa-plus"></i>
                    @(localizer[Lang.BtnNewBreeding])
                </button>
                <button type="button" class="btn btn-themecolor mr-2" onclick="location.href='@Url.Action("Create", "HenBatch", new { henStage = "Laying" })'"
                        style="margin:5px">
                    <i class="fa fa-plus"></i>
                    @(localizer[Lang.BtnNewLaying])
                </button>
            </div>

        }
    }
}


<div style="display:flex; justify-content:flex-start;height: 4.3rem;align-items: center;">
    @if (henStage == "" || henStage == null)
    {
        <div class="form-group filter-input">
            <label for="henStage">
                @(localizer[Lang.HenStageSelectLabel])
            </label>
            <select class="form-control select2" id="henStage">
                <option value="">
                    @(localizer[Lang.HenStageSelectAllOption])
                </option>
                @foreach (var item in henStages)
                {
                    <option henStage value="@item.Value">@item.Text</option>
                }
            </select>
        </div>
    }

    <div class="form-group filter-input" id="FarmDiv" style="@(moreThanOneFarm ? "" : "display:none")">
        <label for="farm">
            @(localizer[Lang.FarmLabel])
        </label>
        <select class="form-control select2" id="farm">
            <option value="">
                @(localizer[Lang.FarmAllOption])
            </option>
            @foreach (var item in farms)
            {
                <option farm value=@item.Value>@item.Text</option>
            }
        </select>
    </div>
    @if (hasClusters)
    {
        <div class="form-group filter-input" id="ClusterDiv" style="@(moreThanOneCluster && !moreThanOneFarm ? "" : "display:none")">
            <label for="cluster">
                @(localizer[Lang.ClusterLabel])
            </label>
            <select class="form-control select2" id="cluster">
                <option value="">
                    @(localizer[Lang.ClusterAllOption])
                </option>
                @foreach (var item in clusters)
                {
                    <option cluster value=@item.Value>@item.Text</option>
                }
            </select>
        </div>
    }

    <div class="form-group filter-input" id="WHDiv" style="@(moreThanOneHenWarehouse && ( !moreThanOneCluster && !moreThanOneFarm) ? "" : "display:none")">
        <label for="warehouse">
            @(localizer[Lang.WarehouseLabel])
        </label>
        <select class="form-control select2" id="warehouse">
            <option value="">
                @(localizer[Lang.WarehouseAllOption])
            </option>
            @foreach (var item in warehouses)
            {
                <option warehouse value=@item.Value>@item.Text</option>
            }
        </select>
    </div>

    <div class="form-group filter-input" id="LineDiv" style="@(moreThanOneLine && (!moreThanOneHenWarehouse && !moreThanOneCluster && !moreThanOneFarm) ? "" : "display:none")">
        <label for="line">
            @(localizer[Lang.LineLabel])
        </label>
        <select class="form-control select2" id="line">
            <option value="">
                @(localizer[Lang.LineAllOption])
            </option>
            @foreach (var item in lines)
            {
                <option line value=@item.Value>@item.Text</option>
            }
        </select>
    </div>

    <div class="form-group filter-input">
        <label for="estado">@(localizer[Lang.StatusSelectLabel])</label>
        <select class="form-control select2" id="status">
            <option value="@HenBatchStatus.Open" selected="selected">@(localizer[Lang.StatusSelectActiveOption])</option>
            <option value="@HenBatchStatus.Closed">@(localizer[Lang.StatusSelectClosedOption])</option>
            <option value="">@(localizer[Lang.StatusSelectAllOption])</option>
        </select>
    </div>

    <div class="form-group filter-input">
        <label for="formula">
            @(localizer[Lang.FormulaSelectLabel])
        </label>
        <select class="form-control select2" id="formula">
            <option value="">
                @(localizer[Lang.FormulaSelectAllOption])
            </option>
            @foreach (var item in formulas)
            {
                <option value="@item.Value">@item.Text</option>
            }
        </select>
    </div>
    <div>
        <button id="btnFilter"
                class="btn waves-effect waves-light btn-dark ml-2 btn-sm datatable-action-button" style="height:fit-content; margin-bottom:2px">
            @(localizer[Lang.Filter])
        </button>
    </div>
</div>

<div id="dateFilters" class="row floating-labels" hidden>
    <div class="col-md-5">
        <div class="row">
            <div class="col-md-12 mt-3">
                <label>@(localizer[Lang.DateStartHenBatch])</label>
            </div>
        </div>
        <div class="row mt-5">
            <div class="col-md-5">
                <ignite-datetime-picker id="start-date-start"
                                        label="@localizer[Lang.DateStartLabel]">
                </ignite-datetime-picker>
            </div>

            <div class="col-md-5">
                <ignite-datetime-picker id="start-date-end"
                                        label="@localizer[Lang.DateEndLabel]">
                </ignite-datetime-picker>
            </div>

            <div>
                <button id="CreatedBtnEmpty"
                        class="btn waves-effect waves-light btn-dark btn-sm datatable-action-button">
                    @(localizer[Lang.BtnEmpty])
                </button>
            </div>

        </div>
    </div>

    <div class="col-md-5 ml-1">
        <div class="row">
            <div class="col-md-12 mt-3">
                <label>@(localizer[Lang.DateEndHenBatch])</label>
            </div>
        </div>
        <div class="row mt-5">
            <div class="col-md-5">
                <ignite-datetime-picker id="end-date-start"
                                        label="@localizer[Lang.DateStartLabel]">
                </ignite-datetime-picker>
            </div>
            <div class="col-md-5">
                <ignite-datetime-picker id="end-date-end"
                                        label="@localizer[Lang.DateEndLabel]">
                </ignite-datetime-picker>
            </div>

            <div>
                <button id="EditedBtnEmpty"
                        class="btn waves-effect waves-light btn-dark btn-sm datatable-action-button">
                    @(localizer[Lang.BtnEmpty])
                </button>
            </div>
        </div>
    </div>
</div>

<a id="export" class="btn btn-primary excel mr-2">
    <i class="fa fa-file-excel m-l-5"></i>
    @(localizer[Lang.BtnExportAll])
</a>

@(
Html.DevExtreme().TreeList<HierarchicalItem>()
.ID(tableId)
.DataSource(ds => ds.Mvc()
    .Controller("HenBatch")
    .LoadAction("GetTree")
    .Key("id")
    .OnBeforeSend("onBeforeSend")
)
.RootValue("")
.KeyExpr("id")
.ParentIdExpr("parentId")
.OnCellPrepared("dxCellTemplates.onCellPrepared")
.Sorting(sorting => sorting.Mode(GridSortingMode.Multiple))
.Columns(columns =>
{
    columns.Add()
           .DataField("farmCode")
           .Caption(localizer[Lang.TableColFarmCode])
           .CssClass("center-cell-content-vertically")
            .SortOrder(SortOrder.Asc).SortIndex(0);
    columns.Add()
            .DataField("farmName")
            .Caption(localizer[Lang.TableColFarmName])
            .CssClass("center-cell-content-vertically")
             .SortOrder(SortOrder.Asc).SortIndex(1);
    columns.Add()
           .DataField("warehouseCode")
           .Caption(localizer[Lang.TableColHenWarehouseCode])
           .CssClass("center-cell-content-vertically")
            .SortOrder(SortOrder.Asc).SortIndex(2);
    columns.Add()
           .DataField("lineCode")
           .Caption(localizer[Lang.TableColLineCode])
           .CssClass("center-cell-content-vertically")
            .SortOrder(SortOrder.Asc).SortIndex(3);
    columns.Add()
           .DataField("code")
           .Caption(localizer[Lang.TableColCode])
           .CssClass("center-cell-content-vertically");
    columns.Add()
            .DataField("name")
            .Caption(localizer[Lang.TableColName])
            .CssClass("center-cell-content-vertically")
            .Visible(false);
    columns.Add()
            .DataField("batchWeekNumber")
            .Caption(localizer[Lang.TableColWeek])
            .CssClass("center-cell-content-vertically");
    columns.Add()
            .DataField("batchStage")
            .Caption(localizer[Lang.TableColBatchStage])
            .CssClass("center-cell-content-vertically");
    columns.Add()
            .DataField("genetic")
            .Caption(localizer[Lang.TableColGenetic])
            .CssClass("center-cell-content-vertically");
    columns.Add()
            .DataField("dateStart")
            .Caption(localizer[Lang.TableColDateStart])
            .DataType(GridColumnDataType.DateTime).Format("dd/MM/yyyy")
            .CssClass("center-cell-content-vertically")
            .SortOrder(SortOrder.Desc).SortIndex(3);
    columns.Add()
            .DataField("dateEnd")
            .Caption(localizer[Lang.TableColDateEnd])
            .DataType(GridColumnDataType.DateTime).Format("dd/MM/yyyy")
            .CssClass("center-cell-content-vertically")
            .Visible(false);
    columns.Add()
            .DataField("henAmount")
            .Caption(localizer[Lang.TableColAmount])
            .CssClass("center-cell-content-vertically");
    columns.Add()
            .DataField("femalesOnFirstProductionDate")
            .Caption(localizer[Lang.TableColFemalesOnFirstProductionDate])
            .CssClass("center-cell-content-vertically");
    columns.Add()
            .DataField("femaleHenAmount")
            .Caption(localizer[Lang.TableColFemaleAmount])
            .CssClass("center-cell-content-vertically")
            .Visible(false);
    columns.Add()
            .DataField("maleHenAmount")
            .Caption(localizer[Lang.TableColMaleAmount])
            .CssClass("center-cell-content-vertically")
            .Visible(false);
    columns.Add()
            .DataField("initialHenAmount")
            .Caption(localizer[Lang.TableColOriginalAmount])
            .CssClass("center-cell-content-vertically");
    columns.Add()
            .DataField("femaleInitialHenAmount")
            .Caption(localizer[Lang.TableColOriginalFemaleAmount])
            .CssClass("center-cell-content-vertically")
            .Visible(false);
    columns.Add()
            .DataField("maleInitialHenAmount")
            .Caption(localizer[Lang.TableColOriginalMaleAmount])
            .CssClass("center-cell-content-vertically")
            .Visible(false);
    columns.Add()
            .DataField("formulas")
            .Caption(localizer[Lang.TableColFormulaConsumed])
            .CellTemplate(new JS("customListForFormulas"))
            .AllowFiltering(false)
            .Width(200)
            .Visible(false);
    if(henStageEnum == HenStage.Laying)
    {
        columns.Add()
            .DataField("firstProductionDate")
            .Caption(localizer[Lang.FirstProductionDate])
            .CssClass("center-cell-content-vertically")
            .AllowFiltering(false)
            .Visible(false);
        columns.Add()
            .DataField("capitalizationDate")
            .Caption(localizer[Lang.CapitalizationDate])
            .CssClass("center-cell-content-vertically")
            .AllowFiltering(false)
            .Visible(false);
        columns.Add()
           .DataField("startDateOfWeekOne")
           .Caption(localizer[Lang.TableColStartDateOfWeekOne])
           .DataType(GridColumnDataType.DateTime).Format("dd/MM/yyyy")
           .CssClass("center-cell-content-vertically")
           .AllowFiltering(false)
           .Visible(true);
    }
    if (hasHenBatchCategory)
    {
        columns.Add()
            .DataField("category")
            .Caption(localizer[Lang.HenBatchCategory])
            .CssClass("center-cell-content-vertically")
            .AllowFiltering(false)
            .Visible(false);
    }
    columns.Add()
    .Fixed(true)
    .FixedPosition(HorizontalEdge.Right)
    .Caption(localizer[Lang.TableColActions])
    .Type(TreeListCommandColumnType.Buttons)
    .CssClass("flex-column-button-cell")
    .HidingPriority(1)
    .MinWidth(50)
    .CellTemplate(new JS("dxDropdownActionButtonCellTemplate.renderCell"))
    .Buttons(buttons =>
    {
        if (Authorize(henStageEnum, true))
        {
            if (operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                Roles.BackofficeLayingAdministrator, Roles.BackofficeLayingUser,
                Roles.BackofficeLayingHenBatchAdministrator, Roles.BackofficeBreedingAdministrator,
                Roles.BackofficeBreedingUser, Roles.BackofficeBreedingHenBatchAdministrator,
                Roles.BackofficeBreedingBirdMovement, Roles.BackofficeLayingBirdMovement) )
            {
                buttons.Add(new DataTableRedirectAction()
                {
                    InternalName = "edit",
                    Icon = " fa fa-edit",
                    Class = "btn-primary",
                    Url = $"/HenBatch/Edit/{{id}}",
                    ShowDisplayName = false,
                    DisplayName = localizer[RowLang.EditHenBatch],
                },
                "isOpenParent");

                buttons.Add(new DataTableRedirectAction()
                {
                    InternalName = "distribute-birds",
                    Class = "btn-themecolor",
                    Icon = " fa fa-dove",
                    Url = $"/MaterialDistributionReport/CreateBirdsDistribution?henStage={{henStage}}&henBatchId={{id}}",
                    ShowDisplayName = false,
                    DisplayName = localizer[RowLang.DistributeBirds],
                },
                "canDistributeWithAndWithoutChildren");

                if (operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                    Roles.BackofficeLayingAdministrator, Roles.BackofficeLayingUser,
                    Roles.BackofficeLayingHenBatchAdministrator, Roles.BackofficeBreedingAdministrator,
                    Roles.BackofficeBreedingUser, Roles.BackofficeBreedingHenBatchAdministrator, Roles.BackofficeLayingBirdMovement,
                    Roles.BackofficeBreedingBirdMovement,
                    Roles.BackofficeLayingBirdMovementAdjustmentApprover,
                    Roles.BackofficeBreedingBirdMovementAdjustmentApprover))
                {
                    buttons.Add(new DataTableRedirectAction()
                    {
                        InternalName = "redistribute",
                        Class = "btn-themecolor",
                        Icon = " fa fa-exchange-alt",
                        Url = $"/HenBatch/Redistribute?id={{id}}&henStage={{henStage}}",
                        ShowDisplayName = false,
                        DisplayName = localizer[RowLang.MoveBirds],
                    },
                    "canDistribute");
                }

                buttons.Add(new DataTableAction()
                {
                    InternalName = "close",
                    Class = "btn-red",
                    Icon = " fa fa-lock-open",
                    Url = $"/HenBatch/Close/{{id}}",
                    ShowDisplayName = false,
                    DisplayName = localizer[RowLang.CloseHenBatch],
                    SuccessTitle = $"{{name}}"
                },
                "isOpenParent",
                "handleCloseAction");

                buttons.Add(new DataTableDecisionAction()
                {
                    InternalName = "delete",
                    Class = "btn-danger",
                    Icon = " fa fa-trash",
                    Modal = new SweetAlert(localizer)
                    {
                        Message = string.Format(localizer[RowLang.DeleteQuestionMessage], $"{{name}}"),
                        TypeOfAlert = "warning"
                    },
                    Url = $"/HenBatch/Delete/{{id}}",
                    SuccessTitle = localizer[RowLang.DeleteConfirmationMessage],
                    ShowDisplayName = false,
                    DisplayName = localizer[RowLang.Delete]
                },
                "canBeDeleted",
                "deleteAction");
            }
        }
        else
        {
            if (operationContext.UserIsInAnyRole(Roles.BackofficeLayingUser, Roles.BackofficeBreedingUser,
                    Roles.BackofficeLayingHenBatchAdministrator, Roles.BackofficeBreedingHenBatchAdministrator,
                    Roles.BackofficeLayingBirdMovement, Roles.BackofficeBreedingBirdMovement,
                    Roles.BackofficeLayingBirdMovementAdjustmentApprover, Roles.BackofficeBreedingBirdMovementAdjustmentApprover))
            {
                buttons.Add(new DataTableRedirectAction()
                {
                    InternalName = "redistribute",
                    Class = "btn-themecolor",
                    Icon = " fa fa-exchange-alt",
                    Url = $"/HenBatch/Redistribute?id={{id}}&henStage={{henStage}}",
                    ShowDisplayName = false,
                    DisplayName = localizer[RowLang.MoveBirds],
                },
                "canDistribute");
            }
        }

        if (Authorize(henStageEnum, false))
        {

            if (henStageEnum.HasValue && henStageEnum == HenStage.Laying) {
                if (!operationContext.UserIsInAnyRole(Roles.BackofficeLayingBirdMovement, Roles.BackofficeBreedingBirdMovement,
                                                  Roles.BackofficeBreedingHenBatchUser, Roles.BackofficeLayingHenBatchUser))
                {
                    buttons.Add(new DataTableRedirectAction()
                    {
                        InternalName = "spiking-birds",
                        Class = "btn-themecolor",
                        Icon = " fa fa-mars-double",
                        Url = $"/MaterialReceptionReport/Create?area={{henStage}}&containerId={{id}}&referURL={url}&spickingReport=true",
                        ShowDisplayName = false,
                        DisplayName = localizer[RowLang.SpikingBirds],
                        SuccessTitle = $"{{id}}"
                    }, "canReceiveSpiking");
                }

            }
            if (operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                Roles.BackofficeLayingAdministrator, Roles.BackofficeLayingUser,
                Roles.BackofficeLayingHenBatchAdministrator, Roles.BackofficeBreedingAdministrator,
                Roles.BackofficeBreedingUser, Roles.BackofficeBreedingHenBatchAdministrator, Roles.BackofficeLayingBirdMovement,
                Roles.BackofficeBreedingBirdMovement))
            {
                buttons.Add(new DataTableRedirectAction()
                {
                    InternalName = "move-birds",
                    Class = "btn-themecolor",
                    Icon = " fa fa-exchange-alt",
                    Url = $"/HenBatch/MoveBirds?henStage={{henStage}}&originId={{id}}",
                    ShowDisplayName = false,
                    DisplayName = localizer[RowLang.MoveBirds],
                    SuccessTitle = $"{{id}}"
                },
                "canMoveBirds");
            }

            if (!operationContext.UserIsInAnyRole(Roles.BackofficeBreedingBirdMovement,
                                                  Roles.BackofficeBreedingHenBatchUser, Roles.BackofficeLayingHenBatchUser))
            {
                if (henStageEnum == HenStage.Laying)
                {
                    buttons.Add(new DataTableRedirectAction()
                    {
                        InternalName = "send-birds",
                        Class = "btn-outline-themecolor themecolor",
                        Icon = " fa fa-dove",
                        Url = $"/MaterialExitReport/Create?area={{henStage}}&originId={{id}}&referURL={url}",
                        ShowDisplayName = false,
                        DisplayName = localizer[RowLang.SendBirds],
                        SuccessTitle = $"{{id}}"
                    },
                    "isParentWithoutChildren");

                    buttons.Add(new DataTableRedirectAction()
                    {
                        InternalName = "distribute-send-birds",
                        Class = "btn-themecolor",
                        Icon = " fa fa-dove",
                        Url = $"/MaterialDistributionReport/SendBirdsDistribution?henBatchId={{id}}&henStage={{henStage}}",
                        ShowDisplayName = false,
                        DisplayName = localizer[RowLang.SendDistributionBirds],
                    },
                    "isParentWithChildren");
                } else if (henStageEnum == HenStage.Breeding) {
                buttons.Add(new DataTableRedirectAction()
                {
                    InternalName = "distribute-send-birds",
                    Class = "btn-themecolor",
                    Icon = " fa fa-dove",
                    Url = $"/MaterialDistributionReport/SendBirdsDistribution?henBatchId={{id}}&henStage={{henStage}}",
                    ShowDisplayName = false,
                    DisplayName = localizer[RowLang.SendDistributionBirds],
                });
                }
            }

            buttons.Add(new DataTableRedirectAction()
            {
                InternalName = "closed",
                Class = "btn-red",
                Icon = " fa fa-lock",
                Url = $"/HenBatch/Details/{{id}}",
                ShowDisplayName = false,
                DisplayName = localizer[RowLang.ClosedHenBatch]
            },
            "isClosed");
            if (operationContext.UserIsInAnyRole(Roles.BackofficeLayingAdministrator, Roles.BackofficeBreedingAdministrator, Roles.BackofficeLayingUser, Roles.BackofficeBreedingUser,
                                                Roles.BackofficeBreedingHenBatchUser, Roles.BackofficeLayingHenBatchUser) )
            {
                buttons.Add(new DataTableRedirectAction()
                {
                    InternalName = "details",
                    Icon = " fa fa-eye",
                    Class = "btn-success",
                    Url = $"/HenBatch/Details/{{id}}",
                    ShowDisplayName = false,
                    DisplayName = localizer[RowLang.Details]
                }, "isParent");
            }

        }

        if (operationContext.UserIsInAnyRole(
                    Roles.BackofficeSuperAdministrator,
                    Roles.BackofficeBreedingAdministrator,
                    Roles.BackofficeLayingAdministrator,
                    Roles.BackofficeHealthCareAdministrator,
                    Roles.BackofficeHealthCareTaskAdministrator,
                    Roles.BackofficeHealthCareTaskUser))
        {
            buttons.Add(new DataTableRedirectAction()
            {
                InternalName = "task",
                Icon = " fa fa-calendar",
                Class = "btn-primary",
                Url = $"/Task/Manager/{{id}}",
                ShowDisplayName = false,
                DisplayName = localizer[RowLang.HenBatchTask],
            },
            "isOpenParent");
        }
        if (operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeSampleCageAdministrator)) {
            buttons.Add(new DataTableRedirectAction()
            {
                InternalName = "sampleCages",
                Icon = " fa fa-cubes",
                Class = "btn-primary",
                Url = $"/SampleCage/Create?henBatchId={{id}}",
                ShowDisplayName = false,
                DisplayName = localizer[RowLang.SampleCageForm],
            },
            "isActive");
        }

        if (isDevelopment)
        {
            buttons.Add(new DataTableRedirectAction()
            {
                InternalName = "trackingBirds",
                Icon = " fa fa-share-alt",
                Class = "btn-primary",
                Url = $"/Tracking/Report?id={{id}}",
                DisplayName = localizer[RowLang.TrackingBirdsTooltip]
            },
            "hasChildActions");

            buttons.Add(new DataTableRedirectAction()
            {
                InternalName = "trackingFood",
                Icon = " fa fa-share-alt",
                Class = "btn-primary",
                Url = $"/Tracking/Report?id={{id}}",
                DisplayName = localizer[RowLang.TrackingFoodTooltip]
            },
            "hasChildActions");
        }
        if(CheckAuthorizationToSetFirstProductionDate(henStageEnum))
        {
            buttons.Add(new DataTableRedirectAction()
            {
                InternalName = "firstProductionDate",
                Icon = "fas fa-calendar",
                Class = "btn-primary",
                Url = $"/HenBatch/FirstProductionDate/{{id}}",
                ShowDisplayName = false,
                Type = "GET",
                DisplayName = localizer[RowLang.SetFirstProductionDate]
            },
            "isOpenParent");
        }
        if (!operationContext.UserIsInAnyRole(Roles.BackofficeLayingBirdMovement, Roles.BackofficeBreedingBirdMovement,
                                                  Roles.BackofficeBreedingHenBatchUser, Roles.BackofficeLayingHenBatchUser))
        {
            buttons.Add(new DataTableRedirectAction()
            {
                InternalName = "birdsReceptions",
                Icon = " fa fa-dove",
                Class = "btn-primary",
                Url = $"/ShippingNote/Index?area={{henStage}}&entityId={{id}}&onlyReception=true",
                ShowDisplayName = false,
                DisplayName = localizer[RowLang.BirdsReceptions]
            }, "isOpenWithChildActions");
        }

    });
})
.AllowColumnReordering(true)
.HeaderFilter(headerFilter => headerFilter.Visible(true))
.Editing(editing => editing
    .AllowAdding(false)
)
.AutoExpandAll(false)
.SearchPanel(seachPanel => seachPanel.Visible(true))
.HeaderFilter(headerFilter => headerFilter.Visible(true))
.FilterRow(filterRow => filterRow
    .Visible(true)
    .ApplyFilter(GridApplyFilterMode.Auto)
)
.ShowRowLines(true)
.ShowBorders(true)
.ColumnChooser(c => c.Enabled(true).Mode(GridColumnChooserMode.Select))
.Selection(c => c.Mode(SelectionMode.Single))
.RowAlternationEnabled(true)
.ColumnHidingEnabled(false)
.WordWrapEnabled(true)
.ColumnAutoWidth(true)
.ColumnFixing(cf => cf.Enabled(true))
.FilterPanel(f => f.Visible(true))
.Scrolling(s =>
{
    s.ShowScrollbar(ShowScrollbarMode.Always);
    s.UseNative(true);
})
.OnToolbarPreparing("dxToolbar.toolbarPreparing")
.StateStoring(s => s
    .Enabled(true)
    .StorageKey(tableId)
)
.Height(new JS("function () { return window.innerHeight / 1.3; }"))
)
@(Html.DevExtreme()
.Popup()
.ID("closeHenBatchPopUp")
.Title(localizer[Lang.CloseHenBatchPopUpTitle])
.Visible(false)
.MaxWidth(500)
.MaxHeight(500)
.ContentTemplate(
    @<text>
        @Html.DevExtreme().TextBox().ID("closing-hen-batch-id").Visible(false)

        @Html.Label(localizer[Lang.CloseHenBatchReasonLabel])
        @Html.DevExtreme().TextArea().ID("closeReason").Placeholder(localizer[Lang.CloseHenBatchReasonPlaceholder])
        <br />
        @Html.Label(localizer[Lang.CloseHenBatchDateLabel])
        @Html.DevExtreme().DateBox().ID("closeDate").DisplayFormat(dateFormatString)
        <br />
        @Html.DevExtreme().Button().ID("closing-hen-batch-close-button").Text(localizer[Lang.CloseBtn])
        @Html.DevExtreme().Button().ID("closing-hen-batch-cancel-button").Text(localizer[Lang.CancelBtn])
    </text>
))


@section Scripts {
    <script>
        const datatableResources = @Json.Serialize(ViewData["DatatableResources"]);
        const henBatchIndexResources = @Json.Serialize(ViewData["HenBatchIndexResources"]);
        $('[data-toggle="tooltip"]').tooltip();
        const validationError = '@Html.Raw(validationError)';
        const noPlaceAvailable = '@Html.Raw(noPlaceAvailable)';
        const henStage = '@henStage';
        const tableId = '@tableId'
        const lang = '@lang';
    </script>
    <script src="@Url.Content("~/lib/bootstrap-datepicker/bootstrap-datepicker.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/lib/bootstrap-datepicker/locales/bootstrap-datepicker.es.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/views/henBatch/henBatchTreeActionHandler.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/views/henBatch/index.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/views/henBatch/actionButtonVisibility.js")" type="text/javascript"></script>
    <script src="~/js/devextreme/localization/dx.messages.es.js"></script>
    <script src="~/js/devextreme/localization/dx.messages.pt.js"></script>
    <script>
        DevExpress.localization.locale('@lang');
    </script>
}

<ignite-load plugins="select2,date-time-picker"></ignite-load>