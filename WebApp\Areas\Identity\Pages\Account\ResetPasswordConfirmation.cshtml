﻿@page
@model ResetPasswordConfirmationModel
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Areas.Identity.Pages.Account.ResetPasswordConfirmation
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer
@{
    ViewData["Title"] = localizer[Lang.Title];
	Layout = "/Views/Shared/_ExternalLayout.cshtml";
}

<section id="wrapper">
    <div class="login-register" style="background-image:url('@Url.Content("~/images/bg-register.png")');">
        <div class="login-box card">
            <div class="card-body text-center">
                <a href="javascript:void(0)" class="text-center db">
                    <img src="@Url.Action("Display", "Tenant", new { replace = "logo-promanager.png" })" class="mb-4" width="200" alt="Home" />
                </a>
                <h3 class="box-title">@ViewData["Title"]</h3>
                <p>
					@localizer[Lang.SubtitleText] <a asp-page="./Login">@localizer[Lang.SubtitleButton]</a>
				</p>
            </div>
        </div>
    </div>
</section>
