﻿@page
@model LoginModel
@using Domain.Entities.Model.Enum
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Areas.Identity.Pages.Account.Login
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer
@inject Binit.Framework.Interfaces.Configuration.IRealmConfiguration realmConfig
@{
    ViewData["Title"] = @localizer[Lang.Title];
    Layout = "/Views/Shared/_ExternalLayout.cshtml";
}


<div id="login" class="d-flex align-items-center justify-content-center vh-100">
    <div class="card-body bg-white h-100" style="max-width: 55rem;">
        <form id="account" method="post"
            class="floating-labels d-flex flex-column align-items-center justify-content-center h-100">
            <a href="javascript:void(0)" class="text-center db">
                <img src="/Tenant/Display?replace=logo-promanager.png&version=@DateTime.Now.Ticks"
                    style="margin-right: 1.5rem;" width="150" alt="Home" />
            </a>
            <div asp-validation-summary="ModelOnly" class="text-danger mb-4"></div>

            <!-- Inputs container -->
            <div class="login-container p-5 px-md-5 rounded" style="max-width: 500px; width: 100%;">
                <div class="form-group mb-5 px-3">
                    <div class="col-xs-12">
                        <label asp-for="Input.Email"
                            style="position: static; display: block; margin-bottom: 5px;"></label>
                        <input asp-for="Input.Email" class="form-control" type="text" required ondrop="return false;"
                            style="border: 1px solid #ccc; border-radius: 4px; padding: 8px;">
                        <span class="bar"></span>
                        <span asp-validation-for="Input.Email" class="text-danger"></span>
                    </div>
                </div>
                <div class="form-group mb-5 px-3">
                    <div class="col-xs-12">
                        <label asp-for="Input.Password"
                            style="position: static; display: block; margin-bottom: 5px;"></label>
                        <input asp-for="Input.Password" class="form-control" type="password" required
                            ondrop="return false;" style="border: 1px solid #ccc; border-radius: 4px; padding: 8px;">
                        <span class="bar"></span>
                        <span asp-validation-for="Input.Password" class="text-danger"></span>
                    </div>
                </div>
                <div class="form-group mb-5 px-3">
                    <div class="col-md-12">
                        <div class="checkbox checkbox-primary p-t-0 d-flex align-items-center">
                            <input asp-for="Input.RememberMe" id="checkbox-signup" type="checkbox"
                                class="filled-in chk-col-light-blue mr-2">
                            <label for="checkbox-signup"> @localizer[Lang.RememberMe] </label>
                        </div>
                        <div class="mt-2">
                            <a asp-page="./ForgotPassword" id="to-recover" class="text-dark"
                                style="font-weight:600;">@localizer[Lang.ForgotPassword]</a>
                        </div>
                    </div>
                </div>
                <div class="form-group text-center mt-5 px-3">
                    <div class="col-xs-12">
                        <button class="btn btn-themecolor btn-block text-uppercase btn-rounded py-2" type="submit"
                            style="font-size: 1rem;">@localizer[Lang.SubmitButton]</button>
                    </div>
                </div>
            </div>
            <!--/Inputs container -->

            @*<div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-12 m-t-10 text-center">
                        <div class="social">
                        </div>
                    </div>
                </div>*@

            @if (realmConfig.AllowSelfSignUp)
            {
                <div class="form-group m-b-0">
                    <div class="col-sm-12 text-center">
                        @localizer[Lang.NoAccount] <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl"
                            class="text-primary m-l-5"><b>@localizer[Lang.SignUp]</b></a>
                    </div>
                </div>
            }
        </form>

        @{
            if ((Model.ExternalLogins?.Count ?? 0) != 0)
            {
                <form id="external-account" class="text-center form-horizontal" asp-page="./ExternalLogin"
                    asp-route-returnUrl="@Model.ReturnUrl" method="post">
                    <div class="social">
                        <p>
                            @foreach (var provider in Model.ExternalLogins)
                            {
                                switch (provider.Name)
                                {
                                    case "Google":
                                        <text>
                                            <button type="submit" href="javascript:void(0)" name="provider" value="@provider.Name"
                                                class="btn btn-googleplus" data-toggle="tooltip" title="@localizer[Lang.WithGoogle]">
                                                <i aria-hidden="true" class="fab fa-google-plus-g"></i>
                                            </button>
                                        </text>
                                        break;
                                    case "Facebook":
                                        <text>
                                            <button type="submit" href="javascript:void(0)" name="provider" value="@provider.Name"
                                                class="btn btn-facebook" data-toggle="tooltip" title="@localizer[Lang.WithFacebook]">
                                                <i aria-hidden="true" class="fab fa-facebook"></i>
                                            </button>
                                        </text>
                                        break;
                                    case "Twitter":
                                        <text>
                                            <button type="submit" href="javascript:void(0)" name="provider" value="@provider.Name"
                                                class="btn btn-twitter" data-toggle="tooltip" title="@localizer[Lang.WithTwitter]">
                                                <i aria-hidden="true" class="fab fa-twitter"></i>
                                            </button>
                                        </text>
                                        break;
                                    default:
                                        <text>
                                            <button type="submit" href="javascript:void(0)" name="provider" value="@provider.Name"
                                                class="btn btn-primary" data-toggle="tooltip"
                                                title="@(string.Format(localizer[Lang.WithAny], @provider.DisplayName))">
                                                @provider.DisplayName
                                            </button>
                                        </text>
                                        break;
                                }
                            }
                        </p>
                    </div>
                </form>
            }
        }
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
