using System;
using System.Collections.Generic;
using Domain.Entities.Model;

namespace Domain.Logic.BusinessLogic.DTOs
{
    public class ManagerialLayingReportFilterDTO
    {
        public Guid Farm { get; set; }
        public Guid ParentHenBatch { get; set; }
        public bool? Active { get; set; }
        public HenStage HenStage { get; set; }
    }

    public class ManagerialLayingReportDTO
    {
        public string Farm { get; set; }
        public string ParentBatch { get; set; }
        public string Distribution { get; set; }
        public string BatchStatus { get; set; }

        public List<HenBatchPerformance> ParentHenBatchPerformances { get; set; }
        public List<HenBatchPerformance> HenBatchPerformances { get; set; }
        public List<GeneticsParametersReference> GeneticsParameters { get; set; }
    }

    public class BatchWeekDataDTO
    {
        public Guid HenBatchId { get; set; }
        public string WarehouseName { get; set; }
        public string LineName { get; set; }
        public string CategoryName { get; set; }

        public int Week { get; set; }
        public int HenAmountFemale { get; set; }
        public int HenAmountMale { get; set; }
    }
}