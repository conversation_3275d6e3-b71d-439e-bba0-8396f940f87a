@model WebApp.Models.MaterialViewModel;
@using Binit.Framework;
@using Binit.Framework.Constants.SeedEntities;
@using Domain.Entities.Model.Enum;
@using Microsoft.Extensions.Localization;
@using WebApp.WebTools;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Material.CreateOrEdit;
@inject IStringLocalizer<SharedResources> localizer
@inject Domain.Logic.ExternalServices.ERP.Account.IERPAPIAccountService erpApiAccountService
@{
    bool all = (bool)ViewData["All"];
    var materialTypes = ViewData["MaterialTypes"] as List<SelectListItem>;
    var capacityUnit = ViewData["CapacityUnit"] as List<SelectListItem>;
    var area = ViewData["Area"] as AreaEnum?;
    var materialType = ViewData["MaterialType"] != null ? (KeyValuePair<Guid, MaterialTypeValue>)ViewData["MaterialType"] : new KeyValuePair<Guid, MaterialTypeValue>();
    var action = $"/Material/{ViewData["Action"] as string}/{(!all ? materialType.Key.ToString() : null)}";
    var stockRemaining = ViewData["Action"] as string == "Edit" ? (bool)ViewData["StockRemaining"] : false;
    var submitLabel = ViewData["Action"] as string == "Create" ? localizer[Lang.BtnCreate] : localizer[Lang.BtnUpdate];
    var materialIsFormula = (bool)ViewData["MaterialIsFormula"];
    var referUrl = materialIsFormula ? $"/Formula" : $"/Material{(!all ? "/" + materialType.Key : "")}?area={area}";
    var formId = "materialForm";
}
@section ViewStyles{
    <link href="~/css/views/material/createoredit.css" rel="stylesheet" />
}

<form class="floating-labels" id=@formId method="POST" action="@action">
    <input type="hidden" asp-for="Id" />
    <input type="hidden" asp-for="Active" />
    <input type="hidden" asp-for="Quantity" />
    <input type="hidden" id="materialTypeId" name="materialTypeId" value="@(!all ? materialType.Key.ToString() : null)" />
    <div class="row">
        <div class="col-12 col-sm-3">
            <ignite-input for-property="InternalId"></ignite-input>
        </div>
        <div class="col-12 col-sm-6">
            <ignite-input for-property="Name"></ignite-input>
        </div>
        <div class="col-12 col-sm-6">
            <ignite-input for-property="Description"></ignite-input>
        </div>
        <div class="col-12 col-sm-6">
            <ignite-input for-property="ExpirationTime"></ignite-input>
        </div>
    </div>
    <div class="row" id="selects">
        <div class="col-4">
            <ignite-dropdown for-property="MaterialTypeId"
                             items="@materialTypes"
                             placeholder="@localizer[Lang.PlaceholderSelectMaterialType]"
                             disabled ="@materialIsFormula">
            </ignite-dropdown>
        </div>
        <div class="col-2">
            <ignite-dropdown for-property="CapacityUnitId"
                             items="@capacityUnit"
                             placeholder="@localizer[Lang.PlaceholderSelectCapacityUnit]">
            </ignite-dropdown>
        </div>
    </div>

    <div class="row">
        <div class="form-group m-b-40 col-12 col-md-6 col-sm-6 m-t-30" binit-validation-for="ColorIdentifier"
             binit-onerror-class="has-danger" binit-onsuccess-class="has-success">
            <label asp-for="ColorIdentifier"></label>
            <input id="ColorIdentifier" class="form-control" type="color" asp-for="ColorIdentifier">
            <span asp-validation-for="ColorIdentifier" class="form-control-feedback"></span>
        </div>
        <ignite-checkbox for-property="ColorChecked" value="@(Model.ColorChecked)"></ignite-checkbox>
    </div>
    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item"> <a class="nav-link active" data-toggle="tab" href="#stock-pane" role="tab"><span class="hidden-sm-up" id="stock-tab"></span> <span class="hidden-xs-down">@(localizer[Lang.StockProperties])</span></a> </li>
        <li class="nav-item"> <a class="nav-link" data-toggle="tab" href="#standard-pane" role="tab"><span class="hidden-sm-up" id="standard-tab"></span> <span class="hidden-xs-down">@(localizer[Lang.StandardProperties])</span></a> </li>

    </ul>
    <!-- Tabs -->
    <div class="tab-content tabcontent-border">
        <!-- Stock -->
        <div class="tab-pane p-20 active" id="stock-pane" role="tabpanel">
            <div style="margin-top: 20px">
                <ignite-input for-property="OptimalStock"></ignite-input>
                <ignite-input for-property="MinimalStock"></ignite-input>
            </div>
            <div class="d-flex justify-content-end" style="flex-direction: column; align-items:flex-end">
                @{
                    var showSynchronizer = true;
                    if (!erpApiAccountService.IsLoggedInOnErpAsSystem())
                    {
                        try
                        {
                            await erpApiAccountService.LoginAsSystem();
                        }
                        catch (Exception)
                        {
                            showSynchronizer = false;
                        }
                    }

                    if (showSynchronizer)
                    {
                        <ignite-checkbox for-property="Synchronize" size="large" color="#29313e"></ignite-checkbox>
                    }
                    else
                    {
                        <div hidden>
                            <ignite-checkbox for-property="Synchronize" size="large" color="#29313e"></ignite-checkbox>
                        </div>
                    }

                }
            </div>
        </div>
        <!-- Standards -->
        <div class="tab-pane p-20" id="standard-pane" role="tabpanel">
            @if (ViewData.ModelState.FirstOrDefault(a => a.Key == "Standards").Value != null)
            {
                <ul>
                    @foreach (var error in Html.ViewData.ModelState["Standards"].Errors)
                    {
                        <li class="form-control-feedback field-validation-error">@error.ErrorMessage</li>
                    }
                </ul>
            }
            <div class="form-group m-b-20" binit-validation-for="Standards"
                 binit-onerror-class="has-danger" binit-onsuccess-class="has-success">
                <span asp-validation-for="StandardValidationError" class="form-control-feedback"></span>
            </div>

            <div class="row justify-content-start">
                <button id="add-standard" type="button" class="btn btn-primary mr-2 mb-2">
                    <i class="fa fa-plus"></i> @(localizer[Lang.BtnAddStandard])
                </button>
            </div>

            <div class="row" standard-row style="@(!Model.Standards.Any() ? "display:none" : "")">
                <div class="col-12">
                    <table id="standard-details" class="table w-100 details">
                        <thead>
                            <tr>
                                <th>@(localizer[Lang.TableStandardName])</th>
                                <th>@(localizer[Lang.TableOptimalIntervalValueMin])</th>
                                <th>@(localizer[Lang.TableOptimalIntervalValueMax])</th>
                                <th>@(localizer[Lang.TableValueAcceptedMin])</th>
                                <th>@(localizer[Lang.TableValueAcceptedMax])</th>
                                <th>@(localizer[Lang.TableActions])</th>
                            </tr>
                        </thead>
                        <tbody>
                            @{
                                int i = 0;
                                foreach (var standard in Model.Standards)
                                {
                                    @await Component.InvokeAsync("MaterialStandardRow", new { index = i, model = Model, removable = standard.removable, editable = true });
                                    i++;
                                }
                            }
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="row justify-content-start" style="display:@(action == "Create"? "none":"")">
                <button type="submit" class="btn btn-primary mr-2 mb-2" onclick="window.location.href='@Url.Action("Index","MaterialAnalysisReport", new {area = area, materialId = Model.Id})'">
                    @(localizer[Lang.BtnGoToAnalysis])
                </button>
            </div>
        </div>

    </div>
    <!-- End Tabs -->
    <!-- Aliases -->
    @await Component.InvokeAsync("EntityAlias", new { model = Model.AliasableViewModel, entityType = Model.GetEntityType(), entityId = Model.Id, formId = formId })
</form>

@{
    @await Html.PartialAsync("_FileManager", Model.Files,
        new FileManagerOptions(ViewData, localizer)
            {
            UploadEnabled = true,
            MaxFiles = 1,
            ParentFormId = "materialForm",
            PropertyName = "FilesIds",
            AcceptedMimeTypes = "image/jpeg,image/jpeg,image/png"
        }
    );
}

<div class="d-flex justify-content-end">
    <button type="button" class="btn btn-secondary mr-2"
            onclick="location.href='@(referUrl)'">
            @localizer[Lang.BtnCancel]
    </button>
    <button id="createMaterial" type="submit" class="btn btn-themecolor" form="materialForm">@submitLabel</button>
</div>

@section Scripts{
    <script>
        var area = '@area';
        var MaterialResources = @Json.Serialize(ViewData["MaterialResources"]);
    </script>
    <script src="~/js/views/material/createoredit.js"></script>
}

<ignite-load plugins="select2,dropzone,switchery"></ignite-load>