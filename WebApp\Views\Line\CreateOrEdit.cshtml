@model WebApp.Models.LineViewModel;
@using Binit.Framework;
@using Microsoft.Extensions.Localization;

@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Line.CreateOrEdit;
@inject IStringLocalizer<SharedResources> localizer

@{
    var action = ViewData["Action"] as string;
    var actionsEnums = ViewData["ActionsEnums"] as List<SelectListItem>;
    var areas = ViewData["AreasEnum"] as List<SelectListItem>;
    var capacityUnits = ViewData["CapacityUnits"] as List<SelectListItem>;
    var containers = ViewData["Containers"] as List<SelectListItem>;
    var materialTypes = ViewData["MaterialTypes"] as List<SelectListItem>;
    var submitLabel = action == "Create" ? localizer[Lang.BtnCreate] : localizer[Lang.BtnUpdate];
    var warehouses = ViewData["Warehouses"] as List<SelectListItem>;
    var formId = "lineForm";
    var units = ViewData["CapacityUnits"] as List<SelectListItem>;
    var farms = ViewData["Farms"] as List<SelectListItem>;
    var hasClusters = (bool)ViewData["HasClusters"];
    var hasSectors = (bool)ViewData["HasSectors"];
}
@section ViewStyles{
    <link href="~/css/views/silo/createOrEdit.css" rel="stylesheet" />
}
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<form class="floating-labels" id=@formId method="POST" action="@action">

    <input type="hidden" asp-for="Id" />
    <input type="hidden" asp-for="ContainerProperties.Id" />
    @if (action == "Create")
    {
        <input type="hidden" asp-for="Name" />
    }

    <div class="row @(action == "Edit"?"":"align-items-end")">

        @if (action == "Edit")
        {
            <input type="hidden" asp-for="WarehouseId" />
            <input type="hidden" asp-for="WarehouseName" />
            <div class="col-xl-3 col-lg-4 col-md-12 col-sm-12 col-12">
                <ignite-input for-property="WarehouseName" type="text" disabled></ignite-input>
            </div>

            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" style="display:flex">
                <ignite-input for-property="ContainerProperties.Name" type="text"></ignite-input>
            </div>

            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" style="display:flex">
                <div class="col-xl-9 col-9">
                    <ignite-input for-property="ContainerProperties.DetailedName" type="text"
                                  disabled="@(!Model.ContainerProperties.ModifiedDetailedName)"></ignite-input>
                </div>
                <div class="col-xl-3 col-3">
                    <ignite-checkbox for-property="ContainerProperties.ModifiedDetailedName" color="#29313e"></ignite-checkbox>
                </div>
                <div class="col-4">
                    <ignite-input for-property="ContainerProperties.Code" type="text"></ignite-input>
                </div>
            </div>
        }
        else
        {
            <div class="col-xl-3 col-lg-4 col-md-12 col-sm-12 col-12">
                <ignite-dropdown for-property="WarehouseId"
                                 items="@warehouses"
                                 placeholder="@localizer[Lang.PlaceholderSelectWarehouse]">
                </ignite-dropdown>
            </div>
        }

    </div>
    <div class="row">
        <div class="col-xl-6 col-lg-12 col-md-12 col-sm-12 col-12">
            <ignite-input for-property="@Model.FloorArea" type="decimal" min="0"></ignite-input>
        </div>
    </div>

    <!-- Container actions -->
    @await Component.InvokeAsync("ContainersActionsTable", new { model = Model, farms = farms, areas = areas, containers = containers, action = action, name = "Line" })

    <!-- Material type actions -->
    @await Component.InvokeAsync("MaterialTypeActionsTable", new { model = Model, materialTypes = materialTypes, actionsEnums = actionsEnums, units = units })

    <!-- Aliases -->
    @await Component.InvokeAsync("EntityAlias", new { model = Model.AliasableViewModel, entityType = "Line", entityId = Model.Id, formId = formId })

    <div class="d-flex justify-content-end">
        <button type="button" class="btn btn-secondary mr-2"
                onclick="window.location.href='@Url.Action("Index","Line")'">
            @localizer[Lang.BtnCancel]
        </button>
        <button type="submit" class="btn btn-themecolor">@submitLabel</button>
    </div>
</form>

@section Scripts {
    <script>
        const lineCreateOrEditResources = @Json.Serialize(ViewData["LineCreateOrEditResources"]);
        var entityType = '@Html.Raw(ViewData["EntityType"])';
    </script>
    <script src="@Url.Content("~/js/views/line/createoredit.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/containerSharedFunctions.js")" type="text/javascript"></script>
}

<ignite-load plugins="select2,switchery"></ignite-load>