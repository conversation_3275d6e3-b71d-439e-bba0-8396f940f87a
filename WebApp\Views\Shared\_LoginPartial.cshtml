@using Binit.Framework
@using Binit.Framework.Entities;
@using Binit.Framework.Helpers
@using Domain.Entities.Model
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization
@using Domain.Entities.Model.Enum
@using Domain.Logic.DTOs.NotificationDTOs
@inject Domain.Logic.ExternalServices.ERP.Account.IERPAPIAccountService erpApiAccountService
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
@inject Domain.Logic.Interfaces.IAccountService AccountService
@inject Domain.Logic.Interfaces.IClaimBusinessLogic claimBusinessLogic
@inject Microsoft.Extensions.Configuration.IConfiguration configuration
@inject Binit.Framework.Interfaces.Configuration.IGeneralConfiguration generalConfiguration
@inject Domain.Logic.Interfaces.INotificationBusinessLogic NotificationBusinessLogic
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Shared._LoginPartial
@inject IStringLocalizer<SharedResources> localizer
@inject Binit.Framework.Interfaces.DAL.IService<TenantConfiguration> tenantConfigurationService


@{
    var user = await AccountService.GetUserFull();
    var claims = await claimBusinessLogic.BuildNavbarClaims();
    // declaration of variables for notifications
    bool allowToRecibeNotifications = tenantConfigurationService.GetAll(asNoTracking: true)
        .Where(c => c.TenantId == user.TenantId).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.AllowToRecibeNotifications && s.Value == "True");
    bool haveNotifications = false;
    List<NotificationDTO> notificationNotSeen = default;
    int indexToNotifications = 0;
    if (allowToRecibeNotifications)
    {
        try
        {
            notificationNotSeen = NotificationBusinessLogic.GetNotificationNotSeen();
            haveNotifications = notificationNotSeen.Count() > 0;
        }
        catch (Exception ex) { }
    }
}

<ul class="navbar-nav">
    @if (SignInManager.IsSignedIn(User))
    {
        <!-- User profile and search -->
        <ul class="navbar-nav my-lg-0 d-flex.no-block align-items-center">
            <!-- Notifications -->
            @if (allowToRecibeNotifications)
            {
                <li class="nav-item dropdown notification-container" id="notification-container">
                    <a class="nav-link waves-effect waves-dark" id="expand-notifications-button" href="" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="icon-Bell"></i>
                        @if (haveNotifications)
                        {
                            <div class="notify" id="notification-alert"> <span class="heartbit" style="border: 5px solid #fcff00;"></span> <span class="point"></span> </div>
                        }
                    </a>

                    <div class="dropdown-menu mailbox dropdown-menu-right animated bounceInDown p-4" style="overflow-y: auto; max-height: 350px;">
                        <h4>@(localizer[Lang.NotificationLabel])</h4>

                        @if (notificationNotSeen != null)
                        {
                            @foreach (var notification in notificationNotSeen)
                            {
                                <div class="card w-70 card-notification" id="card-notification-@indexToNotifications">
                                    <div class="card-body">
                                        <input type="hidden" asp-for=@notification.Id />
                                        <input type="hidden" asp-for=@notification.Url />
                                        <h5 class="card-title mt-1">@notification.Title</h5>
                                        <p class="card-text">@notification.Body</p>
                                    </div>
                                </div>
                                indexToNotifications++;
                            }
                        }

                        <button id="notification-redirect" type="button" onclick="location.href='@Url.Action("Index", "Notification", new { showByUser = true })'"
                        class="btn btn-info btn-block">
                            @(localizer[Lang.NotificationButtonLabel])
                        </button>
                    </div>
                </li>
            }

            <!-- Choose access level -->
        <li class="nav-item dropdown" id="claims">
                <a style="display:flex;flex-direction:row;line-height:20px;align-items:center;padding-bottom:0px;"
               class="nav-link dropdown-toggle waves-effect waves-dark" href="" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="btn-claims">
                    <i class="icon-Filter-2" style="margin-right:10px; color: black; font-weight: bold; text-shadow: 0 0 1px black;"></i>
                    <span style="font-size:15px">
                        @if (claims.ShowCompany)
                        {
                            @($"{localizer[Lang.CompanyLabel]}: ".ToUpper()) @(string.IsNullOrWhiteSpace(claims.CompanyName) ? localizer[Lang.AllLabel] : claims.CompanyName) @(" ")
                        }
                        @if (claims.ShowSite)
                        {
                            @($"{localizer[Lang.SiteLabel]}: ".ToUpper()) @(string.IsNullOrWhiteSpace(claims.SiteName) ? localizer[Lang.AllLabel] : claims.SiteName) @(" ")
                        }
                    </span>
                </a>
                <div class="dropdown-menu mailbox dropdown-menu-right animated bounceInDown">
                    <ul>
                        <li class="p-20">
                            <h4>@(localizer[Lang.AccessLevelLabel])</h4>
                            <form ignore-unload>
                                <div class="form-group text-center" id="companies">
                                    <label for="CompanyClaimsId">@(localizer[Lang.CompanyLabel])</label>
                                    <select class="form-control" id="CompanyClaimsId" name="CompanyClaimsId">
                                        <option value="">@(localizer[Lang.AllLabel])</option>
                                    </select>
                                </div>
                                <div class="form-group text-center" id="sites">
                                    <label for="SiteClaimsId">@(localizer[Lang.SiteLabel])</label>
                                    <select class="form-control" id="SiteClaimsId" name="SiteClaimsId">
                                        <option value="">@(localizer[Lang.AllLabel])</option>
                                    </select>
                                </div>
                                <div class="form-group text-center" id="sectors">
                                    <label for="SectorClaimsId">@(localizer[Lang.SectorLabel])</label>
                                    <select class="form-control" id="SectorClaimsId" name="SectorClaimsId">
                                        <option value="">@(localizer[Lang.AllLabel])</option>
                                    </select>
                                </div>
                                <button id="claimSubmit" type="button" class="btn btn-info btn-block">@(localizer[Lang.BtnSubmit])</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </li>

            <!-- Extra redirection buttons -->
        <li class="nav-item dropdown u-pro">
                <a class="nav-link dropdown-toggle waves-effect waves-dark" href="" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <img src="~/images/icon/apps-icon.png" style="max-width: 2rem; filter: invert(25%);" />
                </a>
                <div class="dropdown-menu dropdown-menu-right animated flipInY drop-app">
                    <ul class="dropdown-user app-ul">
                        @{
                            var showErpButton = true;
                            if (!erpApiAccountService.IsLoggedInOnErpAsSystem())
                            {
                                try
                                {
                                    await erpApiAccountService.LoginAsSystem();
                                }
                                catch (Exception)
                                {
                                    showErpButton = false;
                                }
                            }

                            if (showErpButton && user.ERPRoles != null)
                            {
                                <li><a class="erp-external-link app-link" data-redirect-url="/Home" data-callback-url="/"><img src="~/images/icon/denary30x30.png" style="width:35px;" /> @(localizer[Lang.NavigateToERPLabel])</a></li>
                            }
                        }
                        <li><a class="academy-external-link app-link" data-redirect-url="/Article" data-callback-url="/"><img src="~/images/icon/academy30x30.png" style="width:40px" />@(localizer[Lang.NavigateToWikiLabel])</a></li>
                    </ul>
                </div>
            </li>

            <!-- Profile -->
        <li class="nav-item dropdown u-pro">
                <a class="nav-link dropdown-toggle waves-effect waves-dark profile-pic" href="" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    @if (user != null && user.PhotoId.HasValue)
                    {
                        <img src="/File/Display/@user.PhotoId.ToString()" alt="user" class="" />
                    }
                    else
                    {
                        <img src="~/images/users/user-placeholder.png" alt="user" class="" />
                    }
                    <span>
                        <span class="hidden-md-down">@UserManager.GetUserName(User) &nbsp;<i class="fa fa-angle-down"></i></span>
                        <span class="hidden-md-down" style="display:flex; justify-content:center; line-height:1px!important">
                            @user.Tenant.Name.ToUpper()
                        </span>
                    </span>
                </a>
                <div class="dropdown-menu dropdown-menu-right animated flipInY">
                    <ul class="dropdown-user">
                        <li>
                            <div class="dw-user-box">
                                <div class="u-img">
                                    @if (user.PhotoId.HasValue)
                                    {
                                        <img src="/File/Display/@user.PhotoId.ToString()" alt="user" />
                                    }
                                    else
                                    {
                                        <img src="~/images/users/user-placeholder.png" alt="user">
                                    }
                                </div>
                                <div class="u-text m-t-10">
                                    <h4>@UserManager.GetUserName(User)</h4>
                                </div>
                            </div>
                        </li>
                        <li role="separator" class="divider"></li>
                        <li><a asp-area="Identity" asp-page="/Account/Manage/Index"><i class="ti-settings"></i> @(localizer[Lang.BtnAccount])</a></li>
                        <li role="separator" class="divider"></li>
                        <li>
                            <form id="logoutForm" asp-antiforgery="true" asp-area="Identity" asp-page-handler="External" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                                <a href="#" onclick="document.getElementById('logoutForm').submit();"><i class="fa fa-power-off"></i> @(localizer[Lang.BtnLogout])</a>
                            </form>
                        </li>
                        <li role="separator" class="divider"></li>
                    </ul>
                    <p class="account-dropdown-footer">
                        &copy;@VersionHelper.GetVersion() - @configuration.GetSection("General")["SystemName"] - <a asp-area="" asp-controller="Home" asp-action="Privacy">@(localizer[Lang.Privacy])</a>
                    </p>
                </div>
            </li>
        </ul>
    }
    else
    {
        <li class="nav-item mr-2">
            <a class="btn btn-rounded btn-warning" id="register" asp-area="Identity" asp-page="/Account/Register">@(localizer[Lang.BtnRegister])</a>
        </li>
        <li class="nav-item mr-2">
            <a class="btn btn-rounded btn-secondary" id="login" asp-area="Identity" asp-page="/Account/Login">@(localizer[Lang.BtnLogin])</a>
        </li>
    }
</ul>

