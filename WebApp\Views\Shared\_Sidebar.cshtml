@using WebApp.UIComponents.SidenavItem
@using Binit.Framework.Interfaces.DAL
@using Binit.Framework
@using Domain.Entities.Model
@using Domain.Entities.Model.Enum
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Mvc.Rendering
@using Binit.Framework.Interfaces.Configuration
@using Binit.Framework.Constants.Authentication
@using Binit.Framework.Constants.SeedEntities;
@inject IStringLocalizer<SharedResources> localizer
@inject IOperationContext operationContext
@inject ISolutionConfiguration solutionConfiguration
@inject IService<TenantConfiguration> tenantConfigurationService
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.Shared._Sidebar

@{
    bool foodFactoryConfig = tenantConfigurationService.GetAll()
        .Where(c => c.TenantId == operationContext.GetUserTenantId() && c.TenantConfigurationEnum == TenantConfigurationEnum.FeedFactory)
        .Any(c => c.Value == "True");

    bool automaticallySentEggsConfiguration = tenantConfigurationService.GetAll()
        .Where(c => c.TenantId == operationContext.GetUserTenantId() && c.TenantConfigurationEnum == TenantConfigurationEnum.AutomaticallySentEggs)
        .Any(c => c.Value == "True");

    bool canAccessToSerology = tenantConfigurationService.GetAll()
        .Where(c => c.TenantId == operationContext.GetUserTenantId() && c.TenantConfigurationEnum == TenantConfigurationEnum.Serology)
        .Any(c => c.Value == "True");
}

<aside class="left-sidebar">
    <!-- Sidebar scroll-->
    <div class="scroll-sidebar">
        <!-- Sidebar navigation-->
        <nav class="sidebar-nav d-flex flex-column justify-content-between">
            @if (User.Identity.IsAuthenticated)
            {
                <ul class="sidebarnav top flex-grow-1">

                    @(await Html.RenderComponentAsync<SidenavItem>(
                        RenderMode.Static,
                        new SidenavItem(
                            title: localizer[Lang.BtnHome],
                            icon: "home",
                            link: Url.Action("Index", "Home"),
                            visible: true,
                            new List<SidenavItem>()
                            {
                                new SidenavItem(
                            title: localizer[Lang.BtnDashboard],
                            icon: "icon-Office",
                            link: Url.Action("Index", "Home"),
                            visible: true,
                            id: "sidebarHomeDashboard"
                            ),
                                new SidenavItem(
                            title: localizer[Lang.BtnMessages],
                            icon: "icon-Office",
                            link: Url.Action("Index", "Message"),
                            visible: true,
                            id: "sidebarMessageAll"
                            )
                            },
                            id: "sidebarHome"
                        )
                    ))
                    @{ if (foodFactoryConfig)
                        {
                            @(await Html.RenderComponentAsync<SidenavItem>(
                            RenderMode.Static,
                            new SidenavItem(
                            title: localizer[Lang.BtnFoodManufacturing],
                            icon: "feed",
                            "#",
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeFeedFactoryAdministrator,
                                Roles.BackofficeFeedFactoryReportAdministrator,
                                Roles.BackofficeFeedFactoryFormulaAdministrator,
                                Roles.BackofficeFeedFactoryInconsistencyAdministrator,
                                Roles.BackofficeFeedFactoryShippingNoteAdministrator,
                                Roles.BackofficeMovementReportAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator,
                                Roles.BackofficeMaterialAnalysisReportAdministrator,
                                Roles.BackofficeMaterialAnalysisReportUser,
                                Roles.BackofficeFeedFactoryMaterialAnalysisReportUser,
                                Roles.BackofficeFeedFactoryMaterialAnalysisReportAdministrator,
                                Roles.BackofficeFeedFactoryHappeningUser,
                                Roles.BackofficeFeedFactoryHappeningAdministrator,
                                Roles.BackofficeHappeningAdministrator,
                                Roles.BackofficeHappeningUser,
                                Roles.BackofficeFeedFactoryStockUser,
                                Roles.BackofficeStockAdministrator,
                                Roles.BackofficeStockUser,
                                Roles.BackofficeFeedFactoryInconsistencyUser,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator),
                            new List<SidenavItem>()
                            {
                        new SidenavItem(
                            title: localizer[Lang.BtnDashboard],
                            icon: "icon-Office",
                            link: Url.Action("Dashboard", "FoodManufacturing"),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeFeedFactoryAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator),
                            id: "sidebarFoodManufacturingDashboard"
                        ),
                        new SidenavItem(
                            title: localizer[Lang.BtnMessages],
                            icon: "icon-Office",
                            link: Url.Action("Index", "Message", new { area = AreaEnum.FeedFactory }),
                            visible: true,
                            id: "sidebarMessageFeedFactory"
                            ),
                        new SidenavItem(
                        title: localizer[Lang.BtnSingleReport],
                        icon: "icon-Office",
                        link: Url.Action("Index", "FeedProductionReport"),
                        visible: operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeFeedFactoryAdministrator,
                            Roles.BackofficeFeedFactoryProductionReportAdministrator,
                            Roles.BackofficeFeedFactoryReportAdministrator,
                            Roles.BackofficeFeedFactoryProductionReportAdministrator),
                        id: "sidebarFeedProductionReport"
                        ),
                        new SidenavItem(
                            title: localizer[Lang.BtnRawMaterialReception],
                            icon: "icon-Office",
                            link: Url.Action("Index", "MaterialReceptionReport", new { area = AreaEnum.FeedFactory }),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeFeedFactoryAdministrator,
                                Roles.BackofficeStockAdministrator,
                                Roles.BackofficeStockUser,
                                Roles.BackofficeFeedFactoryAdministrator,
                                Roles.BackofficeFeedFactoryStockAdministrator,
                                Roles.BackofficeFeedFactoryStockUser),
                            id: "sidebarClassificationBtnRawMaterialReception"
                            ),
                        new SidenavItem(
                        title: localizer[Lang.BtnFoodProductionOrder],
                        icon: "icon-Office",
                        link: Url.Action("Index", "FoodProductionOrder"),
                        visible: operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeFeedFactoryAdministrator,
                            Roles.BackofficeFeedFactoryExtraordinaryFoodManufacturingAdministrator,
                            Roles.BackofficeFeedFactoryExtraordinaryFoodManufacturingUser),
                        id: "sidebarFoodProductionOrder"
                       ),
                        new SidenavItem(
                        title: localizer[Lang.BtnFoodManufacturingReports],
                        icon: "icon-office",
                        link: "#",
                        visible: operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeFeedFactoryAdministrator,
                            Roles.BackofficeFeedFactoryProductionReportAdministrator,
                            Roles.BackofficeFeedFactoryReportAdministrator,
                            Roles.BackofficeFeedFactoryProductionReportAdministrator),
                        new List<SidenavItem>()
                        {
                            new SidenavItem(
                            title: localizer[Lang.BtnFoodManufacturingProductionReport],
                            icon: "icon-Office",
                            link: Url.Action("Production", "Report"),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeFeedFactoryAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator,
                                Roles.BackofficeFeedFactoryReportAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator),
                            id: "sidebarFoodManufacturingProductionReport"),

                            new SidenavItem(
                            title: localizer[Lang.BtnFoodManufacturingConsumptionReport],
                            icon: "icon-Office",
                            link: Url.Action("Consumption", "Report"),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeFeedFactoryAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator,
                                Roles.BackofficeFeedFactoryReportAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator),
                            id: "sidebarFoodManufacturingConsumptionReport"),

                            new SidenavItem(
                            title: localizer[Lang.BtnFoodManufacturingInputReport],
                            icon: "icon-Office",
                            link: Url.Action("Input", "Report"),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeFeedFactoryAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator,
                                Roles.BackofficeFeedFactoryReportAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator),
                            id: "sidebarFoodManufacturingInputReport"),

                            new SidenavItem(
                            title: localizer[Lang.BtnFoodManufacturingOutputReport],
                            icon: "icon-Office",
                            link: Url.Action("Output", "Report"),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeFeedFactoryAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator,
                                Roles.BackofficeFeedFactoryReportAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator),
                            id: "sidebarFoodManufacturingOutputReport")
                        },
                        id: "sidebarFoodManufacturingReports"),

                        new SidenavItem(
                        title: localizer[Lang.BtnFormula],
                        icon: "icon-Office",
                        link: Url.Action("Index", "Formula"),
                        visible: operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeFeedFactoryAdministrator,
                            Roles.BackofficeFeedFactoryProductionReportAdministrator,
                            Roles.BackofficeFeedFactoryFormulaAdministrator),
                        id: "sidebarFormula"
                        ),
                       new SidenavItem(
                       title: localizer[Lang.BtnRawMaterials],
                       icon: "icon-Office",
                       link: $"/Material/{MaterialTypes.InsumoMateriaPrimaAlimentacion}?area={AreaEnum.FeedFactory}",
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeFeedFactoryAdministrator,
                           Roles.BackofficeFeedFactoryProductionReportAdministrator,
                           Roles.BackofficeStockAdministrator,
                           Roles.BackofficeFeedFactoryStockAdministrator,
                           Roles.BackofficeFeedFactoryStockUser),
                       id: "sidebarFeedFactoryRawMaterials"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnStocks],
                       icon: "icon-Office",
                       link: Url.Action("Index", "Stock", new { area = AreaEnum.FeedFactory }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeFeedFactoryAdministrator,
                           Roles.BackofficeFeedFactoryProductionReportAdministrator,
                           Roles.BackofficeFeedFactoryStockAdministrator,
                           Roles.BackofficeStockAdministrator,
                           Roles.BackofficeStockUser,
                           Roles.BackofficeFeedFactoryStockUser),
                       id: "sidebarFeedFactoryStock"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnShippingNotes],
                       icon: "icon-Office",
                       link: Url.Action("Index", "ShippingNote", new { area = AreaEnum.FeedFactory }),
                       visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                       Roles.BackofficeFeedFactoryProductionReportAdministrator,
                       Roles.BackofficeFeedFactoryAdministrator,
                       Roles.BackofficeFeedFactoryShippingNoteUser,
                       Roles.BackofficeFeedFactoryStockAdministrator,
                       Roles.BackofficeShippingNoteUser,
                       Roles.BackofficeShippingNoteAdministrator),
                       id: "sidebarFeedFactoryMovement"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnMovementReports],
                       icon: "icon-Office",
                       link: Url.Action("Index", "MovementReport", new { area = AreaEnum.FeedFactory }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeFeedFactoryProductionReportAdministrator,
                           Roles.BackofficeFeedFactoryStockAdministrator,
                           Roles.BackofficeMovementReportUser,
                           Roles.BackofficeMovementReportAdministrator),
                       id: "sidebarFeedFactoryMovementReport"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnHappening],
                       icon: "icon-Office",
                       link: Url.Action("Index", "Happening", new { area = AreaEnum.FeedFactory }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeFeedFactoryAdministrator,
                           Roles.BackofficeFeedFactoryProductionReportAdministrator,
                           Roles.BackofficeHappeningAdministrator, Roles.BackofficeHappeningUser,
                           Roles.BackofficeFeedFactoryHappeningAdministrator,
                           Roles.BackofficeFeedFactoryHappeningUser),
                       id: "sidebarFeedFactoryHappening"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnSampleMaterial],
                       icon: "icon-Office",
                       link: Url.Action("Index", "MaterialAnalysisReport", new { area = AreaEnum.FeedFactory }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeFeedFactoryAdministrator,
                           Roles.BackofficeFeedFactoryProductionReportAdministrator,
                           Roles.BackofficeMaterialAnalysisReportAdministrator,
                           Roles.BackofficeMaterialAnalysisReportUser,
                           Roles.BackofficeFeedFactoryMaterialAnalysisReportAdministrator,
                           Roles.BackofficeFeedFactoryMaterialAnalysisReportUser),
                       id: "sidebarFeedFactoryMaterialAnalysisReport"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnInconsistencyReport],
                       icon: "icon-Office",
                       link: Url.Action("Index", "InconsistencyReport", new { area = AreaEnum.FeedFactory }),
                       visible: operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeFeedFactoryAdministrator,
                            Roles.BackofficeInconsistencyAdministrator,
                            Roles.BackofficeInconsistencyUser,
                            Roles.BackofficeFeedFactoryProductionReportAdministrator,
                            Roles.BackofficeFeedFactoryInconsistencyAdministrator,
                            Roles.BackofficeFeedFactoryInconsistencyUser),
                       id: "sidebarFeedFactoryInconsistencyReport"
                       ),
                     },
                            id: "sidebarFoodManufacturing"
                  )));
                        }
                        else
                        {
                            @(await Html.RenderComponentAsync<SidenavItem>(
                            RenderMode.Static,
                            new SidenavItem(
                            title: localizer[Lang.BtnFoodManufacturing],
                            icon: "feed",
                            "#",
                            visible: operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeFeedFactoryAdministrator,
                            Roles.BackofficeFeedFactoryProductionReportAdministrator,
                            Roles.BackofficeFeedFactoryFormulaAdministrator),
                            new List<SidenavItem>() {
                                new SidenavItem(
                                    title: localizer[Lang.BtnDistributionReport],
                                    icon: "icon-Office",
                                    link: Url.Action("Index", "MaterialDistributionReport", new { area = AreaEnum.FeedFactory }),
                                    visible: operationContext.UserIsInAnyRole(
                                            Roles.BackofficeSuperAdministrator,
                                            Roles.BackofficeFeedFactoryAdministrator,
                                            Roles.BackofficeFeedFactoryProductionReportAdministrator),
                                    id: "sidebarMaterialDistributionReport"
                                   ),
                                new SidenavItem(
                                title: localizer[Lang.BtnFormula],
                                icon: "icon-Office",
                                link: Url.Action("Index", "Formula"),
                                visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeFeedFactoryAdministrator,
                                Roles.BackofficeFeedFactoryProductionReportAdministrator,
                                Roles.BackofficeFeedFactoryFormulaAdministrator),
                                id: "sidebarFormula"
                                )
                            },
                    id: "sidebarFoodManufacturing"
                    )));
                        } }
                    @(await Html.RenderComponentAsync<SidenavItem>(
                               RenderMode.Static,
                               new SidenavItem(
                               title: localizer[Lang.BtnBreeding],
                               icon: "breeding",
                               "#",
                               visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeBreedingAdministrator,
                                   Roles.BackofficeBreedingUser,
                                   Roles.BackofficeBreedingDailyReportsAdministrator,
                                   Roles.BackofficeBreedingDailyReportsUser,
                                   Roles.BackofficeBreedingHenReportAdministrator,
                                   Roles.BackofficeBreedingHenReportUser,
                                   Roles.BackofficeBreedingReportsAdministrator,
                                   Roles.BackofficeBreedingReportsUser,
                                   Roles.BackofficeBreedingHenBatchPerformanceAdministrator,
                                   Roles.BackofficeBreedingHenBatchPerformanceUser,
                                   Roles.BackofficeBreedingGeneticReportAdministrator,
                                   Roles.BackofficeBreedingGeneticReportUser,
                                   Roles.BackofficeBreedingHappeningAdministrator,
                                   Roles.BackofficeBreedingHappeningUser,
                                   Roles.BackofficeBreedingTaskAdministrator,
                                   Roles.BackofficeBreedingTaskUser,
                                   Roles.BackofficeBreedingHenBatchAdministrator,
                                   Roles.BackofficeBreedingHenBatchUser,
                                   Roles.BackofficeHappeningAdministrator,
                                   Roles.BackofficeHappeningUser,
                                   Roles.BackofficeShippingNoteAdministrator,
                                   Roles.BackofficeShippingNoteUser,
                                   Roles.BackofficeMovementReportAdministrator,
                                   Roles.BackofficeMovementReportUser,
                                   Roles.BackofficeStockAdministrator,
                                   Roles.BackofficeInconsistencyUser,
                                   Roles.BackofficeInconsistencyAdministrator,
                                   Roles.BackofficeMaterialAnalysisReportAdministrator,
                                   Roles.BackofficeMaterialAnalysisReportUser,
                                   Roles.BackofficeBreedingStockAdministrator,
                                   Roles.BackofficeBreedingStockUser,
                                   Roles.BackofficeBreedingInconsistencyAdministrator,
                                   Roles.BackofficeBreedingInconsistencyUser,
                                   Roles.BackofficeBreedingMaterialAnalysisReportAdministrator,
                                   Roles.BackofficeBreedingMaterialAnalysisReportUser,
                                   Roles.BackofficeBreedingShippingNoteAdministrator,
                                   Roles.BackofficeDailyReportWithoutDateValidation,
                                   Roles.BackofficeBreedingDashboard,
                                   Roles.BackofficeBreedingBirdMovement,
                                   Roles.BackofficeBreedingBulkLoad,
                                   Roles.BackofficeStockUser,
                                   Roles.HenReportBreedingAdjustmentApprover,
                                   Roles.BackofficeBreedingBirdMovementAdjustmentApprover,
                                   Roles.BackofficeBreedingBenchmark,
                                   Roles.SearaDashboardChartsBreeding),
                               new List<SidenavItem>()
                               {
                        new SidenavItem(
                            title: localizer[Lang.BtnDashboard],
                            icon: "icon-Office",
                            link: Url.Action("Dashboard", "Breeding"),
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeBreedingAdministrator, Roles.BackofficeBreedingDashboard, Roles.SearaDashboardChartsBreeding),
                            id: "sidebarBreedingDashboard"
                        ),
                        new SidenavItem(
                            title: localizer[Lang.BtnMessages],
                            icon: "icon-Office",
                            link: Url.Action("Index", "Message", new { area = AreaEnum.Breeding }),
                            visible:operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeBreedingAdministrator,
                           Roles.BackofficeBreedingUser,
                           Roles.BackofficeBreedingDailyReportsAdministrator,
                           Roles.BackofficeBreedingDailyReportsUser,
                           Roles.BackofficeBreedingHenReportAdministrator,
                           Roles.BackofficeBreedingHenReportUser,
                           Roles.BackofficeBreedingSampleCageReportAdministrator,
                           Roles.BackofficeBreedingSampleCageReportUser,
                           Roles.BackofficeDailyReportWithoutDateValidation,
                           Roles.HenReportBreedingAdjustmentApprover
                           ),
                            id: "sidebarMessageBreeding"
                            ),
                       new SidenavItem(
                       title: localizer[Lang.BtnReport],
                       icon: "icon-Office",
                       link: "#",
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeBreedingAdministrator,
                           Roles.BackofficeBreedingUser,
                           Roles.BackofficeBreedingDailyReportsAdministrator,
                           Roles.BackofficeBreedingDailyReportsUser,
                           Roles.BackofficeBreedingHenReportAdministrator,
                           Roles.BackofficeBreedingHenReportUser,
                           Roles.BackofficeBreedingSampleCageReportAdministrator,
                           Roles.BackofficeBreedingSampleCageReportUser,
                           Roles.BackofficeDailyReportWithoutDateValidation,
                           Roles.HenReportBreedingAdjustmentApprover
                           ),
                       new List<SidenavItem>(){
                                new SidenavItem(
                                title: localizer[Lang.BtnOperational],
                                icon: "icon-Office",
                                link: Url.Action("Index", "HenReport", new { henStage = HenStage.Breeding}),
                                visible: operationContext.UserIsInAnyRole(
                                                               Roles.BackofficeSuperAdministrator,
                                                               Roles.BackofficeBreedingAdministrator,
                                                               Roles.BackofficeBreedingUser,
                                                               Roles.BackofficeBreedingDailyReportsAdministrator,
                                                               Roles.BackofficeBreedingDailyReportsUser,
                                                               Roles.BackofficeBreedingHenReportAdministrator,
                                                               Roles.BackofficeBreedingHenReportUser,
                                                               Roles.BackofficeDailyReportWithoutDateValidation,
                                                               Roles.HenReportBreedingAdjustmentApprover),
                                id: "sidebarBreedingHenReport"
                               ),
                                 new SidenavItem(
                                title: localizer[Lang.BtnRectification],
                                icon: "icon-Office",
                                link: Url.Action("Index", "ReportRectification", new { henStage = HenStage.Breeding}),
                                visible: operationContext.UserIsInAnyRole(
                                                               Roles.BackofficeSuperAdministrator,
                                                               Roles.BackofficeBreedingAdministrator,
                                                               Roles.BackofficeBreedingUser,
                                                               Roles.BackofficeBreedingDailyReportsAdministrator,
                                                               Roles.BackofficeBreedingDailyReportsUser,
                                                               Roles.BackofficeBreedingHenReportAdministrator,
                                                               Roles.BackofficeBreedingHenReportUser,
                                                               Roles.BackofficeDailyReportWithoutDateValidation,
                                                               Roles.HenReportBreedingAdjustmentApprover),
                                id: "sidebarBreedingReportRectification"
                               ),

                                new SidenavItem(
                                title: localizer[Lang.BtnWeight],
                                icon: "icon-Office",
                                link: Url.Action("Index", "SampleCageReport", new { henStage = HenStage.Breeding}),
                                visible: operationContext.UserIsInAnyRole(
                                                               Roles.BackofficeSuperAdministrator,
                                                               Roles.BackofficeBreedingAdministrator,
                                                               Roles.BackofficeBreedingUser,
                                                               Roles.BackofficeBreedingDailyReportsAdministrator,
                                                               Roles.BackofficeBreedingDailyReportsUser,
                                                               Roles.BackofficeBreedingSampleCageReportAdministrator,
                                                               Roles.BackofficeBreedingSampleCageReportUser),
                                id: "sidebarBreedingSampleCageReport"),
                           new SidenavItem(
                                title: localizer[Lang.BtnBulkLoad],
                                icon: "icon-Office",
                                link: Url.Action("BulkLoads", "HenReport", new { henStage = HenStage.Breeding}),
                                visible: operationContext.UserIsInAnyRole(
                                                               Roles.BackofficeSuperAdministrator,
                                                               Roles.BackofficeBreedingBulkLoad),
                                id: "sidebarBreedingBulkLoads")

                       },
                       id:"sidebarBreedingReport"
                       ),
                                              new SidenavItem(
                       title: localizer[Lang.BtnManagerial],
                       icon: "icon-Office",
                       link: "#",
                       visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeBreedingAdministrator,
                                   Roles.BackofficeBreedingUser,
                                   Roles.BackofficeBreedingReportsAdministrator,
                                   Roles.BackofficeBreedingReportsUser,
                                   Roles.BackofficeBreedingHenBatchPerformanceAdministrator,
                                   Roles.BackofficeBreedingHenBatchPerformanceUser,
                                   Roles.BackofficeBreedingGeneticReportAdministrator,
                                   Roles.BackofficeBreedingGeneticReportUser,
                                   Roles.BackofficeBreedingBenchmark,
                                   Roles.ManagerialDashboardBreeding,
                                   Roles.ManagerialInventoryBreeding),
                       new List<SidenavItem>(){
                        new SidenavItem(
                            title: localizer[Lang.BtnDashboard],
                            icon: "icon-Office",
                            link: Url.Action("Dashboard", "ManagerialBreeding"),
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                            Roles.ManagerialDashboardBreeding),
                            id: "sidebarManagerialDashboard"
                        ),
                        new SidenavItem(
                            title: localizer[Lang.BtnWeightUniformityReport],
                            icon: "icon-Office",
                            link: Url.Action("Report", "WeightUniformityReport", new { henStage = AreaEnum.Breeding }),
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeBreedingAdministrator),
                            id: "sidebarWeightUniformityReport"
                        ),

                        new SidenavItem(
                            title: localizer[Lang.BtnManagerialInventory],
                            icon: "icon-Office",
                            link: Url.Action("Inventory", "ManagerialBreeding", new { area = AreaEnum.Breeding }), 
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, 
                                Roles.ManagerialInventoryBreeding), // TODO: verificar as Roles
                            id: "sidebarManagerialInventory"
                        ),

                       },
                       id: "sidebarBreedingReports"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnReports],
                       icon: "icon-Office",
                       link: "#",
                       visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeBreedingAdministrator,
                                   Roles.BackofficeBreedingUser,
                                   Roles.BackofficeBreedingReportsAdministrator,
                                   Roles.BackofficeBreedingReportsUser,
                                   Roles.BackofficeBreedingHenBatchPerformanceAdministrator,
                                   Roles.BackofficeBreedingHenBatchPerformanceUser,
                                   Roles.BackofficeBreedingGeneticReportAdministrator,
                                   Roles.BackofficeBreedingGeneticReportUser,
                                   Roles.BackofficeBreedingBenchmark),
                       new List<SidenavItem>(){
        new SidenavItem(
        title: localizer[Lang.BtnIndicators],
        icon: "icon-Office",
        link: Url.Action("Index", "Indicators", new { henStage = HenStage.Breeding }),
        visible: operationContext.UserIsInAnyRole(
            Roles.BackofficeSuperAdministrator,
            Roles.BackofficeBreedingAdministrator,
            Roles.BackofficeBreedingUser,
            Roles.BackofficeBreedingReportsAdministrator,
            Roles.BackofficeBreedingReportsUser,
            Roles.BackofficeBreedingHenBatchPerformanceAdministrator,
            Roles.BackofficeBreedingHenBatchPerformanceUser,
            Roles.BackofficeBreedingGeneticReportAdministrator,
            Roles.BackofficeBreedingGeneticReportUser),
        id: "sidebarBreedingIndicators"
        ),
        new SidenavItem(
        title: localizer[Lang.BtnAnalytics],
        icon: "icon-Office",
        link: Url.Action("Index", "Analytics", new { area = AreaEnum.Breeding }),
        visible: operationContext.UserIsInAnyRole(
            Roles.BackofficeSuperAdministrator,
            Roles.BreedingAnalyticsCorporateAdministrator,
            Roles.BreedingAnalyticsPersonalAdministrator,
            Roles.BreedingAnalyticsUser),
        id: "sidebarBreedingAnalytics"
        ),
        new SidenavItem(
        title: localizer[Lang.BtnStatistics],
        icon: "icon-Office",
        link: Url.Action("Index", "OperationStatistics", new {henStage = HenStage.Breeding}),
        visible: operationContext.UserIsInAnyRole(
            Roles.BackofficeSuperAdministrator,
            Roles.BackofficeOperationsPivotGridAdministrator,
            Roles.BackofficeOperationsPivotGridUser),
        id: "sidebarBreedingStatistics"),
        new SidenavItem(
        title: localizer[Lang.BtnMortalityByGender],
        icon: "icon-Office",
        link: Url.Action("Report", "MortalityByGender", new {henStage = HenStage.Breeding}),
        visible: operationContext.UserIsInAnyRole(
            Roles.BackofficeSuperAdministrator,
            Roles.BackofficeBreedingAdministrator,
            Roles.BackofficeBreedingUser,
            Roles.BackofficeBreedingUser,
            Roles.BackofficeBreedingReportsAdministrator,
            Roles.BackofficeBreedingReportsUser,
            Roles.BackofficeBreedingGeneticReportAdministrator,
            Roles.BackofficeBreedingGeneticReportUser),
        id: "sidebarBreedingMortalityByGender"),
        new SidenavItem(
        title: localizer[Lang.BtnGeneticGraphic],
        icon: "icon-Office",
        link: Url.Action("Graphic", "GeneticReport", new {henStage = HenStage.Breeding}),
        visible: operationContext.UserIsInAnyRole(
            Roles.BackofficeSuperAdministrator,
            Roles.BackofficeBreedingAdministrator,
            Roles.BackofficeBreedingUser,
            Roles.BackofficeBreedingUser,
            Roles.BackofficeBreedingReportsAdministrator,
            Roles.BackofficeBreedingReportsUser,
            Roles.BackofficeBreedingGeneticReportAdministrator,
            Roles.BackofficeBreedingGeneticReportUser),
        id: "sidebarBreedingGeneticsStatistics"),
        new SidenavItem(
        title: localizer[Lang.BtnBenchmark],
        icon: "icon-Office",
        link: Url.Action("Benchmark", "GeneticReport", new {henStage = HenStage.Breeding}),
        visible: operationContext.UserIsInAnyRole(
            Roles.BackofficeSuperAdministrator,
            Roles.BackofficeBreedingAdministrator,
            Roles.BackofficeBreedingUser,
            Roles.BackofficeBreedingUser,
            Roles.BackofficeBreedingReportsAdministrator,
            Roles.BackofficeBreedingReportsUser,
            Roles.BackofficeBreedingGeneticReportAdministrator,
            Roles.BackofficeBreedingGeneticReportUser,
            Roles.BackofficeBreedingBenchmark),
        id: "sidebarBreedingBenchmark"),
        new SidenavItem(
        title: localizer[Lang.BtnAudit],
        icon: "icon-Office",
        link: Url.Action("Index", "AuditReport", new {henStage = HenStage.Breeding}),
        visible: operationContext.UserIsInAnyRole(
            Roles.BackofficeSuperAdministrator,
            Roles.BackofficeBreedingAdministrator,
            Roles.BackofficeBreedingReportsAdministrator,
            Roles.BackofficeBreedingReportsUser),
                id: "sidebarBreedingAudit"),
                new SidenavItem(
                title: localizer[Lang.BtnGADPlannerFemaleBreeding],
                icon: "icon-Office",
                link: Url.Action("PlannerFemale", "Planner", new {henStage = HenStage.Breeding}),
                visible: operationContext.UserIsInAnyRole(
                Roles.BackofficeSuperAdministrator,
                Roles.BackofficeLayingAdministrator,
                Roles.BackofficeLayingReportsAdministrator,
                Roles.BackofficeLayingReportsUser,
                Roles.BackofficeBreedingAdministrator,
                Roles.BackofficeBreedingReportsAdministrator,
                Roles.BackofficeBreedingReportsUser),
                id: "sidebar-gad-planner-female-breeding"),
                new SidenavItem(
                title: localizer[Lang.BtnGADPlannerMaleBreeding],
                icon: "icon-Office",
                link: Url.Action("PlannerMale", "Planner", new {henStage = HenStage.Breeding}),
                visible: operationContext.UserIsInAnyRole(
                Roles.BackofficeSuperAdministrator,
                Roles.BackofficeLayingAdministrator,
                Roles.BackofficeLayingReportsAdministrator,
                Roles.BackofficeLayingReportsUser,
                Roles.BackofficeBreedingAdministrator,
                Roles.BackofficeBreedingReportsAdministrator,
                Roles.BackofficeBreedingReportsUser),
                id: "sidebar-gad-planner-male-breeding")
    //new SidenavItem(
    //title: localizer[Lang.BtnWarning],
    //    icon: "icon-Office",
    //    link: Url.Action("Index", "Warning", new {henStage = HenStage.Breeding}),
    //    visible: operationContext.UserIsInAnyRole(
    //        Roles.BackofficeSuperAdministrator,
    //        Roles.BackofficeBreedingAdministrator,
    //        Roles.BackofficeBreedingUser,
    //        Roles.BackofficeBreedingUser,
    //        Roles.BackofficeBreedingReportsAdministrator,
    //        Roles.BackofficeBreedingReportsUser,
    //        Roles.BackofficeBreedingGeneticReportAdministrator,
    //        Roles.BackofficeBreedingGeneticReportUser),
    //    id: "sidebarBreedingWarning"),
    //   new SidenavItem(
    //    title: localizer[Lang.BtnWarehouseCapacities],
    //    icon: "icon-Office",
    //    link: Url.Action("CapacityAlerts", "HenWarehouse", new { henStage = AreaEnum.Breeding }),
    //    visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
    //    Roles.BackofficeBreedingAdministrator, Roles.BackofficeBreedingDashboard),
    //    id: "sidebarWarehouseBreedingCapacities"
    //    ),
    },
    id: "sidebarBreedingOtherReports"
    ),
    new SidenavItem(
    title: localizer[Lang.BtnBatchs],
    icon: "icon-Office",
    link: Url.Action("Index", "HenBatch", new { henStage = HenStage.Breeding }),
    visible: operationContext.UserIsInAnyRole(
    Roles.BackofficeSuperAdministrator,
    Roles.BackofficeBreedingAdministrator,
    Roles.BackofficeBreedingUser,
    Roles.BackofficeBreedingHenBatchAdministrator,
    Roles.BackofficeBreedingHenBatchUser,
    Roles.BackofficeBreedingBirdMovement,
    Roles.BackofficeBreedingBirdMovementAdjustmentApprover),
    id: "sidebarBreedingHenBatch"
    ),
    new SidenavItem(
    title: localizer[Lang.BtnBiologicalAssets],
    icon: "icon-Office",
    link: $"/Material/{MaterialTypes.ActivoBiologico}?area={AreaEnum.Breeding}",
    visible: operationContext.UserIsInAnyRole(
    Roles.BackofficeSuperAdministrator),
    id: "sidebarBreedingBiologicalAssets"
    ),

    new SidenavItem(
    title: localizer[Lang.BtnStocks],
    icon: "icon-Office",
    link: Url.Action("Index", "Stock", new { area = AreaEnum.Breeding }),
    visible: operationContext.UserIsInAnyRole(
    Roles.BackofficeSuperAdministrator,
    Roles.BackofficeBreedingAdministrator,
    Roles.BackofficeStockUser,
    Roles.BackofficeBreedingStockAdministrator,
    Roles.BackofficeStockAdministrator,
    Roles.BackofficeBreedingStockUser),
    id: "sidebarBreedingStock"
    ),
    new SidenavItem(
    title: localizer[Lang.BtnShippingNotes],
    icon: "icon-Office",
    link: Url.Action("Index", "ShippingNote", new { area = AreaEnum.Breeding }),
    visible: operationContext.UserIsInAnyRole(
    Roles.BackofficeSuperAdministrator,
    Roles.BackofficeBreedingAdministrator,
    Roles.BackofficeBreedingUser,
    Roles.BackofficeShippingNoteAdministrator,
    Roles.BackofficeShippingNoteUser,
    Roles.BackofficeBreedingShippingNoteAdministrator),
    id: "sidebarBreedingMovement"
    ),
    new SidenavItem(
    title: localizer[Lang.BtnBestPractices],
    icon: "icon-Office",
    link: Url.Action("Index", "BestPractice", new {area = AreaEnum.Breeding}),
    visible: operationContext.UserIsInAnyRole(
        Roles.BackofficeSuperAdministrator,
        Roles.BackofficeHealthCareBestPracticeUser,
        Roles.BackofficeHealthCareBestPracticeAdministrator,
        Roles.BackofficeHealthCareAdministrator)
    ),
    new SidenavItem(
    title: localizer[Lang.BtnHappening],
    icon: "icon-Office",
    link: Url.Action("Index", "Happening", new { area = AreaEnum.Breeding }),
    visible: operationContext.UserIsInAnyRole(
    Roles.BackofficeSuperAdministrator,
    Roles.BackofficeBreedingAdministrator,
    Roles.BackofficeBreedingHappeningAdministrator,
    Roles.BackofficeBreedingHappeningUser,
    Roles.BackofficeHappeningUser,
    Roles.BackofficeHappeningAdministrator),
    id: "sidebarBreedingHappening"
    ),
    new SidenavItem(
    title: localizer[Lang.BtnSampleMaterial],
    icon: "icon-Office",
    link: Url.Action("Index", "MaterialAnalysisReport", new { area = AreaEnum.Breeding }),
    visible: operationContext.UserIsInAnyRole(
    Roles.BackofficeSuperAdministrator,
    Roles.BackofficeBreedingAdministrator,
    Roles.BackofficeBreedingMaterialAnalysisReportUser,
    Roles.BackofficeBreedingMaterialAnalysisReportAdministrator,
    Roles.BackofficeMaterialAnalysisReportAdministrator,
    Roles.BackofficeMaterialAnalysisReportUser),
    id: "sidebarBreedingMaterialAnalysisReport"
    ),
    new SidenavItem(
    title: localizer[Lang.BtnInconsistencyReport],
    icon: "icon-Office",
    link: Url.Action("Index", "InconsistencyReport", new { area = AreaEnum.Breeding }),
    visible: operationContext.UserIsInAnyRole(
    Roles.BackofficeSuperAdministrator,
    Roles.BackofficeInconsistencyAdministrator,
    Roles.BackofficeInconsistencyUser,
    Roles.BackofficeBreedingAdministrator,
    Roles.BackofficeBreedingInconsistencyAdministrator,
    Roles.BackofficeBreedingInconsistencyUser),
    id: "sidebarBreedingInconsistencyReport"
    ),
    },
    id: "sidebarBreeding"
    )))
                    @(await Html.RenderComponentAsync<SidenavItem>(
                      RenderMode.Static,
                      new SidenavItem(
                      title: localizer[Lang.BtnLaying],
                      icon: "laying",
                      "#",
                      visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeLayingAdministrator,
                           Roles.BackofficeLayingUser,
                           Roles.BackofficeStockUser,
                           Roles.BackofficeLayingDailyReportsAdministrator,
                           Roles.BackofficeLayingDailyReportsUser,
                           Roles.BackofficeLayingHenReportAdministrator,
                           Roles.BackofficeLayingHenReportUser,
                           Roles.BackofficeLayingReportsAdministrator,
                           Roles.BackofficeLayingReportsUser,
                           Roles.BackofficeLayingHenBatchPerformanceAdministrator,
                           Roles.BackofficeLayingHenBatchPerformanceUser,
                           Roles.BackofficeLayingGeneticReportAdministrator,
                           Roles.BackofficeLayingGeneticReportUser,
                           Roles.BackofficeLayingHappeningAdministrator,
                           Roles.BackofficeLayingHappeningUser,
                           Roles.BackofficeLayingHenBatchAdministrator,
                           Roles.BackofficeLayingHenBatchUser,
                           Roles.BackofficeLayingHappeningUser,
                           Roles.BackofficeLayingHappeningAdministrator,
                           Roles.BackofficeLayingStockAdministrator,
                           Roles.BackofficeLayingStockUser,
                           Roles.BackofficeLayingInconsistencyAdministrator,
                           Roles.BackofficeLayingInconsistencyUser,
                           Roles.BackofficeLayingMaterialAnalysisReportAdministrator,
                           Roles.BackofficeLayingMaterialAnalysisReportUser,
                           Roles.BackofficeHappeningAdministrator,
                           Roles.BackofficeHappeningUser,
                           Roles.BackofficeShippingNoteAdministrator,
                           Roles.BackofficeShippingNoteUser,
                           Roles.BackofficeMovementReportAdministrator,
                           Roles.BackofficeMovementReportUser,
                           Roles.BackofficeStockAdministrator,
                           Roles.BackofficeInconsistencyUser,
                           Roles.BackofficeInconsistencyAdministrator,
                           Roles.BackofficeMaterialAnalysisReportAdministrator,
                           Roles.BackofficeMaterialAnalysisReportUser,
                           Roles.BackofficeLayingShippingNoteAdministrator,
                           Roles.BackofficeDailyReportWithoutDateValidation,
                           Roles.BackofficeLayingDashboard,
                           Roles.BackofficeLayingBirdMovement,
                           Roles.BackofficeLayingBulkLoad,
                           Roles.HenReportLayingAdjustmentApprover,
                           Roles.BackofficeLayingBirdMovementAdjustmentApprover,
                           Roles.BackofficeLayingBenchmark),
                           new List<SidenavItem>()
                      {
                        new SidenavItem(
                            title: localizer[Lang.BtnDashboard],
                            icon: "icon-Office",
                            link: Url.Action("Dashboard", "Laying"),
                            visible: operationContext.UserIsInAnyRole(
                             Roles.BackofficeSuperAdministrator,
                             Roles.BackofficeLayingAdministrator,
                             Roles.BackofficeLayingDashboard),
                            id: "sidebarLayingDashboard"
                        ),
                        new SidenavItem(
                            title: localizer[Lang.BtnMessages],
                            icon: "icon-Office",
                            link: Url.Action("Index", "Message", new { area = AreaEnum.Laying }),
                            visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeLayingAdministrator,
                           Roles.BackofficeLayingUser,
                           Roles.BackofficeLayingDailyReportsAdministrator,
                           Roles.BackofficeLayingDailyReportsUser,
                           Roles.BackofficeLayingHenReportAdministrator,
                           Roles.BackofficeLayingHenReportUser,
                           Roles.BackofficeLayingSampleCageReportAdministrator,
                           Roles.BackofficeLayingSampleCageReportUser,
                           Roles.BackofficeDailyReportWithoutDateValidation,
                           Roles.HenReportLayingAdjustmentApprover
                           ),
                            id: "sidebarLayingMessage"
                            ),
                       new SidenavItem(
                       title: localizer[Lang.BtnReport],
                       icon: "icon-Office",
                       link: "#",
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeLayingAdministrator,
                           Roles.BackofficeLayingUser,
                           Roles.BackofficeLayingDailyReportsAdministrator,
                           Roles.BackofficeLayingDailyReportsUser,
                           Roles.BackofficeLayingHenReportAdministrator,
                           Roles.BackofficeLayingHenReportUser,
                           Roles.BackofficeLayingSampleCageReportAdministrator,
                           Roles.BackofficeLayingSampleCageReportUser,
                           Roles.BackofficeDailyReportWithoutDateValidation,
                           Roles.HenReportLayingAdjustmentApprover),
                       new List<SidenavItem>(){
                                new SidenavItem(
                                title: localizer[Lang.BtnOperational],
                                icon: "icon-Office",
                                link: Url.Action("Index", "HenReport", new { henStage = HenStage.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                                               Roles.BackofficeSuperAdministrator,
                                                               Roles.BackofficeLayingAdministrator,
                                                               Roles.BackofficeLayingUser,
                                                               Roles.BackofficeLayingDailyReportsAdministrator,
                                                               Roles.BackofficeLayingDailyReportsUser,
                                                               Roles.BackofficeLayingHenReportAdministrator,
                                                               Roles.BackofficeLayingHenReportUser,
                                                               Roles.BackofficeDailyReportWithoutDateValidation,
                                                               Roles.HenReportLayingAdjustmentApprover),
                                id: "sidebarLayingHenReport"
                               ),
                                new SidenavItem(
                                title: localizer[Lang.BtnRectification],
                                icon: "icon-Office",
                                link: Url.Action("Index", "ReportRectification", new { henStage = HenStage.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                                               Roles.BackofficeSuperAdministrator,
                                                               Roles.BackofficeLayingAdministrator,
                                                               Roles.BackofficeLayingUser,
                                                               Roles.BackofficeLayingDailyReportsAdministrator,
                                                               Roles.BackofficeLayingDailyReportsUser,
                                                               Roles.BackofficeLayingHenReportAdministrator,
                                                               Roles.BackofficeLayingHenReportUser,
                                                               Roles.BackofficeDailyReportWithoutDateValidation,
                                                               Roles.HenReportLayingAdjustmentApprover),
                                id: "sidebarBreedingReportRectification"
                               ),
                                new SidenavItem(
                                title: localizer[Lang.BtnWeight],
                                icon: "icon-Office",
                                link: Url.Action("Index", "SampleCageReport", new { henStage = HenStage.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                                               Roles.BackofficeSuperAdministrator,
                                                               Roles.BackofficeLayingAdministrator,
                                                               Roles.BackofficeLayingUser,
                                                               Roles.BackofficeLayingDailyReportsAdministrator,
                                                               Roles.BackofficeLayingDailyReportsUser,
                                                               Roles.BackofficeLayingSampleCageReportAdministrator,
                                                               Roles.BackofficeLayingSampleCageReportUser),
                                id: "sidebarLayingSampleCageReport"
                               ),
                                new SidenavItem(
                                title: localizer[Lang.BtnEggWeight],
                                icon: "icon-Office",
                                link: Url.Action("Index", "EggWeightReport", new { }),
                                visible: operationContext.UserIsInAnyRole(
                                                               Roles.BackofficeSuperAdministrator,
                                                               Roles.BackofficeLayingAdministrator,
                                                               Roles.BackofficeLayingUser,
                                                               Roles.BackofficeLayingDailyReportsAdministrator,
                                                               Roles.BackofficeLayingDailyReportsUser,
                                                               Roles.BackofficeLayingSampleCageReportAdministrator,
                                                               Roles.BackofficeLayingSampleCageReportUser),
                                id: "sidebarEggWeightReport"
                               ),
                                new SidenavItem(
                                title: localizer[Lang.BtnBulkLoad],
                                icon: "icon-Office",
                                link: Url.Action("BulkLoads", "HenReport", new { henStage = HenStage.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                                               Roles.BackofficeSuperAdministrator,
                                                               Roles.BackofficeLayingBulkLoad),
                                id: "sidebarLayingBulkLoads")},
                          id: "sidebarLayingReport"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnManagerial],
                       icon: "icon-Office",
                       link: "#",
                       visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeLayingUser,
                                   Roles.BackofficeLayingReportsAdministrator,
                                   Roles.BackofficeLayingReportsUser,
                                   Roles.BackofficeLayingHenBatchPerformanceAdministrator,
                                   Roles.BackofficeLayingHenBatchPerformanceUser,
                                   Roles.BackofficeLayingGeneticReportAdministrator,
                                   Roles.BackofficeLayingGeneticReportUser,
                                   Roles.BackofficeLayingBenchmark,
                                   Roles.ManagerialDashboardLaying,
                                   Roles.ManagerialInventoryLaying),
                       new List<SidenavItem>(){
                        new SidenavItem(
                            title: localizer[Lang.BtnDashboard],
                            icon: "icon-Office",
                            link: Url.Action("Dashboard", "ManagerialLaying"),
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                            Roles.ManagerialDashboardLaying),
                            id: "sidebarManagerialDashboard"
                        ),
                        new SidenavItem(
                            title: "Relatório Gerencial",
                            icon: "icon-Office",
                            link: Url.Action("Report", "ManagerialLayingReport"),
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator),
                            id: "sidebarManagerialLayingReport"
                        ),
                         new SidenavItem(
                            title: localizer[Lang.BtnManagerialInventory],
                            icon: "icon-Office",
                            link: Url.Action("Inventory", "ManagerialLaying", new { area = AreaEnum.Laying }), 
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, 
                                Roles.ManagerialDashboardLaying), // TODO: verificar as Roles
                            id: "sidebarManagerialInventory"
                        ),
                       },
                       id: "sidebarLayingReports"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnReports],
                       icon: "icon-Office",
                       link: "#",
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingUser,
                            Roles.BackofficeLayingReportsAdministrator,
                            Roles.BackofficeLayingReportsUser,
                            Roles.BackofficeLayingHenBatchPerformanceAdministrator,
                            Roles.BackofficeLayingHenBatchPerformanceUser,
                            Roles.BackofficeLayingGeneticReportAdministrator,
                            Roles.BackofficeLayingGeneticReportUser,
                            Roles.BackofficeLayingBenchmark),
                       new List<SidenavItem>(){
                               new SidenavItem(
                                title: localizer[Lang.BtnIndicators],
                                icon: "icon-Office",
                                link: Url.Action("Index", "Indicators", new { henStage = HenStage.Laying }),
                                visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeLayingAdministrator,
                                Roles.BackofficeLayingUser,
                                Roles.BackofficeLayingReportsAdministrator,
                                Roles.BackofficeLayingReportsUser,
                                Roles.BackofficeLayingHenBatchPerformanceAdministrator,
                                Roles.BackofficeLayingHenBatchPerformanceUser,
                                Roles.BackofficeLayingGeneticReportAdministrator,
                                Roles.BackofficeLayingGeneticReportUser),
                                id: "sidebarLayingIndicators"
                                ),
                               new SidenavItem(
                               title: localizer[Lang.BtnAnalytics],
                               icon: "icon-Office",
                               link: Url.Action("Index", "Analytics", new { area = AreaEnum.Laying }),
                               visible: operationContext.UserIsInAnyRole(
                               Roles.BackofficeSuperAdministrator,
                               Roles.LayingAnalyticsCorporateAdministrator,
                               Roles.LayingAnalyticsPersonalAdministrator,
                               Roles.LayingAnalyticsUser),
                               id: "sidebarLayingAnalytics"
                                ),
                               new SidenavItem(
                                title: localizer[Lang.BtnStatistics],
                                icon: "icon-Office",
                                link: Url.Action("Index", "OperationStatistics", new {henStage = HenStage.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeOperationsPivotGridAdministrator,
                                Roles.BackofficeOperationsPivotGridUser),
                                id: "sidebarLayingStatistics"
                                ),
                               new SidenavItem(
                                title: localizer[Lang.BtnEggProductionByCategory],
                                icon: "icon-Office",
                                link: Url.Action("Report", "EggProductionByCategory"),
                                visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeLayingUser,
                                   Roles.BackofficeLayingReportsAdministrator,
                                   Roles.BackofficeLayingReportsUser,
                                   Roles.BackofficeLayingGeneticReportAdministrator,
                                   Roles.BackofficeLayingGeneticReportUser),
                               id: "sidebarLayingEggProductionByCategory"),
                               new SidenavItem(
                                title: localizer[Lang.BtnMortalityByGender],
                                icon: "icon-Office",
                                link: Url.Action("Report", "MortalityByGender", new {henStage = HenStage.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeLayingUser,
                                   Roles.BackofficeLayingReportsAdministrator,
                                   Roles.BackofficeLayingReportsUser,
                                   Roles.BackofficeLayingGeneticReportAdministrator,
                                   Roles.BackofficeLayingGeneticReportUser),
                               id: "sidebarLayingMortalityByGender"),
                               new SidenavItem(
                                title: localizer[Lang.BtnGeneticGraphic],
                                icon: "icon-Office",
                                link: Url.Action("Graphic", "GeneticReport", new {henStage = HenStage.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeLayingUser,
                                   Roles.BackofficeLayingReportsAdministrator,
                                   Roles.BackofficeLayingReportsUser,
                                   Roles.BackofficeLayingGeneticReportAdministrator,
                                   Roles.BackofficeLayingGeneticReportUser),
                               id: "sidebarLayingGeneticsStatistics"),
                               // Cannot go into MASTER until testing is finished.
                               new SidenavItem(
                                title: localizer[Lang.BtnBenchmark],
                                icon: "icon-Office",
                                link: Url.Action("Benchmark", "GeneticReport", new {henStage = HenStage.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeLayingUser,
                                   Roles.BackofficeLayingReportsAdministrator,
                                   Roles.BackofficeLayingReportsUser,
                                   Roles.BackofficeLayingGeneticReportAdministrator,
                                   Roles.BackofficeLayingGeneticReportUser,
                                   Roles.BackofficeLayingBenchmark),
                               id: "sidebarLayingBenchmark"),
                                new SidenavItem(
                                title: localizer[Lang.BtnAudit],
                                icon: "icon-Office",
                                link: Url.Action("Index", "AuditReport", new {henStage = HenStage.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeLayingReportsAdministrator,
                                   Roles.BackofficeLayingReportsUser),
                                id: "sidebarLayingAudit"),
                               new SidenavItem(
                                   title: localizer[Lang.BtnGADPlannerFemaleLaying],
                                   icon: "icon-Office",
                                   link: Url.Action("PlannerFemale", "Planner", new {henStage = HenStage.Laying}),
                                   visible: operationContext.UserIsInAnyRole(
                                      Roles.BackofficeSuperAdministrator,
                                      Roles.BackofficeLayingAdministrator,
                                      Roles.BackofficeLayingReportsAdministrator,
                                      Roles.BackofficeLayingReportsUser),
                                   id: "sidebar-gad-planner-female-laying"),
                               new SidenavItem(
                                   title: localizer[Lang.BtnGADPlannerMaleLaying],
                                   icon: "icon-Office",
                                   link: Url.Action("PlannerMale", "Planner", new {henStage = HenStage.Laying}),
                                   visible: operationContext.UserIsInAnyRole(
                                      Roles.BackofficeSuperAdministrator,
                                      Roles.BackofficeLayingAdministrator,
                                      Roles.BackofficeLayingReportsAdministrator,
                                      Roles.BackofficeLayingReportsUser),
                                   id: "sidebar-gad-planner-male-laying")
                        //        new SidenavItem(
                        //            title: localizer[Lang.BtnWarning],
                        //            icon: "icon-Office",
                        //            link: Url.Action("Index", "Warning", new {henStage = HenStage.Laying}),
                        //            visible: operationContext.UserIsInAnyRole(
                        //                Roles.BackofficeSuperAdministrator,
                        //                Roles.BackofficeLayingAdministrator,
                        //                Roles.BackofficeLayingUser,
                        //                Roles.BackofficeLayingUser,
                        //                Roles.BackofficeLayingReportsAdministrator,
                        //                Roles.BackofficeLayingReportsUser,
                        //                Roles.BackofficeLayingGeneticReportAdministrator,
                        //                Roles.BackofficeLayingGeneticReportUser),
                        //            id: "sidebarLayingWarning"),
                        //new SidenavItem(
                        //    title: localizer[Lang.BtnWarehouseCapacities],
                        //    icon: "icon-Office",
                        //    link: Url.Action("CapacityAlerts", "HenWarehouse", new { henStage = AreaEnum.Laying }),
                        //    visible: operationContext.UserIsInAnyRole(
                        //     Roles.BackofficeSuperAdministrator,
                        //     Roles.BackofficeLayingAdministrator,
                        //     Roles.BackofficeLayingDashboard),
                        //    id: "sidebarWarehouseLayingCapacities"
                        //),
                       },
                       id: "sidebarLayingOtherReports"
                       ),
                       new SidenavItem(
                               title: localizer[Lang.BtnBatchs],
                               icon: "icon-Office",
                               link: Url.Action("Index", "HenBatch", new { henStage = HenStage.Laying }),
                               visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeLayingUser,
                                   Roles.BackofficeLayingHenBatchAdministrator,
                                   Roles.BackofficeLayingHenBatchUser,
                                   Roles.BackofficeLayingBirdMovement,
                                   Roles.BackofficeLayingBirdMovementAdjustmentApprover),
                               id: "sidebarLayingHenBatch"
                              ),
                               new SidenavItem(
                               title: localizer[Lang.BtnBiologicalAssets],
                               icon: "icon-Office",
                               link: $"/Material/{MaterialTypes.ActivoBiologico}?area={AreaEnum.Laying}",
                               visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator),
                               id: "sidebarLayingBiologicalAssets"
                              ),

                              new SidenavItem(
                               title: localizer[Lang.BtnUnclassifiedEggs],
                               icon: "icon-Office",
                               link: $"/Material/{MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificar}?area={AreaEnum.Laying}",
                               visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator),
                              id: "sidebarLayingUnclassifiedEggsSupplies"
                           ),

                           new SidenavItem(
                               title: localizer[Lang.BtnStocks],
                               icon: "icon-Office",
                               link: "#",
                               visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeStockAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeStockUser,
                                   Roles.BackofficeLayingStockAdministrator,
                                   Roles.BackofficeLayingStockUser),
                               new List<SidenavItem>()
                               {
                                   new SidenavItem(
                                       title: localizer[Lang.BtnGeneralStocks],
                                       icon: "icon-Office",
                                       link: Url.Action("Index", "Stock", new { area = AreaEnum.Laying }),
                                       visible: operationContext.UserIsInAnyRole(
                                           Roles.BackofficeSuperAdministrator,
                                           Roles.BackofficeStockAdministrator,
                                           Roles.BackofficeLayingAdministrator,
                                           Roles.BackofficeStockUser,
                                           Roles.BackofficeLayingStockAdministrator,
                                           Roles.BackofficeLayingStockUser),
                                       id: "sidebarLayingGeneralStock"
                                   ),
                                   new SidenavItem(
                                       title: localizer[Lang.BtnEggStocks],
                                       icon: "icon-Office",
                                       link: Url.Action("Index", "EggStock", new { area = AreaEnum.Laying }),
                                       visible: operationContext.UserIsInAnyRole(
                                           Roles.BackofficeSuperAdministrator,
                                           Roles.BackofficeLayingAdministrator,
                                           Roles.BackofficeLayingStockAdministrator,
                                           Roles.BackofficeLayingStockUser),
                                       id: "sidebarLayingEggStock"
                                   ),
                               },
                               id: "sidebarLayingOtherStock"
                            ),
                               new SidenavItem(
                               title: localizer[Lang.BtnEggsMovements],
                               icon: "icon-Office",
                               link: Url.Action("Index", "MaterialExitReport", new { area = AreaEnum.Laying, destinationArea = AreaEnum.Classification }),
                               visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeStockAdministrator,
                                   Roles.BackofficeLayingStockAdministrator,
                                   Roles.BackofficeLayingStockUser,
                                   Roles.BackofficeStockUser),
                               id: "sidebarLayingStock"
                              ),
                               new SidenavItem(
                               title: localizer[Lang.BtnShippingNotes],
                               icon: "icon-Office",
                               link: Url.Action("Index", "ShippingNote", new { area = AreaEnum.Laying }),
                               visible: operationContext.UserIsInAnyRole(
                                    Roles.BackofficeSuperAdministrator,
                                    Roles.BackofficeLayingAdministrator,
                                    Roles.BackofficeLayingUser,
                                    Roles.BackofficeShippingNoteAdministrator,
                                    Roles.BackofficeShippingNoteUser,
                                    Roles.BackofficeLayingShippingNoteAdministrator),        
                               id: "sidebarLayingMovement"
                              ),
                                new SidenavItem(
                                title: localizer[Lang.BtnBestPractices],
                                icon: "icon-Office",
                                link: Url.Action("Index", "BestPractice", new {area = AreaEnum.Laying}),
                                visible: operationContext.UserIsInAnyRole(
                                    Roles.BackofficeSuperAdministrator,
                                    Roles.BackofficeHealthCareBestPracticeUser,
                                    Roles.BackofficeHealthCareBestPracticeAdministrator,
                                    Roles.BackofficeHealthCareAdministrator)
                                ),
                               new SidenavItem(
                                   title: localizer[Lang.BtnHappening],
                            icon: "icon-Office",
                            link: Url.Action("Index", "Happening", new { area = AreaEnum.Laying }),
                            visible: operationContext.UserIsInAnyRole(
                                   Roles.BackofficeSuperAdministrator,
                                   Roles.BackofficeLayingAdministrator,
                                   Roles.BackofficeHappeningUser,
                                   Roles.BackofficeHappeningAdministrator,
                                   Roles.BackofficeLayingHappeningAdministrator,
                                   Roles.BackofficeLayingHappeningUser),
                            id: "sidebarLayingHappening"
                            ),
                            new SidenavItem(
                            title: localizer[Lang.BtnSampleMaterial],
                            icon: "icon-Office",
                            link: Url.Action("Index", "MaterialAnalysisReport", new { area = AreaEnum.Laying }),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeLayingAdministrator,
                                Roles.BackofficeMaterialAnalysisReportAdministrator,
                                Roles.BackofficeMaterialAnalysisReportUser,
                                Roles.BackofficeLayingMaterialAnalysisReportUser,
                                Roles.BackofficeLayingMaterialAnalysisReportAdministrator),
                            id: "sidebarLayingMaterialAnalysisReport"
                            ),
                            new SidenavItem(
                            title: localizer[Lang.BtnInconsistencyReport],
                            icon: "icon-Office",
                            link: Url.Action("Index", "InconsistencyReport", new { area = AreaEnum.Laying }),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeInconsistencyAdministrator,
                                Roles.BackofficeInconsistencyUser,
                                Roles.BackofficeLayingAdministrator,
                                Roles.BackofficeLayingInconsistencyUser,
                                Roles.BackofficeLayingInconsistencyAdministrator),
                            id: "sidebarLayingInconsistencyReport"
                            ),
               },
                       id: "sidebarLaying"
            )))
                    @{ if (automaticallySentEggsConfiguration)
                        {

                            @(await Html.RenderComponentAsync<SidenavItem>(
                                   RenderMode.Static,
                                   new SidenavItem(
                                   title: localizer[Lang.BtnClassificationWarehouse],
                                   icon: "classification",
                                   "#",
                                   visible: operationContext.UserIsInAnyRole(
                                       Roles.BackofficeSuperAdministrator,
                                       Roles.BackofficeClassificationHappeningAdministrator,
                                       Roles.BackofficeClassificationHappeningUser,
                                       Roles.BackofficeClassificationInconsistencyAdministrator,
                                       Roles.BackofficeClassificationInconsistencyUser,
                                       Roles.BackofficeClassificationMaterialAnalysisReportAdministrator,
                                       Roles.BackofficeClassificationMaterialAnalysisReportUser,
                                       Roles.BackofficeClassificationShippingNoteAdministrator,
                                       Roles.BackofficeClassificationShippingNoteUser,
                                       Roles.BackofficeClassificationStockAdministrator,
                                       Roles.BackofficeClassificationStockUser,
                                       Roles.BackofficeHappeningAdministrator,
                                       Roles.BackofficeHappeningUser,
                                       Roles.BackofficeShippingNoteAdministrator,
                                       Roles.BackofficeShippingNoteUser,
                                       Roles.BackofficeMovementReportAdministrator,
                                       Roles.BackofficeMovementReportUser,
                                       Roles.BackofficeInconsistencyUser,
                                       Roles.BackofficeStockUser,
                                       Roles.BackofficeInconsistencyAdministrator,
                                       Roles.BackofficeMaterialAnalysisReportAdministrator,
                                       Roles.BackofficeMaterialAnalysisReportUser,
                                       Roles.BackofficeClassificationHappeningUser,
                                       Roles.BackofficeClassificationShippingNoteUser,
                                       Roles.BackofficeClassificationStockUser,
                                       Roles.BackofficeClassificationStockAdministrator,
                                       Roles.BackofficeClassificationShippingNoteAdministrator,
                                       Roles.BackofficeClassificationHappeningAdministrator,
                                       Roles.BackofficeClassificationInconsistencyAdministrator,
                                       Roles.BackofficeClassificationInconsistencyUser,
                                       Roles.BackofficeClassificationMaterialAnalysisReportAdministrator,
                                       Roles.BackofficeClassificationMaterialAnalysisReportUser,
                                       Roles.BackofficeClassificationAdministrator,
                                       Roles.BackofficeClassificationUser,
                                       Roles.BackofficeClassificationReportAdministrator,
                                       Roles.BackofficeClassificationReportUser,
                                       Roles.BackofficeClassificationReportWithoutDateValidation,
                                       Roles.BackofficeAgentUser,
                                       Roles.BackofficeStockAdministrator),
                                   new List<SidenavItem>()
                                   {
                                       new SidenavItem(
                                        title: localizer[Lang.BtnClassificationDashboard],
                                        icon: "icon-Office",
                                        link: Url.Action("Dashboard", "ClassificationDashboard"),
                                        visible: false,
                                        id: "sidebarClassificationDashboard"
                                        ),
                                        new SidenavItem(
                                        title: localizer[Lang.BtnOtherReports],
                                        icon: "icon-Office",
                                        link: "#",
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                        Roles.BackofficeClassificationReportAdministrator,
                                        Roles.BackofficeClassificationReportUser, Roles.BackofficeClassificationUser),
                                        new List<SidenavItem>(){
                                            new SidenavItem(
                                                title: localizer[Lang.BtnHatcheringReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Index", "HatcheringReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                Roles.BackofficeClassificationReportAdministrator,
                                                Roles.BackofficeClassificationReportUser, Roles.BackofficeClassificationUser),
                                                id: "sidebarClassificationHatcheringReport"
                                                ),
                                            new SidenavItem(
                                                title: localizer[Lang.BtnFertilityReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Index", "FertilityReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                Roles.BackofficeClassificationReportAdministrator,
                                                Roles.BackofficeClassificationReportUser, Roles.BackofficeClassificationUser),
                                                id: "sidebarClassificationFertilityReport"
                                                ),
                                            new SidenavItem(
                                                title: localizer[Lang.BtnEggQualityReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Index", "EggQualityReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                Roles.BackofficeClassificationReportAdministrator,
                                                Roles.BackofficeClassificationReportUser, Roles.BackofficeClassificationUser),
                                                id: "sidebarClassificationEggQualityReport"
                                                ),
                                            new SidenavItem(
                                                title: localizer[Lang.BtnEggDensityReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Index", "EggDensityReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                Roles.BackofficeClassificationReportAdministrator,
                                                Roles.BackofficeClassificationReportUser, Roles.BackofficeClassificationUser),
                                                id: "sidebarClassificationEggDensityReport"
                                                ),
                                        },
                                            id: "sidebarClassificationReports"
                                        ),


                                        new SidenavItem(
                                        title: localizer[Lang.BtnManualLoading],
                                        icon: "icon-Office",
                                        link: "#",
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                            Roles.BackofficeClassificationAdministrator,
                                            Roles.BackofficeClassificationReportAdministrator),
                                        new List<SidenavItem>(){
                                            new SidenavItem(
                                                title: localizer[Lang.BtnHatcheringReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Create", "HatcheringReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                    Roles.BackofficeClassificationReportAdministrator),
                                                id: "sidebarClassificationManualLoadHatcheringReport"
                                                ),
                                            new SidenavItem(
                                                title: localizer[Lang.BtnFertilityReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Create", "FertilityReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                    Roles.BackofficeClassificationReportAdministrator),
                                                id: "sidebarClassificationManualLoadFertilityReport"
                                                ),
                                            new SidenavItem(
                                                title: localizer[Lang.BtnEggQualityReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Create", "EggQualityReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                    Roles.BackofficeClassificationReportAdministrator),
                                                id: "sidebarClassificationManualLoadEggQualityReport"
                                                ),
                                        },
                                            id: "sidebarClassificationManualLoadReports"
                                        ),
                                        new SidenavItem(
                                        title: localizer[Lang.BtnMaterialsClassifiedEggs],
                                        icon: "icon-Office",
                                        link: $"/Material/{MaterialTypes.InsumoMateriaPrimaHuevosHuevoClasificado}?area={AreaEnum.Classification}",
                                        visible: operationContext.UserIsInAnyRole(
                                            Roles.BackofficeSuperAdministrator,
                                            Roles.BackofficeClassificationAdministrator,
                                            Roles.BackofficeClassificationStockAdministrator,
                                            Roles.BackofficeClassificationStockUser),
                                        id: "sidebarClassificationClassifiedEggsMaterials"
                                        ),
                                        new SidenavItem(
                                        title: localizer[Lang.BtnRawMaterialExitReport],
                                        icon: "icon-Office",
                                        link: Url.Action("Index", "MaterialExitReport", new { area = AreaEnum.Classification }),
                                        visible: operationContext.UserIsInAnyRole(
                                            Roles.BackofficeSuperAdministrator,
                                            Roles.BackofficeStockUser,
                                            Roles.BackofficeStockAdministrator,
                                            Roles.BackofficeClassificationAdministrator,
                                            Roles.BackofficeClassificationStockAdministrator,
                                            Roles.BackofficeClassificationStockUser
                                            ),
                                        id: "sidebarClassificationBtnRawMaterialExit"
                                        )
                                   },
                                   id: "sidebarClassification"
                                )
                             )
                            )

                        }
                        else
                        {

                            @(await Html.RenderComponentAsync<SidenavItem>(
                                   RenderMode.Static,
                                   new SidenavItem(
                                   title: localizer[Lang.BtnClassificationWarehouse],
                                   icon: "classification",
                                   "#",
                                   visible: operationContext.UserIsInAnyRole(
                                       Roles.BackofficeSuperAdministrator,
                                       Roles.BackofficeClassificationHappeningAdministrator,
                                       Roles.BackofficeClassificationHappeningUser,
                                       Roles.BackofficeClassificationInconsistencyAdministrator,
                                       Roles.BackofficeClassificationInconsistencyUser,
                                       Roles.BackofficeClassificationMaterialAnalysisReportAdministrator,
                                       Roles.BackofficeClassificationMaterialAnalysisReportUser,
                                       Roles.BackofficeClassificationShippingNoteAdministrator,
                                       Roles.BackofficeClassificationShippingNoteUser,
                                       Roles.BackofficeClassificationStockAdministrator,
                                       Roles.BackofficeClassificationStockUser,
                                       Roles.BackofficeHappeningAdministrator,
                                       Roles.BackofficeHappeningUser,
                                       Roles.BackofficeShippingNoteAdministrator,
                                       Roles.BackofficeShippingNoteUser,
                                       Roles.BackofficeMovementReportAdministrator,
                                       Roles.BackofficeMovementReportUser,
                                       Roles.BackofficeStockAdministrator,
                                       Roles.BackofficeInconsistencyUser,
                                       Roles.BackofficeInconsistencyAdministrator,
                                       Roles.BackofficeMaterialAnalysisReportAdministrator,
                                       Roles.BackofficeMaterialAnalysisReportUser,
                                       Roles.BackofficeClassificationHappeningUser,
                                       Roles.BackofficeClassificationShippingNoteUser,
                                       Roles.BackofficeClassificationStockUser,
                                       Roles.BackofficeClassificationStockAdministrator,
                                       Roles.BackofficeClassificationShippingNoteAdministrator,
                                       Roles.BackofficeClassificationHappeningAdministrator,
                                       Roles.BackofficeClassificationInconsistencyAdministrator,
                                       Roles.BackofficeClassificationInconsistencyUser,
                                       Roles.BackofficeClassificationMaterialAnalysisReportAdministrator,
                                       Roles.BackofficeClassificationMaterialAnalysisReportUser,
                                       Roles.BackofficeClassificationAdministrator,
                                       Roles.BackofficeClassificationUser,
                                       Roles.BackofficeClassificationReportAdministrator,
                                       Roles.BackofficeClassificationReportUser,
                                       Roles.BackofficeStockUser,
                                       Roles.BackofficeClassificationReportWithoutDateValidation,
                                       Roles.BackofficeAgentUser),
                                    new List<SidenavItem>()
                                    {
                        new SidenavItem(
                            title: localizer[Lang.BtnClassificationDashboard],
                            icon: "icon-Office",
                            link: Url.Action("Dashboard", "ClassificationDashboard"),
                            visible: false,
                            id: "sidebarClassificationDashboard"
                        ),
                        new SidenavItem(
                            title: localizer[Lang.BtnMessages],
                            icon: "icon-Office",
                            link: Url.Action("Index", "Message", new { area = AreaEnum.Classification }),
                            visible: true,
                            id: "sidebarMessageClassification"
                            ),
                        new SidenavItem(
                            title: localizer[Lang.BtnDailyReport],
                            icon: "icon-Office",
                            link: Url.Action("DailyReport", "ClassificationReport"),
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator),
                            id: "sidebarClassificationDailyReport"
                        ),
                        new SidenavItem(
                            title: localizer[Lang.BtnRawMaterialReception],
                            icon: "icon-Office",
                            link: Url.Action("Index", "MaterialReceptionReport", new { area = AreaEnum.Classification }),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeStockUser,
                                Roles.BackofficeStockAdministrator,
                                Roles.BackofficeClassificationAdministrator,
                                Roles.BackofficeClassificationStockAdministrator,
                                Roles.BackofficeClassificationStockUser
                                ),
                            id: "sidebarClassificationBtnRawMaterialReception"
                            ),
                         new SidenavItem(
                            title: localizer[Lang.BtnRawMaterialExitReport],
                            icon: "icon-Office",
                            link: Url.Action("Index", "MaterialExitReport", new { area = AreaEnum.Classification }),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeStockUser,
                                Roles.BackofficeStockAdministrator,
                                Roles.BackofficeClassificationAdministrator,
                                Roles.BackofficeClassificationStockAdministrator,
                                Roles.BackofficeClassificationStockUser
                                ),
                            id: "sidebarClassificationBtnRawMaterialExit"
                            ),
                        new SidenavItem(
                            title: localizer[Lang.BtnProductClassification],
                            icon: "icon-Office",
                            link: Url.Action("Index", "ClassificationReport"),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeClassificationAdministrator,
                                Roles.BackofficeClassificationUser,
                                Roles.BackofficeClassificationReportAdministrator,
                                Roles.BackofficeClassificationReportUser,
                                Roles.BackofficeClassificationReportWithoutDateValidation),
                        id: "sidebarClassificationReportIndex"
                       ),
                        new SidenavItem(
                            title: localizer[Lang.BtnOtherReports],
                            icon: "icon-Office",
                            link: "#",
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                Roles.BackofficeClassificationReportAdministrator,
                                Roles.BackofficeClassificationReportUser),
                            new List<SidenavItem>(){
                                new SidenavItem(
                                        title: localizer[Lang.BtnHatcheringReport],
                                        icon: "icon-Office",
                                        link: Url.Action("Index", "HatcheringReport"),
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                        Roles.BackofficeClassificationReportAdministrator,
                                        Roles.BackofficeClassificationReportUser),
                                        id: "sidebarClassificationHatcheringReport"
                                    ),
                                new SidenavItem(
                                        title: localizer[Lang.BtnFertilityReport],
                                        icon: "icon-Office",
                                        link: Url.Action("Index", "FertilityReport"),
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                        Roles.BackofficeClassificationReportAdministrator,
                                        Roles.BackofficeClassificationReportUser),
                                        id: "sidebarClassificationFertilityReport"
                                    ),
                                new SidenavItem(
                                        title: localizer[Lang.BtnEggQualityReport],
                                        icon: "icon-Office",
                                        link: Url.Action("Index", "EggQualityReport"),
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                        Roles.BackofficeClassificationReportAdministrator,
                                        Roles.BackofficeClassificationReportUser),
                                        id: "sidebarClassificationEggQualityReport"
                                    ),
                                new SidenavItem(
                                        title: localizer[Lang.BtnEggDensityReport],
                                        icon: "icon-Office",
                                        link: Url.Action("Index", "EggDensityReport"),
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                        Roles.BackofficeClassificationReportAdministrator,
                                        Roles.BackofficeClassificationReportUser, Roles.BackofficeClassificationUser),
                                        id: "sidebarClassificationEggDensityReport"
                                        ),
                            },
                            id: "sidebarClassificationReports"
                        ),
                            new SidenavItem(
                                        title: localizer[Lang.BtnManualLoading],
                                        icon: "icon-Office",
                                        link: "#",
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                            Roles.BackofficeClassificationAdministrator,
                                            Roles.BackofficeClassificationReportAdministrator),
                                        new List<SidenavItem>(){
                                            new SidenavItem(
                                                title: localizer[Lang.BtnHatcheringReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Create", "HatcheringReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                    Roles.BackofficeClassificationReportAdministrator),
                                                id: "sidebarClassificationManualLoadHatcheringReport"
                                                ),
                                            new SidenavItem(
                                                title: localizer[Lang.BtnFertilityReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Create", "FertilityReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                    Roles.BackofficeClassificationReportAdministrator),
                                                id: "sidebarClassificationManualLoadFertilityReport"
                                                ),
                                            new SidenavItem(
                                                title: localizer[Lang.BtnEggQualityReport],
                                                icon: "icon-Office",
                                                link: Url.Action("Create", "EggQualityReport"),
                                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator,
                                                    Roles.BackofficeClassificationReportAdministrator),
                                                id: "sidebarClassificationManualLoadEggQualityReport"
                                                ),
                                        },
                                            id: "sidebarClassificationManualLoadReports"
                                        ),

                        new SidenavItem(
                            title: localizer[Lang.BtnMaterials],
                            icon: "icon-Office",
                            link: "#",
                            visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator, Roles.BackofficeClassificationStockAdministrator, Roles.BackofficeClassificationStockUser),
                            new List<SidenavItem>(){
                                    new SidenavItem(
                                        title: localizer[Lang.BtnMaterialsClassifiedEggs],
                                        icon: "icon-Office",
                                        link: $"/Material/{MaterialTypes.InsumoMateriaPrimaHuevosHuevoClasificado}?area={AreaEnum.Classification}",
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator, Roles.BackofficeClassificationStockAdministrator, Roles.BackofficeClassificationStockUser),
                                        id: "sidebarClassificationClassifiedEggsMaterials"
                                    ),

                                    new SidenavItem(
                                        title: localizer[Lang.BtnMaterialsPackaging],
                                        icon: "icon-Office",
                                        link: $"/Material/{MaterialTypes.InsumoPackaging}?area={AreaEnum.Classification}",
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator, Roles.BackofficeClassificationStockAdministrator, Roles.BackofficeClassificationStockUser),
                                        id: "sidebarClassificationPackagingMaterials"
                                    ),

                                    new SidenavItem(
                                        title: localizer[Lang.BtnMaterialsLogisticsAssets],
                                        icon: "icon-Office",
                                        link: $"/Material/{MaterialTypes.ActivoLogistico}?area={AreaEnum.Classification}",
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator, Roles.BackofficeClassificationStockAdministrator, Roles.BackofficeClassificationStockUser),
                                        id: "sidebarClassificationLogisticsAssets"
                                    ),

                                    new SidenavItem(
                                        title: localizer[Lang.BtnMaterialsSKUs],
                                        icon: "icon-Office",
                                        link: $"/SKU",
                                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator, Roles.BackofficeClassificationStockAdministrator, Roles.BackofficeClassificationStockUser),
                                        id: "sidebarClassificationSKUs"
                                    ),
                            },
                            id: "sidebarClassificationMaterials"
                       ),

                       new SidenavItem(
                       title: localizer[Lang.BtnStocks],
                       icon: "icon-Office",
                       link: Url.Action("Index", "Stock", new { area = AreaEnum.Classification }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeClassificationAdministrator,
                           Roles.BackofficeStockAdministrator,
                           Roles.BackofficeStockUser,
                           Roles.BackofficeClassificationStockAdministrator,
                           Roles.BackofficeClassificationStockUser),
                       id: "sidebarClassificationStock"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnShippingNotes],
                       icon: "icon-Office",
                       link: Url.Action("Index", "ShippingNote", new { area = AreaEnum.Classification }),
                       visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeClassificationAdministrator, Roles.BackofficeClassificationShippingNoteAdministrator, Roles.BackofficeClassificationShippingNoteUser),
                       id: "sidebarClassificationMovement"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnHappening],
                       icon: "icon-Office",
                       link: Url.Action("Index", "Happening", new { area = AreaEnum.Classification }),
                       visible: operationContext.UserIsInAnyRole(
                               Roles.BackofficeSuperAdministrator,
                               Roles.BackofficeClassificationAdministrator,
                               Roles.BackofficeClassificationHappeningAdministrator,
                               Roles.BackofficeClassificationHappeningUser,
                               Roles.BackofficeHappeningAdministrator),
                       id: "sidebarClassificationHappening"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnSampleMaterial],
                       icon: "icon-Office",
                       link: Url.Action("Index", "MaterialAnalysisReport", new { area = AreaEnum.Classification }),
                       visible: operationContext.UserIsInAnyRole(
                               Roles.BackofficeSuperAdministrator,
                               Roles.BackofficeClassificationAdministrator,
                               Roles.BackofficeClassificationMaterialAnalysisReportAdministrator,
                               Roles.BackofficeClassificationMaterialAnalysisReportUser,
                               Roles.BackofficeMaterialAnalysisReportAdministrator,
                               Roles.BackofficeMaterialAnalysisReportUser),
                       id: "sidebarClassificationMaterialAnalysisReport"
                       ),
                       new SidenavItem(
                            title: localizer[Lang.BtnInconsistencyReport],
                            icon: "icon-Office",
                            link: Url.Action("Index", "InconsistencyReport", new { area = AreaEnum.Classification }),
                            visible: operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeClassificationAdministrator,
                                Roles.BackofficeInconsistencyAdministrator,
                                Roles.BackofficeInconsistencyUser,
                                Roles.BackofficeClassificationInconsistencyAdministrator,
                                Roles.BackofficeClassificationInconsistencyUser),
                            id: "sidebarClassificatioInconsistencyReport"
                            ),
                            },
                                   id: "sidebarClassification"
                         )
                     )
                    )}}

                    @(await Html.RenderComponentAsync<SidenavItem>(
                       RenderMode.Static,
                       new SidenavItem(
                       title: localizer[Lang.BtnHealth],
                       icon: "health",
                       "#",
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator, 
                           Roles.BackofficeHealthCareAdministrator,
                       Roles.BackofficeHealthCareTaskAdministrator, 
                       Roles.BackofficeStockAdministrator, 
                       Roles.BackofficeHealthCareTaskUser,
                       Roles.BackofficeHealthCareBestPracticeAdministrator,
                       Roles.BackofficeHealthCareBestPracticeUser,
                       Roles.BackofficeHealthCareReportAdministrator,
                       Roles.BackofficeHealthCareReportUser,
                       Roles.BackofficeStockUser,
                       Roles.BackofficeHealthCareStockAdministrator,
                       Roles.BackofficeHealthCareStockUser),
                       new List<SidenavItem>()
                       {
                       new SidenavItem(
                       title: localizer[Lang.BtnDashboard],
                       icon: "icon-Office",
                       link: canAccessToSerology ? Url.Action("Dashboard", "Serology") : Url.Action("Manager", "Task", new { area = AreaEnum.HealthCare }),
                       visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeHealthCareAdministrator,
                       Roles.BackofficeHealthCareTaskUser, Roles.BackofficeHealthCareTaskAdministrator)
                        ),
                       new SidenavItem(
                            title: localizer[Lang.BtnMessages],
                            icon: "icon-Office",
                            link: Url.Action("Index", "Message", new { area = AreaEnum.HealthCare }),
                            visible: true,
                            id: "sidebarMessageHealthCare"
                            ),
                       new SidenavItem(
                       title: localizer[Lang.BtnHealthCareReports],
                       icon: "icon-Office",
                       link: Url.Action("Index", "HealthCareReport"),
                       visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeHealthCareReportAdministrator,
                       Roles.BackofficeHealthCareReportUser, Roles.BackofficeHealthCareAdministrator)
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnBestPractices],
                       icon: "icon-Office",
                       link: Url.Action("Index", "BestPractice", new {area = AreaEnum.HealthCare}),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeHealthCareBestPracticeUser,
                           Roles.BackofficeHealthCareBestPracticeAdministrator,
                           Roles.BackofficeHealthCareAdministrator)
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnHealthCareSupplies],
                       icon: "icon-Office",
                       link: $"/Material/{MaterialTypes.InsumoSanidad}?area={AreaEnum.HealthCare}",
                       visible: operationContext.UserIsInAnyRole(
                          Roles.BackofficeSuperAdministrator,
                          Roles.BackofficeHealthCareBestPracticeUser,
                          Roles.BackofficeHealthCareAdministrator)
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnStocks],
                       icon: "icon-Office",
                       link: Url.Action("Index", "Stock", new { area = AreaEnum.HealthCare }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeHealthCareAdministrator,
                           Roles.BackofficeStockAdministrator,
                           Roles.BackofficeStockUser,
                           Roles.BackofficeHealthCareStockAdministrator,
                           Roles.BackofficeHealthCareStockUser),
                       id: "sidebarHealthCareStock"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnShippingNotes],
                       icon: "icon-Office",
                       link: Url.Action("Index", "ShippingNote", new { area = AreaEnum.HealthCare }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeHealthCareAdministrator,
                           Roles.BackofficeShippingNoteAdministrator,
                           Roles.BackofficeShippingNoteUser,
                           Roles.BackofficeHealthCareShippingNoteAdministrator,
                           Roles.BackofficeHealthCareShippingNoteUser),
                       id: "sidebarHealthCareMovement"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnHappening],
                       icon: "icon-Office",
                       link: Url.Action("Index", "Happening", new { area = AreaEnum.HealthCare }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeHealthCareAdministrator,
                           Roles.BackofficeHappeningAdministrator,
                           Roles.BackofficeHappeningUser,
                           Roles.BackofficeHealthCareHappeningAdministrator,
                           Roles.BackofficeHealthCareHappeningUser),
                       id: "sidebarHealthHappening"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnSampleMaterial],
                       icon: "icon-Office",
                       link: Url.Action("Index", "MaterialAnalysisReport", new { area = AreaEnum.HealthCare }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeHealthCareAdministrator,
                           Roles.BackofficeMaterialAnalysisReportAdministrator,
                           Roles.BackofficeMaterialAnalysisReportUser,
                           Roles.BackofficeHealthCareMaterialAnalysisReportAdministrator,
                           Roles.BackofficeHealthCareMaterialAnalysisReportUser),
                       id: "sidebarHealthCareMaterialAnalysisReport"
                       ),
                       new SidenavItem(
                       title: localizer[Lang.BtnInconsistencyReport],
                       icon: "icon-Office",
                       link: Url.Action("Index", "InconsistencyReport", new { area = AreaEnum.HealthCare }),
                       visible: operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeHealthCareAdministrator,
                           Roles.BackofficeInconsistencyAdministrator,
                           Roles.BackofficeInconsistencyUser,
                           Roles.BackofficeHealthCareInconsistencyAdministrator,
                           Roles.BackofficeHealthCareInconsistencyUser),
                       id: "sidebarHealthCareInconsistencyReport"
                       ),
                      new SidenavItem(
                      title: localizer[Lang.BtnSerologyReport],
                      icon: "icon-Office",
                      link: Url.Action("Index", "SerologyAnalysisReport"),
                      visible: canAccessToSerology && operationContext.UserIsInAnyRole(
                        Roles.BackofficeSuperAdministrator,
                        Roles.BackofficeHealthCareAdministrator),
                      id: "sidebarHealthCareSerologyReport"
                      )
                },
                       id: "sidebarHealthcare"
             )
         )
                    )


                </ul>
                <ul class="sidebarnav bottom">
                    @(await Html.RenderComponentAsync<SidenavItem>(
                    RenderMode.Static,
                    new SidenavItem(
                        localizer[Lang.BtnSettings],
                        "settings",
                        "#",
                        visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                                                  Roles.BackofficeSettingsAdministrator,
                                                                  Roles.BackofficeSystemAdministrator,
                                                                  Roles.BackofficeTenantAdministrator,
                                                                  Roles.BackofficeBackofficeUserAdministrator,
                                                                  Roles.BackofficeGeneticAdministrator,
                                                                  Roles.BackofficeCompanyAdministrator,
                                                                  Roles.BackofficeFarmAdministrator,
                                                                  Roles.BackofficeFarmUser,
                                                                  Roles.BackofficeFeedFactoryMaterialAdministrator
                                                                  ),
                        new List<SidenavItem>()
                        {
                            new SidenavItem(
                                title: localizer[Lang.BtnSystem],
                                icon: "icon-Office",
                                link: Url.Action("System", "Settings"),
                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                                                  Roles.BackofficeSettingsAdministrator,
                                                                  Roles.BackofficeSystemAdministrator,
                                                                  Roles.BackofficeTenantAdministrator,
                                                                  Roles.BackofficeGeneticAdministrator),
                                id:"sidebarSystem"
                            ),
                            new SidenavItem(
                                title: localizer[Lang.BtnCompany],
                                icon: "icon-Office",
                                link: Url.Action("Company", "Settings"),
                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                                                  Roles.BackofficeSettingsAdministrator,
                                                                  Roles.BackofficeCompanyAdministrator,
                                                                  Roles.BackofficeFeedFactoryMaterialAdministrator),
                                id: "sidebarCompany"
                            ),
                            new SidenavItem(
                                title: localizer[Lang.BtnTenant],
                                icon: "icon-Office",
                                link: Url.Action("Tenants", "Settings"),
                                visible: operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
                                                                  Roles.BackofficeFarmAdministrator,
                                                                  Roles.BackofficeFarmUser,
                                                                  Roles.BackofficeBackofficeUserAdministrator,
                                                                  Roles.BackofficeTenantAdministrator),
                                id: "sidebarTenants"
                            )

                        },
                        id: "sidebarSettings"
                    )))
                </ul>
            }
        </nav>
    </div>
</aside>