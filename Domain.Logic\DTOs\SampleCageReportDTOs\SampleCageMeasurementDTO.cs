﻿using System;
using System.ComponentModel.DataAnnotations;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.DTOs.SampleCageReportDTOs.SampleCageMeasurementDTO;

namespace Domain.Logic.DTOs.SampleCageReportDTOs
{
    public class SampleCageMeasurementDTO
    {
        public string Name { get; set; }

        public Guid? FemaleBirdWeightMeasureId { get; set; }

        public Guid? MaleBirdWeightMeasureId { get; set; }

        public Guid SampleCageId { get; set; }

        [Range(0, 10000, ErrorMessage = Lang.AvgEggWeightRangeError)]
        public decimal? AvgEggWeight { get; set; }

        [Required(ErrorMessage = Lang.AvgFemaleBirdWeightRequired)]
        [Range(0, 10000, ErrorMessage = Lang.AvgFemaleBirdWeightRangeError)]
        public decimal AvgFemaleBirdWeight { get; set; }

        [Required(ErrorMessage = Lang.AvgMaleBirdWeightRequired)]
        [Range(0, 10000, ErrorMessage = Lang.AvgMaleBirdWeightRangeError)]
        public decimal AvgMaleBirdWeight { get; set; }

        [Range(0, 100, ErrorMessage = Lang.VariationCoefficientFemaleRangeError)]
        public decimal? VariationCoefficientFemale { get; set; }

        [Range(0, 100, ErrorMessage = Lang.VariationCoefficientMaleRangeError)]
        public decimal? VariationCoefficientMale { get; set; }

        [Range(0, 100, ErrorMessage = Lang.UniformityFemaleRangeError)]
        public decimal? UniformityFemale { get; set; }

        [Range(0, 100, ErrorMessage = Lang.UniformityMaleRangeError)]
        public decimal? UniformityMale { get; set; }

        // warehouse report properties 
        public Guid HenBatchId { get; set; }

        public Guid? HenBatchPerformanceId { get; set; }

        public bool HasFemaleHen { get; set; }

        public bool HasMaleHen { get; set; }

        public SampleCageMeasurementDTO() { }
    }
}
